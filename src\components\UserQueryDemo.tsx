// 用户查询演示组件

import React, { useState } from 'react';

// 本地类型定义
interface UserInfo {
  id: number;
  name: string;
  properties: Record<string, any>;
}

interface SimilarUser {
  id: number;
  score: number;
  properties?: Record<string, any>;
}

interface PathResult {
  path: number[];
  depth: number;
  edges: number[];
}

interface UserQueryDemoProps {
  className?: string;
}

const UserQueryDemo: React.FC<UserQueryDemoProps> = ({ className = '' }) => {
  const [graphName, setGraphName] = useState<string>('social_network');
  const [userVertexType, setUserVertexType] = useState<string>('User');
  const [relationEdgeType, setRelationEdgeType] = useState<string>('Friend');
  
  // 查询状态
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string>('');
  
  // 用户名查询
  const [username, setUsername] = useState<string>('');
  const [foundUsers, setFoundUsers] = useState<UserInfo[]>([]);
  
  // 路径查询
  const [startUserId, setStartUserId] = useState<string>('');
  const [targetUserId, setTargetUserId] = useState<string>('');
  const [maxDepth, setMaxDepth] = useState<number>(3);
  const [pathResults, setPathResults] = useState<PathResult[]>([]);
  
  // 相似用户推荐
  const [recommendUserId, setRecommendUserId] = useState<string>('');
  const [topN, setTopN] = useState<number>(10);
  const [similarUsers, setSimilarUsers] = useState<SimilarUser[]>([]);

  // 模拟查询服务方法
  const simulateQuery = async (queryType: string, params: any) => {
    // 这里暂时返回模拟数据，等 UserQueryService 修复后再替换
    console.log(`模拟查询: ${queryType}`, params);

    if (queryType === 'userByName') {
      return [{ id: 1, name: params.username, properties: { age: 25 } }];
    } else if (queryType === 'path') {
      return [{ path: [params.startId, params.targetId || params.startId + 1], depth: 1, edges: [] }];
    } else if (queryType === 'similar') {
      return [{ id: 2, score: 0.85, properties: { name: 'Similar User' } }];
    }

    return [];
  };

  // 用户名查询
  const handleUserNameQuery = async () => {
    if (!username.trim()) {
      setError('请输入用户名');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const users = await simulateQuery('userByName', { username }) as UserInfo[];
      setFoundUsers(users);

      if (users.length === 0) {
        setError(`未找到用户: ${username}`);
      }
    } catch (err) {
      setError(`查询失败: ${err}`);
    } finally {
      setLoading(false);
    }
  };

  // 路径查询
  const handlePathQuery = async () => {
    if (!startUserId.trim()) {
      setError('请输入起始用户ID');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const startId = parseInt(startUserId);
      const targetId = targetUserId.trim() ? parseInt(targetUserId) : undefined;

      const paths = await simulateQuery('path', { startId, targetId, maxDepth }) as PathResult[];
      setPathResults(paths);

      if (paths.length === 0) {
        setError('未找到路径');
      }
    } catch (err) {
      setError(`路径查询失败: ${err}`);
    } finally {
      setLoading(false);
    }
  };

  // 相似用户推荐
  const handleSimilarUserQuery = async () => {
    if (!recommendUserId.trim()) {
      setError('请输入用户ID');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const userId = parseInt(recommendUserId);

      const recommendations = await simulateQuery('similar', { userId, topN }) as SimilarUser[];
      setSimilarUsers(recommendations);

      if (recommendations.length === 0) {
        setError('未找到相似用户');
      }
    } catch (err) {
      setError(`推荐查询失败: ${err}`);
    } finally {
      setLoading(false);
    }
  };

  // 复合查询：用户名 + 推荐
  const handleComplexQuery = async () => {
    if (!username.trim()) {
      setError('请输入用户名');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const users = await simulateQuery('userByName', { username }) as UserInfo[];
      const recommendations = await simulateQuery('similar', { userId: users[0]?.id, topN }) as SimilarUser[];

      setFoundUsers(users);
      setSimilarUsers(recommendations);
      if (users.length > 0) {
        setRecommendUserId(users[0].id.toString());
      }
    } catch (err) {
      setError(`复合查询失败: ${err}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={`p-6 bg-primary-dark rounded-lg shadow-lg border border-gray-800 ${className}`}>
      <h2 className="text-spec-header font-bold mb-6 text-text-primary font-arial">用户查询演示</h2>

      {/* 配置区域 */}
      <div className="mb-6 p-4 bg-gray-900 rounded border border-gray-700">
        <h3 className="text-spec-body font-semibold mb-3 text-text-primary font-arial">图配置</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-text-secondary text-spec-regular font-arial mb-1">图名称</label>
            <input
              type="text"
              value={graphName}
              onChange={(e) => setGraphName(e.target.value)}
              className="w-full p-2 bg-gray-800 border border-gray-600 rounded text-text-primary font-arial focus:border-primary-blue focus:outline-none"
            />
          </div>
          <div>
            <label className="block text-text-secondary text-spec-regular font-arial mb-1">用户顶点类型</label>
            <input
              type="text"
              value={userVertexType}
              onChange={(e) => setUserVertexType(e.target.value)}
              className="w-full p-2 bg-gray-800 border border-gray-600 rounded text-text-primary font-arial focus:border-primary-blue focus:outline-none"
            />
          </div>
          <div>
            <label className="block text-text-secondary text-spec-regular font-arial mb-1">关系边类型</label>
            <input
              type="text"
              value={relationEdgeType}
              onChange={(e) => setRelationEdgeType(e.target.value)}
              className="w-full p-2 bg-gray-800 border border-gray-600 rounded text-text-primary font-arial focus:border-primary-blue focus:outline-none"
            />
          </div>
        </div>
      </div>

      {/* 错误信息 */}
      {error && (
        <div className="mb-4 p-3 bg-primary-orange/10 border border-primary-orange/30 rounded text-primary-orange font-arial">
          {error}
        </div>
      )}

      {/* 加载状态 */}
      {loading && (
        <div className="mb-4 p-3 bg-primary-blue/10 border border-primary-blue/30 rounded text-primary-blue font-arial">
          查询中...
        </div>
      )}

      {/* 查询区域 */}
      <div className="space-y-6">
        {/* 1. 用户名查询 */}
        <div className="p-4 bg-gray-900 rounded border border-gray-700">
          <h3 className="text-spec-body font-semibold mb-3 text-text-primary font-arial">1. 用户名查询</h3>
          <div className="flex gap-4 mb-4">
            <input
              type="text"
              placeholder="输入用户名"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              className="flex-1 p-2 bg-gray-800 border border-gray-600 rounded text-text-primary font-arial focus:border-primary-blue focus:outline-none"
            />
            <button
              onClick={handleUserNameQuery}
              disabled={loading}
              className="px-4 py-2 bg-primary-blue text-text-primary rounded hover:bg-primary-blue/80 transition-colors duration-200 font-arial font-semibold disabled:opacity-50"
            >
              查询用户
            </button>
            <button
              onClick={handleComplexQuery}
              disabled={loading}
              className="px-4 py-2 bg-primary-yellow text-primary-dark rounded hover:bg-primary-yellow/80 transition-colors duration-200 font-arial font-semibold disabled:opacity-50"
            >
              查询+推荐
            </button>
          </div>
          
          {foundUsers.length > 0 && (
            <div>
              <h4 className="text-spec-regular font-semibold mb-2 text-text-primary font-arial">找到的用户:</h4>
              <div className="space-y-2">
                {foundUsers.map((user) => (
                  <div key={user.id} className="p-2 bg-primary-yellow/10 border border-primary-yellow/30 rounded">
                    <span className="text-primary-yellow font-arial">ID: {user.id}</span>
                    <span className="text-text-primary font-arial ml-4">姓名: {user.name}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* 2. 路径查询 */}
        <div className="p-4 bg-gray-900 rounded border border-gray-700">
          <h3 className="text-spec-body font-semibold mb-3 text-text-primary font-arial">2. 最大深度路径查询</h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
            <input
              type="number"
              placeholder="起始用户ID"
              value={startUserId}
              onChange={(e) => setStartUserId(e.target.value)}
              className="p-2 bg-gray-800 border border-gray-600 rounded text-text-primary font-arial focus:border-primary-blue focus:outline-none"
            />
            <input
              type="number"
              placeholder="目标用户ID (可选)"
              value={targetUserId}
              onChange={(e) => setTargetUserId(e.target.value)}
              className="p-2 bg-gray-800 border border-gray-600 rounded text-text-primary font-arial focus:border-primary-blue focus:outline-none"
            />
            <input
              type="number"
              placeholder="最大深度"
              value={maxDepth}
              onChange={(e) => setMaxDepth(parseInt(e.target.value) || 3)}
              className="p-2 bg-gray-800 border border-gray-600 rounded text-text-primary font-arial focus:border-primary-blue focus:outline-none"
            />
            <button
              onClick={handlePathQuery}
              disabled={loading}
              className="px-4 py-2 bg-primary-blue text-text-primary rounded hover:bg-primary-blue/80 transition-colors duration-200 font-arial font-semibold disabled:opacity-50"
            >
              查询路径
            </button>
          </div>
          
          {pathResults.length > 0 && (
            <div>
              <h4 className="text-spec-regular font-semibold mb-2 text-text-primary font-arial">路径结果:</h4>
              <div className="space-y-2">
                {pathResults.map((path, index) => (
                  <div key={index} className="p-2 bg-primary-orange/10 border border-primary-orange/30 rounded">
                    <span className="text-primary-orange font-arial">深度: {path.depth}</span>
                    <span className="text-text-primary font-arial ml-4">路径: {path.path.join(' → ')}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* 3. 相似用户推荐 */}
        <div className="p-4 bg-gray-900 rounded border border-gray-700">
          <h3 className="text-spec-body font-semibold mb-3 text-text-primary font-arial">3. 相似用户推荐</h3>
          <div className="flex gap-4 mb-4">
            <input
              type="number"
              placeholder="用户ID"
              value={recommendUserId}
              onChange={(e) => setRecommendUserId(e.target.value)}
              className="flex-1 p-2 bg-gray-800 border border-gray-600 rounded text-text-primary font-arial focus:border-primary-blue focus:outline-none"
            />
            <input
              type="number"
              placeholder="推荐数量"
              value={topN}
              onChange={(e) => setTopN(parseInt(e.target.value) || 10)}
              className="w-32 p-2 bg-gray-800 border border-gray-600 rounded text-text-primary font-arial focus:border-primary-blue focus:outline-none"
            />
            <button
              onClick={handleSimilarUserQuery}
              disabled={loading}
              className="px-4 py-2 bg-primary-blue text-text-primary rounded hover:bg-primary-blue/80 transition-colors duration-200 font-arial font-semibold disabled:opacity-50"
            >
              推荐用户
            </button>
          </div>
          
          {similarUsers.length > 0 && (
            <div>
              <h4 className="text-spec-regular font-semibold mb-2 text-text-primary font-arial">推荐结果:</h4>
              <div className="space-y-2">
                {similarUsers.map((user) => (
                  <div key={user.id} className="p-2 bg-primary-yellow/10 border border-primary-yellow/30 rounded">
                    <span className="text-primary-yellow font-arial">ID: {user.id}</span>
                    <span className="text-text-primary font-arial ml-4">相似度: {user.score.toFixed(4)}</span>
                    {user.properties?.name && (
                      <span className="text-text-secondary font-arial ml-4">姓名: {user.properties.name}</span>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default UserQueryDemo;
