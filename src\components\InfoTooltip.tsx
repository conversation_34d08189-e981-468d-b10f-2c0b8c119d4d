// 信息浮窗组件

import React from 'react';

interface TooltipProps {
  x: number;
  y: number;
  visible: boolean;
  title: string;
  content: Record<string, any>;
  type: 'node' | 'edge';
}

const InfoTooltip: React.FC<TooltipProps> = ({ x, y, visible, title, content, type }) => {
  if (!visible) return null;

  const getTypeColor = () => {
    return type === 'node' ? '#1D77FF' : '#FB956B';
  };

  const getTypeIcon = () => {
    return type === 'node' ? '●' : '—';
  };

  return (
    <div
      className="absolute z-50 bg-gray-800 border border-gray-600 rounded-lg shadow-lg p-3 max-w-xs pointer-events-none"
      style={{
        left: x + 10,
        top: y - 10,
        fontFamily: 'PingFang SC',
      }}
    >
      {/* 标题 */}
      <div className="flex items-center gap-2 mb-2">
        <span 
          className="text-lg font-bold"
          style={{ color: getTypeColor() }}
        >
          {getTypeIcon()}
        </span>
        <span className="text-white font-semibold text-sm">
          {title}
        </span>
        <span className="text-gray-400 text-xs">
          ({type === 'node' ? '节点' : '关系'})
        </span>
      </div>

      {/* 内容 */}
      <div className="space-y-1">
        {Object.entries(content).map(([key, value]) => (
          <div key={key} className="flex justify-between text-xs">
            <span className="text-gray-300">{key}:</span>
            <span className="text-white ml-2 truncate">
              {typeof value === 'object' ? JSON.stringify(value) : String(value)}
            </span>
          </div>
        ))}
      </div>

      {/* 小箭头 */}
      <div 
        className="absolute w-2 h-2 bg-gray-800 border-l border-b border-gray-600 transform rotate-45"
        style={{
          left: -4,
          top: 16,
        }}
      />
    </div>
  );
};

export default InfoTooltip;
