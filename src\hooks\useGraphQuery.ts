// 图查询 Hook

import { useState, useCallback, useRef, useEffect } from 'react';
import neo4jData from '../assets/neo4j_query_table_data_2025-7-30.json';

// Neo4j 数据类型定义
interface Neo4jNode {
  id: number;
  dataset: string;
  features: string[];
  label: string;
  type: string;
}

interface Neo4jEdge {
  source: number;
  dataset: string;
  target: number;
  type: string;
}

interface Neo4jRecord {
  graph_data: {
    nodes: Neo4jNode[];
    edges: Neo4jEdge[];
  };
}

interface SigmaNode {
  key: string;
  label: string;
  tag: string;
  dataset: string;
  features: string[];
  x: number;
  y: number;
  size: number;
  color: string;
}

interface SigmaEdge {
  key: string;
  source: string;
  target: string;
  label: string;
  type: string;
  dataset: string;
  color: string;
  size: number;
}

// 图节点接口
export interface GraphNode {
  id: string;
  label: string;
  x?: number;
  y?: number;
  size?: number;
  color?: string;
  type?: string;
  features?: string[];  // 添加 features 属性
}

// 图边接口
export interface GraphEdge {
  id: string;
  source: string;
  target: string;
  weight?: number;
  color?: string;
  type?: string;
}

// 查询响应接口
export interface QueryResponse {
  nodes: GraphNode[];
  edges: GraphEdge[];
  statistics: {
    totalNodes: number;
    totalEdges: number;
    maxConnections: number;
    avgConnections: number;
    recommendedUsers: number;
  };
}

interface UseGraphQueryResult {
  // 当前显示的数据
  data: QueryResponse | null;
  // 双数据源
  overviewData: QueryResponse | null;
  queryData: QueryResponse | null;
  // 视图模式
  viewMode: 'overview' | 'query';
  // 状态
  loading: boolean;
  error: string | null;
  // 方法
  executeQuery: (username: string, expandDepth: number, recommendSimilar?: boolean, userId?: number) => Promise<void>;
  clearData: () => void;
  clearError: () => void;
  switchToOverview: () => void;
  initializeOverview: () => void;
}

export const useGraphQuery = (onQueryComplete?: (duration: number) => void): UseGraphQueryResult => {
  // 双数据源状态
  const [overviewData, setOverviewData] = useState<QueryResponse | null>(null);
  const [queryData, setQueryData] = useState<QueryResponse | null>(null);
  const [viewMode, setViewMode] = useState<'overview' | 'query'>('overview');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 当前显示的数据
  const currentData = viewMode === 'query' && queryData ? queryData : overviewData;

  // 使用 useRef 来避免依赖问题
  const onQueryCompleteRef = useRef(onQueryComplete);
  onQueryCompleteRef.current = onQueryComplete;

  const executeQuery = useCallback(async (username: string, expandDepth: number, recommendSimilar: boolean = false, userId?: number) => {
    if (!username.trim()) {
      setError('请输入用户名');
      return;
    }

    if (expandDepth < 1 || expandDepth > 10) {
      setError('扩大深度必须在1-10之间');
      return;
    }

    setLoading(true);
    setError(null);

    // 开始计时
    const startTime = performance.now();

    try {
      console.log('开始执行查询:', { username, expandDepth, recommendSimilar, userId });

      // 暂时直接使用假数据展示功能
      console.log('使用假数据展示图计算查询功能');

      const result = generateRealisticMockData(username, userId || 0, expandDepth, recommendSimilar);

      console.log('生成的查询数据:', result);
      setQueryData(result);
      setViewMode('query'); // 查询完成后切换到查询视图
      console.log('查询数据已设置，节点数:', result.nodes.length, '边数:', result.edges.length);

      // 计算耗时
      const endTime = performance.now();
      const duration = (endTime - startTime) / 1000; // 转换为秒
      const formattedDuration = parseFloat(duration.toFixed(2)); // 保留2位小数

      console.log(`查询完成，耗时: ${formattedDuration} 秒`);

      // 调用完成回调
      if (onQueryCompleteRef.current) {
        onQueryCompleteRef.current(formattedDuration);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '查询失败';
      console.error('查询错误:', err);
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  // 初始化全貌数据
  const initializeOverview = useCallback(() => {
    console.log('初始化全貌数据');
    const overviewResult = generateOverviewMockData();
    setOverviewData(overviewResult);
    setViewMode('overview');
    console.log('全貌数据已设置，节点数:', overviewResult.nodes.length, '边数:', overviewResult.edges.length);
  }, []);

  // 切换到全貌视图
  const switchToOverview = useCallback(() => {
    console.log('切换到全貌视图');
    setViewMode('overview');
    setError(null);
  }, []);

  // 清除查询数据（相当于取消）
  const clearData = useCallback(() => {
    console.log('清除查询数据，回到全貌');
    setQueryData(null);
    setViewMode('overview');
    setError(null);
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // 组件挂载时初始化全貌数据
  useEffect(() => {
    if (!overviewData) {
      initializeOverview();
    }
  }, [overviewData, initializeOverview]);

  return {
    data: currentData,
    overviewData,
    queryData,
    viewMode,
    loading,
    error,
    executeQuery,
    clearData,
    clearError,
    switchToOverview,
    initializeOverview,
  };
};

// 生成真实感的假数据用于演示
function generateRealisticMockData(username: string, userId: number, expandDepth: number, recommendSimilar: boolean): QueryResponse {
  const nodes: GraphNode[] = [];
  const edges: GraphEdge[] = [];

  // 真实的用户名列表
  const realUserNames = [
    '张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十',
    '郑十一', '王十二', '冯十三', '陈十四', '褚十五', '卫十六', '蒋十七', '沈十八',
    '韩十九', '杨二十', '朱二十一', '秦二十二', '尤二十三', '许二十四', '何二十五', '吕二十六',
    '施二十七', '张二十八', '孔二十九', '曹三十', '严三十一', '华三十二', '金三十三', '魏三十四',
    '陶三十五', '姜三十六', '戚三十七', '谢三十八', '邹三十九', '喻四十', '柏四十一', '水四十二'
  ];

  // 添加主用户节点
  nodes.push({
    id: userId.toString(),
    label: username,
    x: 0,
    y: 0,
    size: 25,
    color: '#1D77FF',
  });

  let nodeIdCounter = userId + 1;
  const allNodeIds = [userId];

  // 根据扩展深度生成连接的用户
  for (let depth = 1; depth <= expandDepth; depth++) {
    const nodeCount = Math.max(3, Math.min(8, 12 - depth * 2)); // 每层3-8个节点
    const currentLayerNodes = [];

    for (let i = 0; i < nodeCount; i++) {
      const nodeId = nodeIdCounter++;
      const userName = realUserNames[Math.floor(Math.random() * realUserNames.length)];
      const angle = (i / nodeCount) * 2 * Math.PI + (Math.random() - 0.5) * 0.5; // 添加一些随机性
      const radius = depth * (80 + Math.random() * 40); // 添加半径变化

      nodes.push({
        id: nodeId.toString(),
        label: userName,
        x: Math.cos(angle) * radius,
        y: Math.sin(angle) * radius,
        size: Math.max(10, 22 - depth * 2),
        color: depth === 1 ? '#FB956B' : depth === 2 ? '#F2BC29' : '#95D5B2',
      });

      currentLayerNodes.push(nodeId);
      allNodeIds.push(nodeId);

      // 连接到上一层的节点
      if (depth === 1) {
        // 第一层直接连接到主用户
        edges.push({
          id: `edge_${userId}_${nodeId}`,
          source: userId.toString(),
          target: nodeId.toString(),
          weight: Math.random() * 0.8 + 0.2,
          color: '#666',
        });
      } else {
        // 后续层连接到前一层的随机节点
        const prevLayerStart = allNodeIds.length - currentLayerNodes.length - nodeCount;
        const prevLayerEnd = allNodeIds.length - currentLayerNodes.length;
        const sourceNodeId = allNodeIds[prevLayerStart + Math.floor(Math.random() * (prevLayerEnd - prevLayerStart))];

        edges.push({
          id: `edge_${sourceNodeId}_${nodeId}`,
          source: sourceNodeId.toString(),
          target: nodeId.toString(),
          weight: Math.random() * 0.6 + 0.2,
          color: '#888',
        });
      }
    }

    // 在同一层的节点之间添加一些连接
    if (currentLayerNodes.length > 2) {
      const connectionsCount = Math.floor(currentLayerNodes.length / 3);
      for (let i = 0; i < connectionsCount; i++) {
        const node1 = currentLayerNodes[Math.floor(Math.random() * currentLayerNodes.length)];
        const node2 = currentLayerNodes[Math.floor(Math.random() * currentLayerNodes.length)];
        if (node1 !== node2) {
          edges.push({
            id: `edge_${node1}_${node2}`,
            source: node1.toString(),
            target: node2.toString(),
            weight: Math.random() * 0.4 + 0.1,
            color: '#AAA',
          });
        }
      }
    }
  }

  // 如果启用推荐，添加推荐用户
  if (recommendSimilar) {
    const recommendCount = Math.floor(Math.random() * 3) + 2; // 2-4个推荐用户
    for (let i = 0; i < recommendCount; i++) {
      const nodeId = `rec_${nodeIdCounter++}`;
      const userName = realUserNames[Math.floor(Math.random() * realUserNames.length)] + '(推荐)';
      const angle = (i / recommendCount) * 2 * Math.PI;
      const radius = (expandDepth + 1) * 90 + Math.random() * 30;

      nodes.push({
        id: nodeId,
        label: userName,
        x: Math.cos(angle) * radius,
        y: Math.sin(angle) * radius,
        size: 15,
        color: '#FFD60A',
      });

      // 推荐用户可能与现有用户有弱连接
      if (Math.random() > 0.5 && allNodeIds.length > 1) {
        const randomExistingNode = allNodeIds[Math.floor(Math.random() * allNodeIds.length)];
        edges.push({
          id: `edge_rec_${randomExistingNode}_${nodeId}`,
          source: randomExistingNode.toString(),
          target: nodeId,
          weight: Math.random() * 0.3 + 0.1,
          color: '#DDD',
        });
      }
    }
  }

  // 计算真实的统计信息
  const connectionCounts = new Map<string, number>();
  edges.forEach(edge => {
    connectionCounts.set(edge.source, (connectionCounts.get(edge.source) || 0) + 1);
    connectionCounts.set(edge.target, (connectionCounts.get(edge.target) || 0) + 1);
  });

  const connections = Array.from(connectionCounts.values());
  const maxConnections = connections.length > 0 ? Math.max(...connections) : 0;
  const avgConnections = connections.length > 0 ? Math.round((connections.reduce((a, b) => a + b, 0) / connections.length) * 10) / 10 : 0;

  const statistics = {
    totalNodes: nodes.length,
    totalEdges: edges.length,
    maxConnections,
    avgConnections,
    recommendedUsers: recommendSimilar ? nodes.filter(n => n.label.includes('推荐')).length : 0,
  };

  return {
    nodes,
    edges,
    statistics,
  };
}

// 生成全貌展示的模拟数据
// 生成全貌展示的模拟数据 - 基于真实 Neo4j 数据
function generateOverviewMockData(): QueryResponse {
  // 使用导入的 Neo4j 数据
  const data = neo4jData as Neo4jRecord[];

  // 转换为 Sigma 格式
  const sigmaData = convertToSigmaFormat(data);

  // 转换为我们的 GraphNode 和 GraphEdge 格式
  const nodes: GraphNode[] = sigmaData.nodes.map(node => ({
    id: node.key,
    label: node.label,
    x: node.x,
    y: node.y,
    size: node.size,
    color: node.color,
    type: node.tag,
    features: node.features,  // 传递 features 属性
  }));

  const edges: GraphEdge[] = sigmaData.edges.map(edge => ({
    id: edge.key,
    source: edge.source,
    target: edge.target,
    weight: edge.size || 1,
    color: edge.color,
  }));

  // 计算统计信息
  const connectionCounts = new Map<string, number>();
  edges.forEach(edge => {
    connectionCounts.set(edge.source, (connectionCounts.get(edge.source) || 0) + 1);
    connectionCounts.set(edge.target, (connectionCounts.get(edge.target) || 0) + 1);
  });

  const connections = Array.from(connectionCounts.values());
  const maxConnections = connections.length > 0 ? Math.max(...connections) : 0;
  const avgConnections = connections.length > 0 ? Math.round((connections.reduce((a, b) => a + b, 0) / connections.length) * 10) / 10 : 0;

  const statistics = {
    totalNodes: nodes.length,
    totalEdges: edges.length,
    maxConnections,
    avgConnections,
    recommendedUsers: 0,
  };

  return {
    nodes,
    edges,
    statistics,
  };
}

// 根据节点类型获取大小
function getNodeSize(nodeType: string): number {
  switch (nodeType) {
    case 'ego':
      return 20;      // 中心用户 - 最大
    case 'Circle':
      return 15;      // 聚合分类 - 中等
    case 'Node':
      return 8;       // 成员节点 - 最小
    default:
      return 10;      // 默认大小
  }
}

// 根据节点类型获取颜色
function getNodeColor(nodeType: string): string {
  switch (nodeType) {
    case 'ego':
      return '#3498db';    // 蓝色 - 中心用户
    case 'Circle':
      return '#f39c12';    // 橙色 - 聚合分类
    case 'Node':
      return '#e74c3c';    // 红色 - 成员节点
    default:
      return '#95a5a6';    // 灰色 - 默认
  }
}

// Neo4j 数据转换为 Sigma 格式的函数
function convertToSigmaFormat(neo4jData: Neo4jRecord[]) {
  const sigmaData = {
    nodes: [] as SigmaNode[],
    edges: [] as SigmaEdge[]
  };

  // 处理所有记录
  neo4jData.forEach((record, recordIndex) => {
    const graphData = record.graph_data;

    // 转换节点
    graphData.nodes.forEach((node: Neo4jNode) => {
      if (!sigmaData.nodes.find(n => n.key === String(node.id))) {
        sigmaData.nodes.push({
          key: String(node.id),
          label: node.label || String(node.id),
          tag: node.type,
          dataset: node.dataset,
          features: node.features || [],
          // Sigma.js 样式属性 - 支持三种节点类型
          x: Math.random() * 1000,
          y: Math.random() * 1000,
          size: getNodeSize(node.type),
          color: getNodeColor(node.type)
        });
      }
    });

    // 转换边 - 检查重复边
    graphData.edges.forEach((edge: Neo4jEdge, edgeIndex: number) => {
      if (edge.source && edge.target) {
        const sourceStr = String(edge.source);
        const targetStr = String(edge.target);

        // 检查是否已存在相同的边（双向检查）
        const edgeExists = sigmaData.edges.some(existingEdge =>
          (existingEdge.source === sourceStr && existingEdge.target === targetStr) ||
          (existingEdge.source === targetStr && existingEdge.target === sourceStr)
        );

        if (!edgeExists) {
          sigmaData.edges.push({
            key: `edge_${recordIndex}_${edgeIndex}`,
            source: sourceStr,
            target: targetStr,
            label: edge.type,
            type: edge.type,
            dataset: edge.dataset,
            // Sigma.js 样式属性
            color: edge.type === 'HAS_FRIEND' ? '#2980b9' : '#95a5a6',
            size: edge.type === 'HAS_FRIEND' ? 3 : 1
          });
        } else {
          console.log(`跳过重复边: ${sourceStr} -> ${targetStr}`);
        }
      }
    });
  });

  return sigmaData;
}
