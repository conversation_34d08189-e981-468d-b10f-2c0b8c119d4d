// 图查询 Hook

import { useState, useCallback, useRef, useEffect } from 'react';
import neo4jData from '../assets/neo4j_query_table_data_2025-7-30.json';

// Neo4j 数据类型定义
interface Neo4jNode {
  id: number;
  dataset: string;
  features: string[];
  label: string;
  type: string;
}

interface Neo4jEdge {
  source: number;
  dataset: string;
  target: number;
  type: string;
}

interface Neo4jRecord {
  graph_data: {
    nodes: Neo4jNode[];
    edges: Neo4jEdge[];
  };
}

interface SigmaNode {
  key: string;
  label: string;
  tag: string;
  dataset: string;
  features: string[];
  x: number;
  y: number;
  size: number;
  color: string;
}

interface SigmaEdge {
  key: string;
  source: string;
  target: string;
  label: string;
  type: string;
  dataset: string;
  color: string;
  size: number;
}

// 图节点接口
export interface GraphNode {
  id: string;
  label: string;
  x?: number;
  y?: number;
  size?: number;
  color?: string;
  type?: string;
  features?: string[];  // 添加 features 属性
}

// 图边接口
export interface GraphEdge {
  id: string;
  source: string;
  target: string;
  weight?: number;
  color?: string;
  type?: string;
}

// 查询响应接口
export interface QueryResponse {
  nodes: GraphNode[];
  edges: GraphEdge[];
  statistics: {
    totalNodes: number;
    totalEdges: number;
    maxConnections: number;
    avgConnections: number;
    recommendedUsers: number;
  };
}

interface UseGraphQueryResult {
  // 当前显示的数据
  data: QueryResponse | null;
  // 双数据源
  overviewData: QueryResponse | null;
  queryData: QueryResponse | null;
  // 视图模式
  viewMode: 'overview' | 'query';
  // 状态
  loading: boolean;
  error: string | null;
  // 方法
  executeQuery: (username: string, expandDepth: number, recommendSimilar?: boolean, userId?: number) => Promise<void>;
  clearData: () => void;
  clearError: () => void;
  switchToOverview: () => void;
  initializeOverview: () => void;
}

export const useGraphQuery = (onQueryComplete?: (duration: number) => void): UseGraphQueryResult => {
  // 双数据源状态
  const [overviewData, setOverviewData] = useState<QueryResponse | null>(null);
  const [queryData, setQueryData] = useState<QueryResponse | null>(null);
  const [viewMode, setViewMode] = useState<'overview' | 'query'>('overview');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 当前显示的数据
  const currentData = viewMode === 'query' && queryData ? queryData : overviewData;

  // 使用 useRef 来避免依赖问题
  const onQueryCompleteRef = useRef(onQueryComplete);
  onQueryCompleteRef.current = onQueryComplete;

  const executeQuery = useCallback(async (username: string, expandDepth: number, recommendSimilar: boolean = false, userId?: number) => {
    if (!username.trim()) {
      setError('请输入用户名');
      return;
    }

    if (expandDepth < 1 || expandDepth > 10) {
      setError('扩大深度必须在1-10之间');
      return;
    }

    setLoading(true);
    setError(null);

    // 开始计时
    const startTime = performance.now();

    try {
      console.log('开始执行查询:', { username, expandDepth, recommendSimilar, userId });

      // 暂时直接使用假数据展示功能
      console.log('使用假数据展示图计算查询功能');

      const result = generateRealisticMockData(username, userId || 0, expandDepth, recommendSimilar);

      console.log('生成的查询数据:', result);
      setQueryData(result);
      setViewMode('query'); // 查询完成后切换到查询视图
      console.log('查询数据已设置，节点数:', result.nodes.length, '边数:', result.edges.length);

      // 计算耗时
      const endTime = performance.now();
      const duration = (endTime - startTime) / 1000; // 转换为秒
      const formattedDuration = parseFloat(duration.toFixed(2)); // 保留2位小数

      console.log(`查询完成，耗时: ${formattedDuration} 秒`);

      // 调用完成回调
      if (onQueryCompleteRef.current) {
        onQueryCompleteRef.current(formattedDuration);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '查询失败';
      console.error('查询错误:', err);
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  // 初始化全貌数据
  const initializeOverview = useCallback(() => {
    console.log('初始化全貌数据');
    const overviewResult = generateOverviewMockData();
    setOverviewData(overviewResult);
    setViewMode('overview');
    console.log('全貌数据已设置，节点数:', overviewResult.nodes.length, '边数:', overviewResult.edges.length);
  }, []);

  // 切换到全貌视图
  const switchToOverview = useCallback(() => {
    console.log('切换到全貌视图');
    setViewMode('overview');
    setError(null);
  }, []);

  // 清除查询数据（相当于取消）
  const clearData = useCallback(() => {
    console.log('清除查询数据，回到全貌');
    setQueryData(null);
    setViewMode('overview');
    setError(null);
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // 组件挂载时初始化全貌数据
  useEffect(() => {
    if (!overviewData) {
      initializeOverview();
    }
  }, [overviewData, initializeOverview]);

  return {
    data: currentData,
    overviewData,
    queryData,
    viewMode,
    loading,
    error,
    executeQuery,
    clearData,
    clearError,
    switchToOverview,
    initializeOverview,
  };
};

// 生成真实感的假数据用于演示 - 基于Neo4j数据结构
function generateRealisticMockData(username: string, userId: number, expandDepth: number, recommendSimilar: boolean): QueryResponse {
  const nodes: GraphNode[] = [];
  const edges: GraphEdge[] = [];

  // 特征名称列表
  const featureNames = [
    '@Forbes:', '@ForbesGames', '@ForbesGames:', '@GoogleCowboy', '@ForbesLife:',
    '@TechCrunch', '@Wired', '@TheVerge', '@Engadget', '@Mashable',
    '@VentureBeat', '@TechRadar', '@CNET', '@Gizmodo', '@Ars',
    '@SlashDot', '@Reddit', '@HackerNews', '@GitHub', '@StackOverflow'
  ];

  let nodeIdCounter = 100000; // 使用大数字ID模拟真实数据
  const nodesByHop = new Map<number, string[]>(); // 记录每跳的节点

  // 第0跳：添加查询的EgoNode
  const egoNodeId = userId.toString();
  nodes.push({
    id: egoNodeId,
    label: username,
    type: 'ego',
    x: 0,
    y: 0,
    size: getNodeSize('ego'),
    color: getNodeColor('ego'),
    features: [featureNames[0], featureNames[1], featureNames[2]]
  });
  nodesByHop.set(0, [egoNodeId]);

  // 为EgoNode添加一些FeatureName节点
  const egoFeatures = featureNames.slice(0, 3);
  egoFeatures.forEach((feature, index) => {
    const featureNodeId = `feature_${nodeIdCounter++}`;
    nodes.push({
      id: featureNodeId,
      label: feature,
      type: 'FeatureName',
      x: Math.cos((index / egoFeatures.length) * 2 * Math.PI) * 50,
      y: Math.sin((index / egoFeatures.length) * 2 * Math.PI) * 50,
      size: 6,
      color: '#9b59b6',
      features: [feature]
    });

    edges.push({
      id: `edge_${egoNodeId}_${featureNodeId}`,
      source: egoNodeId,
      target: featureNodeId,
      weight: 1,
      color: '#9b59b6'
    });
  });

  // 生成多跳数据
  for (let hop = 1; hop <= expandDepth; hop++) {
    const currentHopNodes: string[] = [];
    const prevHopNodes = nodesByHop.get(hop - 1) || [];

    // 每跳生成的节点数量递减
    const nodeCount = Math.max(2, Math.min(6, 8 - hop));

    for (let i = 0; i < nodeCount; i++) {
      // 生成Circle节点（聚合分类）
      const circleNodeId = `circle_${nodeIdCounter++}`;
      const angle = (i / nodeCount) * 2 * Math.PI;
      const radius = hop * 120;

      nodes.push({
        id: circleNodeId,
        label: `Circle_${hop}_${i + 1}`,
        type: 'Circle',
        x: Math.cos(angle) * radius,
        y: Math.sin(angle) * radius,
        size: getNodeSize('Circle'),
        color: getNodeColor('Circle'),
        features: [featureNames[Math.floor(Math.random() * featureNames.length)]]
      });
      currentHopNodes.push(circleNodeId);

      // 连接到上一跳的节点
      const sourceNode = prevHopNodes[Math.floor(Math.random() * prevHopNodes.length)];
      edges.push({
        id: `edge_${sourceNode}_${circleNodeId}`,
        source: sourceNode,
        target: circleNodeId,
        weight: 1,
        color: '#2980b9'
      });

      // 为每个Circle生成2-4个friend节点
      const friendCount = Math.floor(Math.random() * 3) + 2;
      for (let j = 0; j < friendCount; j++) {
        const friendNodeId = `friend_${nodeIdCounter++}`;
        const friendAngle = angle + (j / friendCount - 0.5) * 0.8;
        const friendRadius = radius + 60;

        nodes.push({
          id: friendNodeId,
          label: `Friend_${hop}_${i + 1}_${j + 1}`,
          type: 'friend',
          x: Math.cos(friendAngle) * friendRadius,
          y: Math.sin(friendAngle) * friendRadius,
          size: getNodeSize('friend'),
          color: getNodeColor('friend'),
          features: [
            featureNames[Math.floor(Math.random() * featureNames.length)],
            featureNames[Math.floor(Math.random() * featureNames.length)]
          ]
        });

        // friend节点属于Circle
        edges.push({
          id: `edge_${circleNodeId}_${friendNodeId}`,
          source: circleNodeId,
          target: friendNodeId,
          weight: 1,
          color: '#95a5a6'
        });

        // friend之间的连接
        if (j > 0 && Math.random() > 0.5) {
          const prevFriendId = `friend_${nodeIdCounter - 2}`;
          edges.push({
            id: `edge_${prevFriendId}_${friendNodeId}`,
            source: prevFriendId,
            target: friendNodeId,
            weight: 0.5,
            color: '#95a5a6'
          });
        }
      }
    }

    nodesByHop.set(hop, currentHopNodes);
  }

  // 相似数据推荐：推荐另一个类似的ego节点及其网络
  if (recommendSimilar) {
    const recommendedEgoId = `rec_ego_${nodeIdCounter++}`;
    const recX = 300;
    const recY = 200;

    // 推荐的ego节点
    nodes.push({
      id: recommendedEgoId,
      label: `推荐用户_${Math.floor(Math.random() * 1000)}`,
      type: 'ego',
      x: recX,
      y: recY,
      size: getNodeSize('ego'),
      color: '#e67e22', // 橙色表示推荐
      features: [featureNames[3], featureNames[4], featureNames[5]]
    });

    // 为推荐ego生成1-2跳的网络
    for (let recHop = 1; recHop <= Math.min(2, expandDepth); recHop++) {
      const recNodeCount = Math.max(2, 4 - recHop);

      for (let i = 0; i < recNodeCount; i++) {
        // 推荐网络的Circle
        const recCircleId = `rec_circle_${nodeIdCounter++}`;
        const angle = (i / recNodeCount) * 2 * Math.PI;
        const radius = recHop * 80;

        nodes.push({
          id: recCircleId,
          label: `推荐Circle_${recHop}_${i + 1}`,
          type: 'Circle',
          x: recX + Math.cos(angle) * radius,
          y: recY + Math.sin(angle) * radius,
          size: getNodeSize('Circle'),
          color: '#e67e22',
          features: [featureNames[Math.floor(Math.random() * featureNames.length)]]
        });

        // 连接到推荐ego或上一层
        const sourceId = recHop === 1 ? recommendedEgoId : `rec_circle_${nodeIdCounter - recNodeCount - 1}`;
        edges.push({
          id: `edge_${sourceId}_${recCircleId}`,
          source: sourceId,
          target: recCircleId,
          weight: 1,
          color: '#e67e22'
        });

        // 为推荐Circle生成friend节点
        const recFriendCount = Math.floor(Math.random() * 2) + 1;
        for (let j = 0; j < recFriendCount; j++) {
          const recFriendId = `rec_friend_${nodeIdCounter++}`;
          const friendAngle = angle + (j / recFriendCount - 0.5) * 0.6;
          const friendRadius = radius + 50;

          nodes.push({
            id: recFriendId,
            label: `推荐Friend_${recHop}_${i + 1}_${j + 1}`,
            type: 'friend',
            x: recX + Math.cos(friendAngle) * friendRadius,
            y: recY + Math.sin(friendAngle) * friendRadius,
            size: getNodeSize('friend'),
            color: '#e67e22',
            features: [featureNames[Math.floor(Math.random() * featureNames.length)]]
          });

          edges.push({
            id: `edge_${recCircleId}_${recFriendId}`,
            source: recCircleId,
            target: recFriendId,
            weight: 1,
            color: '#e67e22'
          });
        }
      }
    }

    // 在原始网络和推荐网络之间添加一些弱连接
    if (Math.random() > 0.3) {
      const originalNodes = nodes.filter(n => !n.id.startsWith('rec_') && n.type !== 'FeatureName');
      const randomOriginalNode = originalNodes[Math.floor(Math.random() * originalNodes.length)];

      edges.push({
        id: `edge_cross_${randomOriginalNode.id}_${recommendedEgoId}`,
        source: randomOriginalNode.id,
        target: recommendedEgoId,
        weight: 0.3,
        color: '#bdc3c7'
      });
    }
  }

  // 计算统计信息
  const connectionCounts = new Map<string, number>();
  edges.forEach(edge => {
    connectionCounts.set(edge.source, (connectionCounts.get(edge.source) || 0) + 1);
    connectionCounts.set(edge.target, (connectionCounts.get(edge.target) || 0) + 1);
  });

  const connections = Array.from(connectionCounts.values());
  const maxConnections = connections.length > 0 ? Math.max(...connections) : 0;
  const avgConnections = connections.length > 0 ? Math.round((connections.reduce((a, b) => a + b, 0) / connections.length) * 10) / 10 : 0;

  const statistics = {
    totalNodes: nodes.length,
    totalEdges: edges.length,
    maxConnections,
    avgConnections,
    recommendedUsers: recommendSimilar ? nodes.filter(n => n.id.startsWith('rec_ego_')).length : 0,
  };

  return {
    nodes,
    edges,
    statistics,
  };
}

// 生成全貌展示的模拟数据
// 生成全貌展示的模拟数据 - 基于真实 Neo4j 数据
function generateOverviewMockData(): QueryResponse {
  // 使用导入的 Neo4j 数据
  const data = neo4jData as Neo4jRecord[];

  // 转换为 Sigma 格式
  const sigmaData = convertToSigmaFormat(data);

  // 转换为我们的 GraphNode 和 GraphEdge 格式
  const nodes: GraphNode[] = sigmaData.nodes.map(node => ({
    id: node.key,
    label: node.label,
    x: node.x,
    y: node.y,
    size: node.size,
    color: node.color,
    type: node.tag,
    features: node.features,  // 传递 features 属性
  }));

  const edges: GraphEdge[] = sigmaData.edges.map(edge => ({
    id: edge.key,
    source: edge.source,
    target: edge.target,
    weight: edge.size || 1,
    color: edge.color,
  }));

  // 计算统计信息
  const connectionCounts = new Map<string, number>();
  edges.forEach(edge => {
    connectionCounts.set(edge.source, (connectionCounts.get(edge.source) || 0) + 1);
    connectionCounts.set(edge.target, (connectionCounts.get(edge.target) || 0) + 1);
  });

  const connections = Array.from(connectionCounts.values());
  const maxConnections = connections.length > 0 ? Math.max(...connections) : 0;
  const avgConnections = connections.length > 0 ? Math.round((connections.reduce((a, b) => a + b, 0) / connections.length) * 10) / 10 : 0;

  const statistics = {
    totalNodes: nodes.length,
    totalEdges: edges.length,
    maxConnections,
    avgConnections,
    recommendedUsers: 0,
  };

  return {
    nodes,
    edges,
    statistics,
  };
}

// 根据节点类型获取大小
function getNodeSize(nodeType: string): number {
  switch (nodeType) {
    case 'ego':
      return 20;      // 中心用户 - 最大
    case 'Circle':
      return 15;      // 聚合分类 - 中等
    case 'friend':
      return 8;       // 朋友节点 - 最小
    default:
      return 10;      // 默认大小
  }
}

// 根据节点类型获取颜色
function getNodeColor(nodeType: string): string {
  switch (nodeType) {
    case 'ego':
      return '#3498db';    // 蓝色 - 中心用户
    case 'Circle':
      return '#f39c12';    // 橙色 - 聚合分类
    case 'friend':
      return '#e74c3c';    // 红色 - 朋友节点
    default:
      return '#95a5a6';    // 灰色 - 默认
  }
}

// Neo4j 数据转换为 Sigma 格式的函数
function convertToSigmaFormat(neo4jData: Neo4jRecord[]) {
  const sigmaData = {
    nodes: [] as SigmaNode[],
    edges: [] as SigmaEdge[]
  };

  // 处理所有记录
  neo4jData.forEach((record, recordIndex) => {
    const graphData = record.graph_data;

    // 转换节点
    graphData.nodes.forEach((node: Neo4jNode) => {
      if (!sigmaData.nodes.find(n => n.key === String(node.id))) {
        sigmaData.nodes.push({
          key: String(node.id),
          label: node.label || String(node.id),
          tag: node.type,
          dataset: node.dataset,
          features: node.features || [],
          // Sigma.js 样式属性 - 支持三种节点类型
          x: Math.random() * 1000,
          y: Math.random() * 1000,
          size: getNodeSize(node.type),
          color: getNodeColor(node.type)
        });
      }
    });

    // 转换边 - 检查重复边
    graphData.edges.forEach((edge: Neo4jEdge, edgeIndex: number) => {
      if (edge.source && edge.target) {
        const sourceStr = String(edge.source);
        const targetStr = String(edge.target);

        // 检查是否已存在相同的边（双向检查）
        const edgeExists = sigmaData.edges.some(existingEdge =>
          (existingEdge.source === sourceStr && existingEdge.target === targetStr) ||
          (existingEdge.source === targetStr && existingEdge.target === sourceStr)
        );

        if (!edgeExists) {
          sigmaData.edges.push({
            key: `edge_${recordIndex}_${edgeIndex}`,
            source: sourceStr,
            target: targetStr,
            label: edge.type,
            type: edge.type,
            dataset: edge.dataset,
            // Sigma.js 样式属性
            color: edge.type === 'HAS_FRIEND' ? '#2980b9' : '#95a5a6',
            size: edge.type === 'HAS_FRIEND' ? 3 : 1
          });
        } else {
          console.log(`跳过重复边: ${sourceStr} -> ${targetStr}`);
        }
      }
    });
  });

  return sigmaData;
}
