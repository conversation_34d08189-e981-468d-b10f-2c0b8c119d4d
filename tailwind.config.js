/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        // 根据 spec/style.md 定义的主题色
        primary: {
          dark: '#101010',      // 主色：深灰
          blue: '#1D77FF',      // 高亮色：蓝色
          orange: '#FB956B',    // 强调色：橙红色
          yellow: '#F2BC29',    // 次要强调色：黄色
        },
        text: {
          primary: '#FFFFFF',   // 默认文字：白色
          secondary: '#9D9D9D', // 次要文字：浅灰
        },
        'card-bg': '#1A1A1A',   // 卡片背景色
      },
      fontFamily: {
        'arial': ['Arial', 'sans-serif'],
      },
      fontSize: {
        'spec-regular': '16px',
        'spec-header': '22px',
        'spec-body': '18px',
        'spec-large': '36px',
      },
      animation: {
        'spin-slow': 'spin 3s linear infinite',
      },
    },
  },
  plugins: [],
}
