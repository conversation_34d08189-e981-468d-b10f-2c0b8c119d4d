// 数据浏览器视图组件

import React from 'react';
import GraphDataExplorer from '../GraphDataExplorer';

const ExplorerView: React.FC = () => {
  return (
    <div className="space-y-8">
      {/* 页面标题 */}
      <div className="bg-card-bg rounded-[20px] p-12">
        <h1 className="text-primary-blue text-spec-header font-semibold mb-4 font-arial">
          图数据浏览器
        </h1>
        <p className="text-text-primary text-spec-body leading-normal font-arial">
          浏览和查询图数据库中的数据，支持连接真实的 Thrift 服务进行数据操作。
          您可以查看图列表、顶点类型、边类型，以及执行基本的图数据库操作。
        </p>
      </div>

      {/* 图数据浏览器组件 */}
      <GraphDataExplorer />
    </div>
  );
};

export default ExplorerView;
