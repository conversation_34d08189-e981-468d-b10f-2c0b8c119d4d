# 用户查询功能使用指南

本指南介绍如何使用 Thrift 接口实现用户名查询、最大深度查询和相似用户推荐功能。

## 功能概述

### 1. 用户名查询 (queryUserByName)
根据用户名查找用户信息，支持精确匹配和模糊匹配。

**使用场景：**
- 根据用户名搜索用户
- 验证用户是否存在
- 获取用户的基本信息

**实现原理：**
使用 `filterQuery` 方法，通过属性过滤器查询指定用户名的用户。

### 2. 最大深度路径查询 (queryMaxDepthPath)
查询两个用户之间的最短路径或从指定用户出发的最大深度遍历。

**使用场景：**
- 查找两个用户之间的关系路径
- 分析用户的影响范围
- 计算社交网络的连通性

**实现原理：**
- 两点路径：使用 `pathFindCalc` 方法
- 广度遍历：使用 `filterQuery` 方法配合 `stepLayerLimit`

### 3. 相似用户推荐 (recommendSimilarUsers)
基于图算法推荐与指定用户相似的其他用户。

**使用场景：**
- 好友推荐
- 内容推荐
- 社交网络扩展

**实现原理：**
- 基于连接相似度：使用 `recommendCalc` 方法
- 基于共同好友：使用 `similarMutualCalc` 方法

## 核心 Thrift 方法说明

### filterQuery
复杂查询方法，支持多种过滤条件和遍历策略。

```typescript
interface FilterQueryRequest {
  graph: string;                    // 图名称
  startVertex: string;              // 起始顶点类型
  startIds: number[];               // 起始顶点ID列表
  throughEdges: string[];           // 遍历的边类型
  targetEdgeNodeName: string;       // 目标顶点类型
  isReturnEdge: number;             // 是否返回边信息
  count: number;                    // 返回结果数量限制
  stepLayerLimit: number;           // 遍历深度限制
  filterMap: FilterMap;             // 过滤条件映射
  targetFilter: Filter | null;      // 目标过滤器
}
```

### recommendCalc
推荐算法计算方法。

```typescript
interface RecommendRequest {
  graph: string;                    // 图名称
  startId: number;                  // 起始用户ID
  throughEdges: string[];           // 遍历的边类型
  filterVector: Filter[];           // 过滤条件向量
  corelationFlag: number;           // 相关性计算标志
  multiFlag: number;                // 多路径计算标志
  minCount: number;                 // 最小共同连接数
  minInDegree: number;              // 最小入度
  topn: number;                     // 返回前N个结果
}
```

### similarMutualCalc
相似度互相关计算方法。

```typescript
interface SimilarMutualRequest {
  graph: string;                    // 图名称
  edge1: string;                    // 第一层边类型
  edge2: string;                    // 第二层边类型
  startId: number;                  // 起始用户ID
  topn: number;                     // 返回前N个结果
  multiFlag: number;                // 多路径标志
  filterStep1: Filter;              // 第一步过滤器
  filterStep2: Filter;              // 第二步过滤器
}
```

### pathFindCalc
路径查找计算方法。

```typescript
interface PathFindRequest {
  graph: string;                    // 图名称
  startId: number;                  // 起始用户ID
  destId: number;                   // 目标用户ID
  throughEdges: string[];           // 遍历的边类型
  filterVector: Filter[];           // 过滤条件向量
  topn: number;                     // 返回前N条路径
}
```

## 使用示例

### 1. 查询用户名为 "Alice" 的用户

```typescript
import { UserQueryService } from '../services/graph/UserQueryService';

const queryService = new UserQueryService('social_network', 'User', 'Friend');

// 查询用户
const users = await queryService.queryUserByName('Alice');
console.log('找到的用户:', users);
```

### 2. 查询两个用户之间的路径

```typescript
// 查询用户ID 1 到用户ID 100 之间的路径，最大深度为 5
const paths = await queryService.queryMaxDepthPath(1, 100, 5);
console.log('路径结果:', paths);
```

### 3. 推荐相似用户

```typescript
// 为用户ID 1 推荐前10个相似用户
const recommendations = await queryService.recommendSimilarUsers(1, 10);
console.log('推荐用户:', recommendations);
```

### 4. 复合查询：根据用户名查找并推荐

```typescript
// 查找用户 "Bob" 并推荐相似用户
const result = await queryService.queryUserAndRecommend('Bob', 5);
console.log('用户信息:', result.user);
console.log('推荐列表:', result.recommendations);
```

## 配置参数说明

### 图配置
- **graphName**: 图数据库名称，如 'social_network'
- **userVertexType**: 用户顶点类型，如 'User'
- **relationEdgeType**: 关系边类型，如 'Friend', 'Follow', 'Like'

### 查询参数
- **topN**: 推荐结果数量，建议 5-20
- **maxDepth**: 最大遍历深度，建议 3-6
- **minCommonConnections**: 最少共同连接数，建议 2-5
- **timeout**: 查询超时时间，复杂查询建议 10-15 秒

## 性能优化建议

1. **合理设置深度限制**：避免过深的遍历导致性能问题
2. **使用适当的过滤条件**：减少不必要的计算
3. **批量查询**：对于多个用户的查询，考虑批量处理
4. **缓存结果**：对于频繁查询的结果进行缓存
5. **异步处理**：使用 Promise 和 async/await 进行异步处理

## 错误处理

所有查询方法都包含完整的错误处理机制：

```typescript
try {
  const result = await queryService.queryUserByName('Alice');
  // 处理成功结果
} catch (error) {
  console.error('查询失败:', error.message);
  // 处理错误情况
}
```

## 注意事项

1. **数据格式**：确保图数据库中的用户数据包含 'name' 字段
2. **连接配置**：确保 Thrift 服务器正确配置和运行
3. **权限控制**：在生产环境中添加适当的权限验证
4. **数据一致性**：注意并发查询时的数据一致性问题

## 扩展功能

基于现有的查询服务，可以进一步扩展：

1. **高级过滤**：支持更复杂的属性过滤条件
2. **聚合查询**：统计分析功能
3. **实时推荐**：基于用户行为的实时推荐
4. **图可视化**：将查询结果可视化展示
5. **批量操作**：支持批量用户查询和推荐
