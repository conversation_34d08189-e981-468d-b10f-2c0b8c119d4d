
namespace cl tutorial
namespace cpp tutorial
namespace d tutorial
namespace dart tutorial
namespace java tutorial
namespace php tutorial
namespace perl tutorial
namespace haxe tutorial
namespace netcore tutorial

/**
 * Thrift lets you do typedefs to get pretty names for your types. Standard
 * C style here.
 */
typedef i32 MyInteger

/**
 * Thrift also lets you define constants for use across languages. Complex
 * types and structs are specified using JSON notation.
 */
const i32 INT32CONSTANT = 9853
const map<string,string> MAPCONSTANT = {'hello':'world', 'goodnight':'moon'}

/**
 * You can define enums, which are just 32 bit integers. Values are optional
 * and start at 1 if not supplied, C style again.
 */
enum Operation {
  ADD = 1,
  SUBTRACT = 2,
  MULTIPLY = 3,
  DIVIDE = 4
}

/**
 * Structs are the basic complex data structures. They are comprised of fields
 * which each have an integer identifier, a type, a symbolic name, and an
 * optional default value.
 *
 * Fields can be declared "optional", which ensures they will not be included
 * in the serialized output if they aren't set.  Note that this requires some
 * manual management in some languages.
 */
struct Work {
  1: i32 num1 = 0,
  2: i32 num2,
  3: Operation op,
  4: optional string comment,
}

struct EPair {
  1: i64 eid,
  2: i64 vid,
}

struct VertexPk {
  1: i64 id,
  2: i64 id_l,
  3: string id_s,
  4: string vertexName
}
struct EdgePk {
  1: i64 id,
  2: string edgeName,
  3: i64 srcid,
  4: i64 dstid
}

struct VertexID {
  1: i64 id,
  2: string name
}

struct IntervalPair {
  1: i64 left,
  2: i64 right,
  3: string name
}

/**
 * Structs can also be exceptions, if they are nasty.
 */
exception InvalidOperation {
  1: i32 whatOp,
  2: string why
}

struct ReadNodeFilter_Req {
  1: i32 filterType = -1,
  2: string nodeName,
  3: string attrName,
  4: string upper,
  5: string lower
}
struct ReadFilter_Req {
  1: i32 filterType = -1,
  2: string nodeName,
  3: string attrName,
  4: string upper,
  5: string lower,
  6: ReadNodeFilter_Req targetFilter
}
struct ReadFilterSet_Req {
  1: list<ReadFilter_Req> filters,
  2: i32 relationType
}
struct ReadFilterQuery_Req {
  1: string graph,
  2: string startVertex,
  3: list<i64> startIds,
  4: list<string> throughEdges,
  5: string targetEdgeNodeName,
  6: i32 isReturnEdge = 1,
  7: i32 count = 100,
  8: i32 stepLayerLimit = 0, 
  9: map<string, ReadFilterSet_Req> filterMap,
  10: ReadFilterSet_Req targetFilter
}
struct ReadRegulerQuery_Req {
  1: string graph,
  2: string startVertex,
  3: list<i64> startIds,
  4: list<string> throughEdges,
  5: string targetEdgeNodeName,
  6: i32 isReturnEdge = 1,
  7: i32 count = 100,
  8: list<ReadFilterSet_Req> filterVector
}
struct ReadRegulerQuerySort_Req {
  1: string graph,
  2: string startVertex,
  3: list<i64> startIds,
  4: list<string> throughEdges,
  5: string targetEdgeNodeName,
  6: i32 isReturnEdge = 1,
  7: i32 count = 100,
  8: list<ReadFilterSet_Req> filterVector,
  9: list<string> sortAttrs
}
struct StepGroupQuery_Req {
  1: string graph,
  2: string startVertex,
  3: list<i64> startIds,
  4: list<string> throughEdges,
  5: list<ReadFilterSet_Req> filterVector,
  6: map<i32, list<string>> edgeAttrGroup, 
  7: map<i32, list<string>> vertexAttrGroup,
  8: i32 sortFlag = 0,
  9: i32 sortTopN = 100
}
struct SimilarFREdgeQuery_Req {
  1: string graph,
  2: string edge,
  3: i64 startId,
  4: i32 startDirect,
  5: string scoreAttrName,
  6: i32 topn, 
  7: ReadFilterSet_Req filterStep1,
  8: ReadFilterSet_Req filterStep2
}
struct SimilarMutualQuery_Req {
  1: string graph,
  2: string edge1,
  3: string edge2,
  4: i64 startId,
  5: i32 topn, 
  6: i32 multiFlag,
  7: ReadFilterSet_Req filterStep1,
  8: ReadFilterSet_Req filterStep2
}
struct RecommendQuery_Req {
  1: string graph,
  2: i64 startId,
  3: list<string> throughEdges,
  4: list<ReadFilterSet_Req> filterVector,
  5: i32 corelationFlag, 
  6: i32 multiFlag,
  7: i32 minCount,
  8: i32 minInDegree,
  9: i32 topn
}
struct PathQuery_Req {
  1: string graph,
  2: i64 startId,
  3: i64 destId,
  4: list<string> throughEdges,
  5: list<ReadFilterSet_Req> filterVector,
  6: i32 topn
}
struct SimplePageQuery_Req {
  1: string graph,
  2: string veName,
  3: bool vertexFlag,
  4: ReadFilterSet_Req filter,
  5: i32 pageSize,
  6: i64 scrollId = -1,
}
struct SimplePageQuerySort_Req {
  1: string graph,
  2: string veName,
  3: bool vertexFlag,
  4: ReadFilterSet_Req filter,
  5: i32 pageSize,
  6: i64 scrollId = -1,
  7: list<string> sortAttrs
}
struct SimpleGroupQuery_Req {
  1: string graph,
  2: string veName,
  3: bool vertexFlag,
  4: ReadFilterSet_Req filter,
  5: list<string> attrGroup,
  6: i32 sortFlag = 0,
  7: i32 sortTopN = 100
}
struct CompondStepQuery_Req {
  1: SimplePageQuery_Req startQuery,
  2: ReadRegulerQuery_Req stepQuery
}
struct CompondGroupQuery_Req {
  1: SimpleGroupQuery_Req startQuery,
  2: StepGroupQuery_Req stepQuery
}
struct TagJob4WanderWorkRestrain {
  1: list<ReadRegulerQuery_Req> restrains,
  2: i32 restrainLogic
}
struct TagJob4WanderWork_Req {
  1: list<RecommendQuery_Req> paths,
  2: i64 jobid,
  3: string resultEdge,
  4: i32 logic,
  5: string factEdge,
  6: map<i64, TagJob4WanderWorkRestrain> restrainMap,
  7: double ratio,
  8: i32 single,
  9: double threshold,
  10: i32 brandId,
  11: string userBrandEdgeName
}
struct TagJob4Custom1_Req {
  1: list<RecommendQuery_Req> paths,
  2: i64 jobid,
  3: string resultEdge,
  4: map<i64, i32> brandMap,
  5: string brandAttrName,
  6: double ratio,
  7: i32 single,
  8: i32 brandId,
  9: string userBrandEdgeName,
  10: i32 brandEdgeFlag = 1,
  11: i32 brandStep = 1
}
struct ReadRegulerJobQuery_Req {
  1: string graph,
  2: list<i64> startIds,
  3: list<string> throughEdges,
  4: map<i32, list<string>> edgeAttrGroup, 
  5: map<i32, list<string>> vertexAttrGroup,
  6: list<ReadFilterSet_Req> filterVector,
  7: map<string, list<string>> expVertexAttrGroup,
  8: i64 jobid
}
struct ReadRegulerJob_BranchEdge_Req {
  1:string edgename;
  2:ReadFilterSet_Req filter;
  3:list<string> edgeAttrs;
  4:list<string> vertexAttrs;
}
struct ReadRegulerJob_BranchPath_Req {
  1:list<ReadRegulerJob_BranchEdge_Req> path;
  2:i32 type;
}
struct ReadRegulerJob_MainStep_Req {
  1:ReadRegulerJob_BranchEdge_Req mainStep;
  2:list<ReadRegulerJob_BranchPath_Req> branchPaths;
}
struct ReadRegulerJobQueryV2_Req {
  1: string graph,
  2: list<i64> startIds,
  3: i64 jobid,
  4: list<ReadRegulerJob_MainStep_Req> paths,
  5: list<string> vertexAttrs;
}
struct TagJob4Custom2PathThreshold_Req {
  1: i32 filterType = -1,
  2: double upper,
  3: double lower
}
struct TagJob4Custom2PathThresholdSet_Req {
  1: list<TagJob4Custom2PathThreshold_Req> thresholds,
  2: i32 relationType
}
struct TagJob4Custom2PathFilter_Req {
  1: i32 filterType = -1,
  2: i32 fillType = 0,
  3: string attrName,
  4: string upper,
  5: string lower
}
struct TagJob4Custom2PathFilterSet_Req {
  1: list<TagJob4Custom2PathFilter_Req> filters,
  2: i32 relationType
}
struct TagJob4Custom2Path_Req {
  1: list<string> throughEdges,
  2: list<ReadFilterSet_Req> filters_up,
  3: list<ReadFilterSet_Req> filters_down,
  4: i32 filterLogic_up,
  5: i32 filterLogic_down,
  6: i32 calcType,
  7: i32 statisType_up,
  8: i32 statisType_down,
  9: TagJob4Custom2PathThreshold_Req threshold,
  10: list<TagJob4Custom2PathFilterSet_Req> filters_spec_edge_up,
  11: list<TagJob4Custom2PathFilterSet_Req> filters_spec_edge_down,
  12: list<TagJob4Custom2PathFilterSet_Req> filters_spec_node_up,
  13: list<TagJob4Custom2PathFilterSet_Req> filters_spec_node_down
}
struct TagJob4Custom2_Req {
  1: string graph,
  2: double ratio,
  3: i32 single,
  4: list<TagJob4Custom2Path_Req> paths,
  5: i32 thresholdLogic,
  6: list<TagJob4Custom2PathThresholdSet_Req> thresholdSets,
  7: i64 jobid,
  8: i32 threadCnt,
  9: list<i64> singleTagIdList,
  10: string resultEdge,
  11: i32 brandId,
  12: string userBrandEdgeName
}
struct TagJob4Custom4Path_Req {
  1: list<string> throughEdges,
  2: list<ReadFilterSet_Req> filters,
  3: i32 up_step,
  4: i32 up_isEdge,
  5: string up_attr,
  6: i32 down_step,
  7: i32 down_isEdge,
  8: string down_attr
}
struct TagJob4Custom4_Req {
  1: string graph,
  2: double ratio,
  3: list<TagJob4Custom4Path_Req> paths,
  4: i64 jobid
}
struct TagJob4Custom3_Res {
  1: i32 pathidx,
  2: i64 uid,
  3: i64 tagid,
  4: i64 up,
  5: i64 down,
  6: double score
}

struct FreeBroadPath_Req {
  1: list<string> throughEdges,
  2: list<ReadFilterSet_Req> edgefilters,
  3: list<string> throughVertexes,
  4: list<ReadFilterSet_Req> vertexfilters,
  5: string startVertex,
  6: list<i64> startIds,
  7: i32 steplimit = 100,
  8: i32 resultlimit = 10000,
  9: string graph
}
struct VertexCommunicatePath_Req {
  1: list<string> throughEdges,
  2: list<ReadFilterSet_Req> edgefilters,
  3: list<string> throughVertexes,
  4: list<ReadFilterSet_Req> vertexfilters,
  5: i64 srcid,
  6: i64 dstid,
  7: string graph,
  8: i32 resultlimit = 10000,
  9: i32 calclimit = 10000000,
  10: i32 pathlimit = 100,
  11: i32 stepDown = 0,
  12: i32 stepUp = 100,
  13: i32 shortFlag = 0,
  14: string srcVertex,
  15: string dstVertex
}
struct VertexConnectionPath_Req {
  1: list<string> throughEdges,
  2: list<ReadFilterSet_Req> edgefilters,
  3: list<string> throughVertexes,
  4: list<ReadFilterSet_Req> vertexfilters,
  5: list<VertexID> vids,
  6: string graph,
  7: i32 resultlimit = 10000,
  8: i32 calclimit = 10000000,
  9: i32 pathlimit = 100
}


struct GraphEntityResult_Res {
  1: list<VertexPk> vids,
  2: list<EdgePk> eids
}
struct PathResultBean_Res {
  1: string edge_name,
  2: string dst_vname,
  3: i64 eid,
  4: i64 dst_vid,
  5: string dst_vpk
}

/**
 * type : 0-属性值等值聚合 1-属性值分段聚合(左闭右开)
 */
struct PathCalcJobQuery_Agg_Field_Req {
  1: i32 type,
  2: string name,
  3: list<IntervalPair> interval_vals
}
/**
 * type : 0-不聚合 1-属性聚合 2-终点聚合 
 */
struct PathCalcJobQuery_Agg_Req {
  1: i32 type,
  2: map<i32, list<PathCalcJobQuery_Agg_Field_Req>> edgeAttrGroup, 
  3: map<i32, list<PathCalcJobQuery_Agg_Field_Req>> vertexAttrGroup
}
/**
 * start_type 1-根据startIds指定点起始 2-根据startVertexFilter的过滤条件从概念出发 3-通过jobid的计算结果出发
 */
struct PathCalcJobQuery_Path_Req {
  1: list<i64> startIds,
  2: ReadFilterSet_Req startVertexFilter,
  3: i64 jobid,
  4: i32 start_type,
  5: list<string> throughEdges,
  6: list<ReadFilterSet_Req> filterVector,
  7: string start_vertex,
  8: PathCalcJobQuery_Agg_Req agginfo,
}
/**
 * type : 1-count 2-distinct 3-numberic(sum max min) 4-Intersection 5-Union 6-Complement
 * list_type : 0-不保存清单 1-保存终点 2-保存终边 3-保存指定属性值
 * 当type=3的时候，需设置type_step:要计算的属性在第几步、type_edge:要计算的属性是否是边、type_attrname:要计算的属性的名、type_pathidx:指标属性在第几条路径称
 * multi_type, 1代表多路径串行计算：点集，2代表多路径串行计算：边集
 */
struct PathCalcJobQuery_Calc_Req {
  1: i32 type,
  2: i32 type_step,
  3: i32 type_edge,
  4: i32 type_pathidx,
  5: string type_attrname,
  6: i32 list_type,
  7: i32 multi_type,
  8: map<i32, list<string>> edgeAttrGroup, 
  9: map<i32, list<string>> vertexAttrGroup
}
/**
 * jobid : 0表示同步
 */
struct PathCalcJobQuery_Req {
  1: string graph,
  2: list<PathCalcJobQuery_Path_Req> pathinfos,
  3: PathCalcJobQuery_Calc_Req calcinfo,
  4: PathCalcJobQuery_Agg_Req agginfo, 
  5: i64 jobid,
  6: i32 threadCnt = 1,
  7: string start_vertex
}


enum DataTypeEnum {
  INT_TYPE = 1,
  DOUBLE_TYPE = 2,
  STRING_Type = 3
}
struct MixDataStruct_Res {
  1: DataTypeEnum dataType, 
  2: string str,
  3: i64 num,
  4: double dbl 
}
struct StepGroupObject_Res {
  1: list<MixDataStruct_Res> attrs, 
  2: i64 val
}
struct KeyValuePair_Res {
  1: i64 kid, 
  2: double val
}
struct PageList_Res {
  1: list<i64> ids, 
  2: i64 scrollId
}
struct TagJobSample_Res {
  1: i64 total,
  2: i64 sample,
  3: i64 result,
  4: map<i64, i32> tagMap,
  5: i32 state,
  6: map<i32, i32> brand_total_map,
  7: map<i32, i32> brand_result_map,
  8: map<i32, map<i64, i32>> brand_tag_map
}

struct PathCalcJob_Num_Res {
  1: i64 max,
  2: i64 min,
  3: i64 sum,
  4: double max_d,
  5: double min_d,
  6: double sum_d,
  7: i64 count,
  8: i64 distinct_count
}
struct PathCalcJob_Agg_Res {
  1: list<string> groups,
  2: PathCalcJob_Num_Res func_val
}

/**
 * num_type : 1-整数 2-浮点数
 */
struct PathCalcJob_Res {
  1: i32 calc_type,
  2: i32 num_type,
  3: PathCalcJob_Num_Res func_val,
  4: list<PathCalcJob_Agg_Res> agg_val,
  5: i64 offset
}

struct SameTargetQuery_Req {
  1: string graph,
  2: list<string> throughEdges,
  3: list<i64> vids,
  4: list<ReadFilterSet_Req> filterVector, 
  5: i32 resSort = 0,
  6: i32 threadCnt = 1,
  7: i32 resLimit = 100
}


/**
 * Ahh, now onto the cool part, defining a service. Services just need a name
 * and can optionally inherit from another service using the extends keyword.
 */
service GraphCalculator {

  /**
   * A method definition looks like C code. It has a return type, arguments,
   * and optionally a list of exceptions that it may throw. Note that argument
   * lists and exception lists are specified using the exact same syntax as
   * field lists in struct or exception definitions.
   */

   void ping(),

   //i32 add(1:i32 num1, 2:i32 num2),

   //i32 calculate(1:i32 logid, 2:Work w) throws (1:InvalidOperation ouch),

    i32 makeNewGraph(1:string graph),
    list<string> showGraphs(),
    list<string> showVertexes(1:string graph),
    list<string> showEdges(1:string graph),

    i32 loadStructToGraph(1:string filename, 2:string graph, 3:string name, 4:i32 isEdgeFlag),
    string showGraphConfig(1:string graph, 2:string name, 3:i32 isEdgeFlag),

    i32 loadCsvTxtToGraph(1:string graph, 2:string nname, 3:string filename, 4:i32 csvflag, 5:i32 edgeFlag, 6:i32 edgecopy),

    i64 insertVertex(1:string graph, 2:string nname, 3:string vjson),
    list<i64> insertVertexes(1:string graph, 2:string nname, 3:list<string> vjsons),

    i64 insertEdge(1:string graph, 2:string ename, 3:string ejson),
    list<i64> insertEdges(1:string graph, 2:string ename, 3:list<string> ejsons),

    i64 getVertexCount(1:string graph, 2:string vertex),
    i64 getEdgeCount(1:string graph, 2:string edge),

    list<string> scanVertex(1:string graph, 2:string vertex, 3:i64 offset, 4:i32 count),
    list<string> scanEdge(1:string graph, 2:string edge, 3:i64 offset, 4:i32 count, 5:i32 containVertexPK),

    string getVertexInfoById(1:string graph, 2:string vertex, 3:i64 idx),
    i64 getVertexIdxByPK(1:string graph, 2:string vertex, 3:string pk, 4:i64 pk_i),

    string getVertexInfoByIds(1:string graph, 2:string vertex, 3:list<i64> vids),
    string getEdgeInfoByIds(1:string graph, 2:string edge, 3:list<i64> eids, 4:i32 containVertexPK),

    i64 getEdgeCountBySrcId(1:string graph, 2:string edge, 3:i64 vid),

    list<EPair> getEdgeListBySrcId(1:string graph, 2:string edge, 3:i64 vid, 4:i64 offset, 5:i32 count),

    list<list<string>> getEdgeListBySrcIds(1:string graph, 2:string edge, 3:list<string> vids),

    list<list<i64>> shortestPath(1:string graph, 2:string edge, 3:i64 srcId, 4:i64 dstId, 5:i32 weightseq),

    list<list<i64>> bfsallPath(1:string graph, 2:string edge, 3:i64 srcId, 4:i64 dstId, 5:i32 pathlimit, 6:i32 layerlimit, 7:list<i64> throughVids),

    list<list<i64>> dfsallPath(1:string graph, 2:string edge, 3:i64 srcId, 4:i64 dstId, 5:i32 pathlimit),

    set<i64> filterQuery(1:ReadFilterQuery_Req query, 2:i32 midFilterFlag), 
    set<i64> regulerQuery(1:ReadRegulerQuery_Req query), 
    list<i64> regulerQueryWithSort(1:ReadRegulerQuerySort_Req query), 
    i32 reloadMemory(1:string graph),
    i32 deleteVertexById(1:string graph, 2:string vertex, 3:i64 vid),
    i32 deleteEdgeById(1:string graph, 2:string edge, 3:i64 eid),
    i32 deleteEdgeBySrcId(1:string graph, 2:string edge, 3:i64 vid),
    i32 deleteEdgeByDstId(1:string graph, 2:string edge, 3:i64 vid),
    list<i64> getInEdgeIds(1:string graph, 2:string edge, 3:i64 vid),
    i64 getDeletedVertexCount(1:string graph, 2:string vertex),
    i64 getDeletedEdgeCount(1:string graph, 2:string edge),
    i32 deleteVertex(1:string graph, 2:string vertex), 
    i32 deleteEdge(1:string graph, 2:string edge), 
    i32 deleteGraph(1:string graph), 
    list<StepGroupObject_Res> stepGroupCalc(1:StepGroupQuery_Req query), 
    list<StepGroupObject_Res> stepGroupCalc_Multi(1:StepGroupQuery_Req query), 
    list<KeyValuePair_Res> similarFREdgeCalc(1:SimilarFREdgeQuery_Req query), 
    list<KeyValuePair_Res> similarMutualCalc(1:SimilarMutualQuery_Req query), 
    list<KeyValuePair_Res> recommendCalc(1:RecommendQuery_Req query), 
    list<list<i64>> pathFindCalc(1:PathQuery_Req query),
    set<i64> regulerSetQuery(1:list<ReadRegulerQuery_Req> querys, 2:i32 type), 
    i64 checkEdgeExists(1:string graph, 2:string edge, 3:i64 srcIdx, 4:i64 dstIdx, 5:ReadFilterSet_Req filters),
    i64 updateEdge(1:string graph, 2:string ename, 3:string ejson, 4:i64 eid),
    PageList_Res simplePageQuery(1:SimplePageQuery_Req query), 
    PageList_Res simplePageQueryWithSort(1:SimplePageQuerySort_Req query), 
    list<StepGroupObject_Res> simpleGroupCalc(1:SimpleGroupQuery_Req query),
    set<i64> compondStepQuery(1:CompondStepQuery_Req query),
    list<StepGroupObject_Res> compondGroupCalc(1:CompondGroupQuery_Req query),
    list<list<string>> pathMatchCalc(1:string graph, 2:string startVertex, 3:string endVertex),
    i64 submitTagJob4WanderWork(1:TagJob4WanderWork_Req query),
    i64 submitTagJob4Custom1(1:TagJob4Custom1_Req query),
    TagJobSample_Res queryTagJobProgress(1:i64 jobid),
    i64 submitReadRegulerJob(1:ReadRegulerJobQuery_Req query),
    i32 queryReadRegulerJobState(1:i64 jobid),
    i32 removeReadRegulerJobState(1:i64 jobid),
    i32 removeReadRegulerJobFile(1:i64 jobid),
    i64 queryReadRegulerJobFileCount(1:i64 jobid),
    string queryReadRegulerJobFile(1:i64 jobid, 2:i64 offset, 3:i32 count),
    list<i64> findOneIdDfs(1:string graph, 2:list<i64> ids, 3:string firstEdge, 4:string secEdge),
    i64 submitTagJob4Custom2(1:TagJob4Custom2_Req query),
    i64 submitTagJob4Custom3(1:TagJob4Custom2_Req query),
    i64 submitTagJob4Custom4(1:TagJob4Custom4_Req query),
    i64 submitEdgeSetCalcJob(1:i64 jobid, 2:string graph, 3:string preEdge, 4:string aftEdge),
    i32 queryEdgeSetCalcJobState(1:i64 jobid),
    list<TagJob4Custom3_Res> queryTagJob4Custom3File(1:i64 jobid, 2:string graphName, 3:i64 start, 4:i32 count),
    list<TagJob4Custom3_Res> queryTagJob4Custom4File(1:i64 jobid, 2:string graphName, 3:i64 start, 4:i32 count),
    string queryEdgeSetCalcJobResult(1:i64 jobid, 2:i64 offset, 3:i32 count),
    i32 removeEdgeSetCalcJobResult(1:i64 jobid),
    string queryTagCalcResultEdgeByBrand(1:string graphName, 2:string edgeName, 3:i64 offset, 4:i32 count, 5: string userBrandEdgeName, 6: i32 brandId),
    i64 submitReadRegulerJobV2(1:ReadRegulerJobQueryV2_Req query),
    string queryReadRegulerJobFileV2(1:i64 jobid, 2:i64 offset, 3:i32 count),
    i64 edgeSetDistinctCount(1:string graph, 2:list<string> edges),

    PathCalcJob_Res submitPathCalcJob(1:PathCalcJobQuery_Req query),
    PathCalcJob_Res queryPathCalcJob(1:string graphname, 2:i64 jobid),
    string queryPathCalcJobListFile(1:string graphname, 2:i64 jobid, 3:i64 offset, 4:i32 count),
    GraphEntityResult_Res queryFreeBroadPath(1:FreeBroadPath_Req query),
    list<list<PathResultBean_Res> > queryVertexCommunicatePath(1:VertexCommunicatePath_Req query),
    list<list<PathResultBean_Res> > queryVertexConnectionPath(1:VertexConnectionPath_Req query),

    list<EPair> sameTargetCalc(1:SameTargetQuery_Req query),


   /**
    * This method has a oneway modifier. That means the client only makes
    * a request and does not listen for any response at all. Oneway methods
    * must be void.
    */
   oneway void zip()

}

/**
 * That just about covers the basics. Take a look in the test/ folder for more
 * detailed examples. After you run this file, your generated code shows up
 * in folders with names gen-<language>. The generated code isn't too scary
 * to look at. It even has pretty indentation.
 */
