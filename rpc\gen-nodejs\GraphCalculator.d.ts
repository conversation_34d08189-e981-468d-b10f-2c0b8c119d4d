//
// Autogenerated by Thrift Compiler (0.22.0)
//
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
//

import thrift = require('thrift');
import Thrift = thrift.Thrift;
import Q = thrift.Q;
import Int64 = require('node-int64');

import ttypes = require('./graphapi_types');
import Operation = ttypes.Operation
import DataTypeEnum = ttypes.DataTypeEnum
import INT32CONSTANT = ttypes.INT32CONSTANT
import MAPCONSTANT = ttypes.MAPCONSTANT
import InvalidOperation = ttypes.InvalidOperation
import Work = ttypes.Work
import EPair = ttypes.EPair
import VertexPk = ttypes.VertexPk
import EdgePk = ttypes.EdgePk
import VertexID = ttypes.VertexID
import IntervalPair = ttypes.IntervalPair
import ReadNodeFilter_Req = ttypes.ReadNodeFilter_Req
import ReadFilter_Req = ttypes.ReadFilter_Req
import ReadFilterSet_Req = ttypes.ReadFilterSet_Req
import ReadFilterQuery_Req = ttypes.ReadFilterQuery_Req
import ReadRegulerQuery_Req = ttypes.ReadRegulerQuery_Req
import ReadRegulerQuerySort_Req = ttypes.ReadRegulerQuerySort_Req
import StepGroupQuery_Req = ttypes.StepGroupQuery_Req
import SimilarFREdgeQuery_Req = ttypes.SimilarFREdgeQuery_Req
import SimilarMutualQuery_Req = ttypes.SimilarMutualQuery_Req
import RecommendQuery_Req = ttypes.RecommendQuery_Req
import PathQuery_Req = ttypes.PathQuery_Req
import SimplePageQuery_Req = ttypes.SimplePageQuery_Req
import SimplePageQuerySort_Req = ttypes.SimplePageQuerySort_Req
import SimpleGroupQuery_Req = ttypes.SimpleGroupQuery_Req
import CompondStepQuery_Req = ttypes.CompondStepQuery_Req
import CompondGroupQuery_Req = ttypes.CompondGroupQuery_Req
import TagJob4WanderWorkRestrain = ttypes.TagJob4WanderWorkRestrain
import TagJob4WanderWork_Req = ttypes.TagJob4WanderWork_Req
import TagJob4Custom1_Req = ttypes.TagJob4Custom1_Req
import ReadRegulerJobQuery_Req = ttypes.ReadRegulerJobQuery_Req
import ReadRegulerJob_BranchEdge_Req = ttypes.ReadRegulerJob_BranchEdge_Req
import ReadRegulerJob_BranchPath_Req = ttypes.ReadRegulerJob_BranchPath_Req
import ReadRegulerJob_MainStep_Req = ttypes.ReadRegulerJob_MainStep_Req
import ReadRegulerJobQueryV2_Req = ttypes.ReadRegulerJobQueryV2_Req
import TagJob4Custom2PathThreshold_Req = ttypes.TagJob4Custom2PathThreshold_Req
import TagJob4Custom2PathThresholdSet_Req = ttypes.TagJob4Custom2PathThresholdSet_Req
import TagJob4Custom2PathFilter_Req = ttypes.TagJob4Custom2PathFilter_Req
import TagJob4Custom2PathFilterSet_Req = ttypes.TagJob4Custom2PathFilterSet_Req
import TagJob4Custom2Path_Req = ttypes.TagJob4Custom2Path_Req
import TagJob4Custom2_Req = ttypes.TagJob4Custom2_Req
import TagJob4Custom4Path_Req = ttypes.TagJob4Custom4Path_Req
import TagJob4Custom4_Req = ttypes.TagJob4Custom4_Req
import TagJob4Custom3_Res = ttypes.TagJob4Custom3_Res
import FreeBroadPath_Req = ttypes.FreeBroadPath_Req
import VertexCommunicatePath_Req = ttypes.VertexCommunicatePath_Req
import VertexConnectionPath_Req = ttypes.VertexConnectionPath_Req
import GraphEntityResult_Res = ttypes.GraphEntityResult_Res
import PathResultBean_Res = ttypes.PathResultBean_Res
import PathCalcJobQuery_Agg_Field_Req = ttypes.PathCalcJobQuery_Agg_Field_Req
import PathCalcJobQuery_Agg_Req = ttypes.PathCalcJobQuery_Agg_Req
import PathCalcJobQuery_Path_Req = ttypes.PathCalcJobQuery_Path_Req
import PathCalcJobQuery_Calc_Req = ttypes.PathCalcJobQuery_Calc_Req
import PathCalcJobQuery_Req = ttypes.PathCalcJobQuery_Req
import MixDataStruct_Res = ttypes.MixDataStruct_Res
import StepGroupObject_Res = ttypes.StepGroupObject_Res
import KeyValuePair_Res = ttypes.KeyValuePair_Res
import PageList_Res = ttypes.PageList_Res
import TagJobSample_Res = ttypes.TagJobSample_Res
import PathCalcJob_Num_Res = ttypes.PathCalcJob_Num_Res
import PathCalcJob_Agg_Res = ttypes.PathCalcJob_Agg_Res
import PathCalcJob_Res = ttypes.PathCalcJob_Res
import SameTargetQuery_Req = ttypes.SameTargetQuery_Req

/**
 * Ahh, now onto the cool part, defining a service. Services just need a name
 * and can optionally inherit from another service using the extends keyword.
 */
declare class Client {
    private output: thrift.TTransport;
    private pClass: thrift.TProtocol;
    private _seqid: number;

    constructor(output: thrift.TTransport, pClass: { new(trans: thrift.TTransport): thrift.TProtocol });

    /**
     * A method definition looks like C code. It has a return type, arguments,
     * and optionally a list of exceptions that it may throw. Note that argument
     * lists and exception lists are specified using the exact same syntax as
     * field lists in struct or exception definitions.
     */
    ping(): Promise<void>;

    /**
     * A method definition looks like C code. It has a return type, arguments,
     * and optionally a list of exceptions that it may throw. Note that argument
     * lists and exception lists are specified using the exact same syntax as
     * field lists in struct or exception definitions.
     */
    ping(callback: (error: void, response: void)=>void): void;

    makeNewGraph(graph: string): Promise<number>;

    makeNewGraph(graph: string, callback: (error: void, response: number)=>void): void;

    showGraphs(): Promise<string[]>;

    showGraphs(callback: (error: void, response: string[])=>void): void;

    showVertexes(graph: string): Promise<string[]>;

    showVertexes(graph: string, callback: (error: void, response: string[])=>void): void;

    showEdges(graph: string): Promise<string[]>;

    showEdges(graph: string, callback: (error: void, response: string[])=>void): void;

    loadStructToGraph(filename: string, graph: string, name: string, isEdgeFlag: number): Promise<number>;

    loadStructToGraph(filename: string, graph: string, name: string, isEdgeFlag: number, callback: (error: void, response: number)=>void): void;

    showGraphConfig(graph: string, name: string, isEdgeFlag: number): Promise<string>;

    showGraphConfig(graph: string, name: string, isEdgeFlag: number, callback: (error: void, response: string)=>void): void;

    loadCsvTxtToGraph(graph: string, nname: string, filename: string, csvflag: number, edgeFlag: number, edgecopy: number): Promise<number>;

    loadCsvTxtToGraph(graph: string, nname: string, filename: string, csvflag: number, edgeFlag: number, edgecopy: number, callback: (error: void, response: number)=>void): void;

    insertVertex(graph: string, nname: string, vjson: string): Promise<Int64>;

    insertVertex(graph: string, nname: string, vjson: string, callback: (error: void, response: Int64)=>void): void;

    insertVertexes(graph: string, nname: string, vjsons: string[]): Promise<Int64[]>;

    insertVertexes(graph: string, nname: string, vjsons: string[], callback: (error: void, response: Int64[])=>void): void;

    insertEdge(graph: string, ename: string, ejson: string): Promise<Int64>;

    insertEdge(graph: string, ename: string, ejson: string, callback: (error: void, response: Int64)=>void): void;

    insertEdges(graph: string, ename: string, ejsons: string[]): Promise<Int64[]>;

    insertEdges(graph: string, ename: string, ejsons: string[], callback: (error: void, response: Int64[])=>void): void;

    getVertexCount(graph: string, vertex: string): Promise<Int64>;

    getVertexCount(graph: string, vertex: string, callback: (error: void, response: Int64)=>void): void;

    getEdgeCount(graph: string, edge: string): Promise<Int64>;

    getEdgeCount(graph: string, edge: string, callback: (error: void, response: Int64)=>void): void;

    scanVertex(graph: string, vertex: string, offset: Int64, count: number): Promise<string[]>;

    scanVertex(graph: string, vertex: string, offset: Int64, count: number, callback: (error: void, response: string[])=>void): void;

    scanEdge(graph: string, edge: string, offset: Int64, count: number, containVertexPK: number): Promise<string[]>;

    scanEdge(graph: string, edge: string, offset: Int64, count: number, containVertexPK: number, callback: (error: void, response: string[])=>void): void;

    getVertexInfoById(graph: string, vertex: string, idx: Int64): Promise<string>;

    getVertexInfoById(graph: string, vertex: string, idx: Int64, callback: (error: void, response: string)=>void): void;

    getVertexIdxByPK(graph: string, vertex: string, pk: string, pk_i: Int64): Promise<Int64>;

    getVertexIdxByPK(graph: string, vertex: string, pk: string, pk_i: Int64, callback: (error: void, response: Int64)=>void): void;

    getVertexInfoByIds(graph: string, vertex: string, vids: Int64[]): Promise<string>;

    getVertexInfoByIds(graph: string, vertex: string, vids: Int64[], callback: (error: void, response: string)=>void): void;

    getEdgeInfoByIds(graph: string, edge: string, eids: Int64[], containVertexPK: number): Promise<string>;

    getEdgeInfoByIds(graph: string, edge: string, eids: Int64[], containVertexPK: number, callback: (error: void, response: string)=>void): void;

    getEdgeCountBySrcId(graph: string, edge: string, vid: Int64): Promise<Int64>;

    getEdgeCountBySrcId(graph: string, edge: string, vid: Int64, callback: (error: void, response: Int64)=>void): void;

    getEdgeListBySrcId(graph: string, edge: string, vid: Int64, offset: Int64, count: number): Promise<EPair[]>;

    getEdgeListBySrcId(graph: string, edge: string, vid: Int64, offset: Int64, count: number, callback: (error: void, response: EPair[])=>void): void;

    getEdgeListBySrcIds(graph: string, edge: string, vids: string[]): Promise<string[][]>;

    getEdgeListBySrcIds(graph: string, edge: string, vids: string[], callback: (error: void, response: string[][])=>void): void;

    shortestPath(graph: string, edge: string, srcId: Int64, dstId: Int64, weightseq: number): Promise<Int64[][]>;

    shortestPath(graph: string, edge: string, srcId: Int64, dstId: Int64, weightseq: number, callback: (error: void, response: Int64[][])=>void): void;

    bfsallPath(graph: string, edge: string, srcId: Int64, dstId: Int64, pathlimit: number, layerlimit: number, throughVids: Int64[]): Promise<Int64[][]>;

    bfsallPath(graph: string, edge: string, srcId: Int64, dstId: Int64, pathlimit: number, layerlimit: number, throughVids: Int64[], callback: (error: void, response: Int64[][])=>void): void;

    dfsallPath(graph: string, edge: string, srcId: Int64, dstId: Int64, pathlimit: number): Promise<Int64[][]>;

    dfsallPath(graph: string, edge: string, srcId: Int64, dstId: Int64, pathlimit: number, callback: (error: void, response: Int64[][])=>void): void;

    filterQuery(query: ReadFilterQuery_Req, midFilterFlag: number): Promise<Int64[]>;

    filterQuery(query: ReadFilterQuery_Req, midFilterFlag: number, callback: (error: void, response: Int64[])=>void): void;

    regulerQuery(query: ReadRegulerQuery_Req): Promise<Int64[]>;

    regulerQuery(query: ReadRegulerQuery_Req, callback: (error: void, response: Int64[])=>void): void;

    regulerQueryWithSort(query: ReadRegulerQuerySort_Req): Promise<Int64[]>;

    regulerQueryWithSort(query: ReadRegulerQuerySort_Req, callback: (error: void, response: Int64[])=>void): void;

    reloadMemory(graph: string): Promise<number>;

    reloadMemory(graph: string, callback: (error: void, response: number)=>void): void;

    deleteVertexById(graph: string, vertex: string, vid: Int64): Promise<number>;

    deleteVertexById(graph: string, vertex: string, vid: Int64, callback: (error: void, response: number)=>void): void;

    deleteEdgeById(graph: string, edge: string, eid: Int64): Promise<number>;

    deleteEdgeById(graph: string, edge: string, eid: Int64, callback: (error: void, response: number)=>void): void;

    deleteEdgeBySrcId(graph: string, edge: string, vid: Int64): Promise<number>;

    deleteEdgeBySrcId(graph: string, edge: string, vid: Int64, callback: (error: void, response: number)=>void): void;

    deleteEdgeByDstId(graph: string, edge: string, vid: Int64): Promise<number>;

    deleteEdgeByDstId(graph: string, edge: string, vid: Int64, callback: (error: void, response: number)=>void): void;

    getInEdgeIds(graph: string, edge: string, vid: Int64): Promise<Int64[]>;

    getInEdgeIds(graph: string, edge: string, vid: Int64, callback: (error: void, response: Int64[])=>void): void;

    getDeletedVertexCount(graph: string, vertex: string): Promise<Int64>;

    getDeletedVertexCount(graph: string, vertex: string, callback: (error: void, response: Int64)=>void): void;

    getDeletedEdgeCount(graph: string, edge: string): Promise<Int64>;

    getDeletedEdgeCount(graph: string, edge: string, callback: (error: void, response: Int64)=>void): void;

    deleteVertex(graph: string, vertex: string): Promise<number>;

    deleteVertex(graph: string, vertex: string, callback: (error: void, response: number)=>void): void;

    deleteEdge(graph: string, edge: string): Promise<number>;

    deleteEdge(graph: string, edge: string, callback: (error: void, response: number)=>void): void;

    deleteGraph(graph: string): Promise<number>;

    deleteGraph(graph: string, callback: (error: void, response: number)=>void): void;

    stepGroupCalc(query: StepGroupQuery_Req): Promise<StepGroupObject_Res[]>;

    stepGroupCalc(query: StepGroupQuery_Req, callback: (error: void, response: StepGroupObject_Res[])=>void): void;

    stepGroupCalc_Multi(query: StepGroupQuery_Req): Promise<StepGroupObject_Res[]>;

    stepGroupCalc_Multi(query: StepGroupQuery_Req, callback: (error: void, response: StepGroupObject_Res[])=>void): void;

    similarFREdgeCalc(query: SimilarFREdgeQuery_Req): Promise<KeyValuePair_Res[]>;

    similarFREdgeCalc(query: SimilarFREdgeQuery_Req, callback: (error: void, response: KeyValuePair_Res[])=>void): void;

    similarMutualCalc(query: SimilarMutualQuery_Req): Promise<KeyValuePair_Res[]>;

    similarMutualCalc(query: SimilarMutualQuery_Req, callback: (error: void, response: KeyValuePair_Res[])=>void): void;

    recommendCalc(query: RecommendQuery_Req): Promise<KeyValuePair_Res[]>;

    recommendCalc(query: RecommendQuery_Req, callback: (error: void, response: KeyValuePair_Res[])=>void): void;

    pathFindCalc(query: PathQuery_Req): Promise<Int64[][]>;

    pathFindCalc(query: PathQuery_Req, callback: (error: void, response: Int64[][])=>void): void;

    regulerSetQuery(querys: ReadRegulerQuery_Req[], type: number): Promise<Int64[]>;

    regulerSetQuery(querys: ReadRegulerQuery_Req[], type: number, callback: (error: void, response: Int64[])=>void): void;

    checkEdgeExists(graph: string, edge: string, srcIdx: Int64, dstIdx: Int64, filters: ReadFilterSet_Req): Promise<Int64>;

    checkEdgeExists(graph: string, edge: string, srcIdx: Int64, dstIdx: Int64, filters: ReadFilterSet_Req, callback: (error: void, response: Int64)=>void): void;

    updateEdge(graph: string, ename: string, ejson: string, eid: Int64): Promise<Int64>;

    updateEdge(graph: string, ename: string, ejson: string, eid: Int64, callback: (error: void, response: Int64)=>void): void;

    simplePageQuery(query: SimplePageQuery_Req): Promise<PageList_Res>;

    simplePageQuery(query: SimplePageQuery_Req, callback: (error: void, response: PageList_Res)=>void): void;

    simplePageQueryWithSort(query: SimplePageQuerySort_Req): Promise<PageList_Res>;

    simplePageQueryWithSort(query: SimplePageQuerySort_Req, callback: (error: void, response: PageList_Res)=>void): void;

    simpleGroupCalc(query: SimpleGroupQuery_Req): Promise<StepGroupObject_Res[]>;

    simpleGroupCalc(query: SimpleGroupQuery_Req, callback: (error: void, response: StepGroupObject_Res[])=>void): void;

    compondStepQuery(query: CompondStepQuery_Req): Promise<Int64[]>;

    compondStepQuery(query: CompondStepQuery_Req, callback: (error: void, response: Int64[])=>void): void;

    compondGroupCalc(query: CompondGroupQuery_Req): Promise<StepGroupObject_Res[]>;

    compondGroupCalc(query: CompondGroupQuery_Req, callback: (error: void, response: StepGroupObject_Res[])=>void): void;

    pathMatchCalc(graph: string, startVertex: string, endVertex: string): Promise<string[][]>;

    pathMatchCalc(graph: string, startVertex: string, endVertex: string, callback: (error: void, response: string[][])=>void): void;

    submitTagJob4WanderWork(query: TagJob4WanderWork_Req): Promise<Int64>;

    submitTagJob4WanderWork(query: TagJob4WanderWork_Req, callback: (error: void, response: Int64)=>void): void;

    submitTagJob4Custom1(query: TagJob4Custom1_Req): Promise<Int64>;

    submitTagJob4Custom1(query: TagJob4Custom1_Req, callback: (error: void, response: Int64)=>void): void;

    queryTagJobProgress(jobid: Int64): Promise<TagJobSample_Res>;

    queryTagJobProgress(jobid: Int64, callback: (error: void, response: TagJobSample_Res)=>void): void;

    submitReadRegulerJob(query: ReadRegulerJobQuery_Req): Promise<Int64>;

    submitReadRegulerJob(query: ReadRegulerJobQuery_Req, callback: (error: void, response: Int64)=>void): void;

    queryReadRegulerJobState(jobid: Int64): Promise<number>;

    queryReadRegulerJobState(jobid: Int64, callback: (error: void, response: number)=>void): void;

    removeReadRegulerJobState(jobid: Int64): Promise<number>;

    removeReadRegulerJobState(jobid: Int64, callback: (error: void, response: number)=>void): void;

    removeReadRegulerJobFile(jobid: Int64): Promise<number>;

    removeReadRegulerJobFile(jobid: Int64, callback: (error: void, response: number)=>void): void;

    queryReadRegulerJobFileCount(jobid: Int64): Promise<Int64>;

    queryReadRegulerJobFileCount(jobid: Int64, callback: (error: void, response: Int64)=>void): void;

    queryReadRegulerJobFile(jobid: Int64, offset: Int64, count: number): Promise<string>;

    queryReadRegulerJobFile(jobid: Int64, offset: Int64, count: number, callback: (error: void, response: string)=>void): void;

    findOneIdDfs(graph: string, ids: Int64[], firstEdge: string, secEdge: string): Promise<Int64[]>;

    findOneIdDfs(graph: string, ids: Int64[], firstEdge: string, secEdge: string, callback: (error: void, response: Int64[])=>void): void;

    submitTagJob4Custom2(query: TagJob4Custom2_Req): Promise<Int64>;

    submitTagJob4Custom2(query: TagJob4Custom2_Req, callback: (error: void, response: Int64)=>void): void;

    submitTagJob4Custom3(query: TagJob4Custom2_Req): Promise<Int64>;

    submitTagJob4Custom3(query: TagJob4Custom2_Req, callback: (error: void, response: Int64)=>void): void;

    submitTagJob4Custom4(query: TagJob4Custom4_Req): Promise<Int64>;

    submitTagJob4Custom4(query: TagJob4Custom4_Req, callback: (error: void, response: Int64)=>void): void;

    submitEdgeSetCalcJob(jobid: Int64, graph: string, preEdge: string, aftEdge: string): Promise<Int64>;

    submitEdgeSetCalcJob(jobid: Int64, graph: string, preEdge: string, aftEdge: string, callback: (error: void, response: Int64)=>void): void;

    queryEdgeSetCalcJobState(jobid: Int64): Promise<number>;

    queryEdgeSetCalcJobState(jobid: Int64, callback: (error: void, response: number)=>void): void;

    queryTagJob4Custom3File(jobid: Int64, graphName: string, start: Int64, count: number): Promise<TagJob4Custom3_Res[]>;

    queryTagJob4Custom3File(jobid: Int64, graphName: string, start: Int64, count: number, callback: (error: void, response: TagJob4Custom3_Res[])=>void): void;

    queryTagJob4Custom4File(jobid: Int64, graphName: string, start: Int64, count: number): Promise<TagJob4Custom3_Res[]>;

    queryTagJob4Custom4File(jobid: Int64, graphName: string, start: Int64, count: number, callback: (error: void, response: TagJob4Custom3_Res[])=>void): void;

    queryEdgeSetCalcJobResult(jobid: Int64, offset: Int64, count: number): Promise<string>;

    queryEdgeSetCalcJobResult(jobid: Int64, offset: Int64, count: number, callback: (error: void, response: string)=>void): void;

    removeEdgeSetCalcJobResult(jobid: Int64): Promise<number>;

    removeEdgeSetCalcJobResult(jobid: Int64, callback: (error: void, response: number)=>void): void;

    queryTagCalcResultEdgeByBrand(graphName: string, edgeName: string, offset: Int64, count: number, userBrandEdgeName: string, brandId: number): Promise<string>;

    queryTagCalcResultEdgeByBrand(graphName: string, edgeName: string, offset: Int64, count: number, userBrandEdgeName: string, brandId: number, callback: (error: void, response: string)=>void): void;

    submitReadRegulerJobV2(query: ReadRegulerJobQueryV2_Req): Promise<Int64>;

    submitReadRegulerJobV2(query: ReadRegulerJobQueryV2_Req, callback: (error: void, response: Int64)=>void): void;

    queryReadRegulerJobFileV2(jobid: Int64, offset: Int64, count: number): Promise<string>;

    queryReadRegulerJobFileV2(jobid: Int64, offset: Int64, count: number, callback: (error: void, response: string)=>void): void;

    edgeSetDistinctCount(graph: string, edges: string[]): Promise<Int64>;

    edgeSetDistinctCount(graph: string, edges: string[], callback: (error: void, response: Int64)=>void): void;

    submitPathCalcJob(query: PathCalcJobQuery_Req): Promise<PathCalcJob_Res>;

    submitPathCalcJob(query: PathCalcJobQuery_Req, callback: (error: void, response: PathCalcJob_Res)=>void): void;

    queryPathCalcJob(graphname: string, jobid: Int64): Promise<PathCalcJob_Res>;

    queryPathCalcJob(graphname: string, jobid: Int64, callback: (error: void, response: PathCalcJob_Res)=>void): void;

    queryPathCalcJobListFile(graphname: string, jobid: Int64, offset: Int64, count: number): Promise<string>;

    queryPathCalcJobListFile(graphname: string, jobid: Int64, offset: Int64, count: number, callback: (error: void, response: string)=>void): void;

    queryFreeBroadPath(query: FreeBroadPath_Req): Promise<GraphEntityResult_Res>;

    queryFreeBroadPath(query: FreeBroadPath_Req, callback: (error: void, response: GraphEntityResult_Res)=>void): void;

    queryVertexCommunicatePath(query: VertexCommunicatePath_Req): Promise<PathResultBean_Res[][]>;

    queryVertexCommunicatePath(query: VertexCommunicatePath_Req, callback: (error: void, response: PathResultBean_Res[][])=>void): void;

    queryVertexConnectionPath(query: VertexConnectionPath_Req): Promise<PathResultBean_Res[][]>;

    queryVertexConnectionPath(query: VertexConnectionPath_Req, callback: (error: void, response: PathResultBean_Res[][])=>void): void;

    sameTargetCalc(query: SameTargetQuery_Req): Promise<EPair[]>;

    sameTargetCalc(query: SameTargetQuery_Req, callback: (error: void, response: EPair[])=>void): void;

    /**
     * This method has a oneway modifier. That means the client only makes
     * a request and does not listen for any response at all. Oneway methods
     * must be void.
     */
    zip(): Promise<void>;

    /**
     * This method has a oneway modifier. That means the client only makes
     * a request and does not listen for any response at all. Oneway methods
     * must be void.
     */
    zip(callback: (error: void, response: void)=>void): void;
  }

declare class Processor {
  private _handler: object;

  constructor(handler: object);
  process(input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_ping(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_makeNewGraph(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_showGraphs(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_showVertexes(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_showEdges(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_loadStructToGraph(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_showGraphConfig(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_loadCsvTxtToGraph(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_insertVertex(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_insertVertexes(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_insertEdge(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_insertEdges(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_getVertexCount(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_getEdgeCount(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_scanVertex(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_scanEdge(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_getVertexInfoById(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_getVertexIdxByPK(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_getVertexInfoByIds(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_getEdgeInfoByIds(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_getEdgeCountBySrcId(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_getEdgeListBySrcId(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_getEdgeListBySrcIds(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_shortestPath(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_bfsallPath(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_dfsallPath(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_filterQuery(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_regulerQuery(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_regulerQueryWithSort(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_reloadMemory(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_deleteVertexById(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_deleteEdgeById(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_deleteEdgeBySrcId(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_deleteEdgeByDstId(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_getInEdgeIds(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_getDeletedVertexCount(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_getDeletedEdgeCount(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_deleteVertex(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_deleteEdge(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_deleteGraph(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_stepGroupCalc(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_stepGroupCalc_Multi(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_similarFREdgeCalc(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_similarMutualCalc(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_recommendCalc(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_pathFindCalc(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_regulerSetQuery(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_checkEdgeExists(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_updateEdge(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_simplePageQuery(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_simplePageQueryWithSort(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_simpleGroupCalc(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_compondStepQuery(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_compondGroupCalc(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_pathMatchCalc(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_submitTagJob4WanderWork(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_submitTagJob4Custom1(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_queryTagJobProgress(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_submitReadRegulerJob(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_queryReadRegulerJobState(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_removeReadRegulerJobState(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_removeReadRegulerJobFile(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_queryReadRegulerJobFileCount(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_queryReadRegulerJobFile(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_findOneIdDfs(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_submitTagJob4Custom2(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_submitTagJob4Custom3(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_submitTagJob4Custom4(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_submitEdgeSetCalcJob(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_queryEdgeSetCalcJobState(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_queryTagJob4Custom3File(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_queryTagJob4Custom4File(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_queryEdgeSetCalcJobResult(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_removeEdgeSetCalcJobResult(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_queryTagCalcResultEdgeByBrand(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_submitReadRegulerJobV2(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_queryReadRegulerJobFileV2(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_edgeSetDistinctCount(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_submitPathCalcJob(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_queryPathCalcJob(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_queryPathCalcJobListFile(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_queryFreeBroadPath(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_queryVertexCommunicatePath(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_queryVertexConnectionPath(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_sameTargetCalc(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
    process_zip(seqid: number, input: thrift.TProtocol, output: thrift.TProtocol): void;
}
