// 搜索图标组件

import React from 'react';

interface SearchIconProps {
  className?: string;
}

const SearchIcon: React.FC<SearchIconProps> = ({ className = "w-10 h-10" }) => {
  return (
    <svg className={className} viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="20" cy="20" r="18" stroke="white" strokeWidth="2" fill="none"/>
      <circle cx="20" cy="16" r="6" stroke="white" strokeWidth="2" fill="none"/>
      <path d="M14 26c0-3.314 2.686-6 6-6s6 2.686 6 6" stroke="white" strokeWidth="2" fill="none"/>
      <circle cx="30" cy="10" r="8" stroke="white" strokeWidth="2" fill="none"/>
      <circle cx="30" cy="10" r="3" fill="white"/>
    </svg>
  );
};

export default SearchIcon;
