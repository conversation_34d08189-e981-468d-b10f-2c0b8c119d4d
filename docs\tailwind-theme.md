# Tailwind CSS 主题配置文档

## 概述

本项目使用 Tailwind CSS v4 作为样式框架，并根据 Figma 设计规范配置了自定义主题。

## 更新日志

### 2024-07-24 - 按照 Figma 设计更新
- 根据 Figma Dev MCP 提供的设计规范完全重构了 Dashboard 组件
- 更新了颜色系统以匹配设计稿
- 调整了字体为 PingFang SC
- 精确匹配了布局、间距和圆角设计

## 安装和配置

### 依赖包
- `tailwindcss` - Tailwind CSS 核心包
- `@tailwindcss/vite` - Vite 插件

### 配置文件
- `vite.config.ts` - Vite 配置，包含 Tailwind 插件
- `tailwind.config.js` - Tailwind 配置文件
- `src/index.css` - 主样式文件，包含 @theme 配置

## 主题配置

### 颜色系统

根据 Figma 设计规范配置的颜色：

| 颜色名称 | 十六进制值 | Tailwind 类名 | 用途 |
|---------|-----------|--------------|------|
| Primary | #000000 | `bg-primary` | 主背景 - 黑色 |
| Card Background | rgba(60,60,60,0.8) | `bg-card-bg` | 卡片背景 - 半透明灰色 |
| Navigation | #3c3c3c | `bg-nav-bg` | 导航栏背景 |
| Highlight | #679cff | `bg-highlight`, `text-highlight` | 高亮色 - 蓝色 |
| Accent | #FB956B | `bg-accent`, `text-accent` | 强调色 - 红色 |
| Secondary | #F2BC29 | `bg-secondary`, `text-secondary` | 次要强调色 - 黄色 |
| Text Primary | #ffffff | `text-text-primary` | 主要文本 - 白色 |
| Text Secondary | #d9d9d9 | `text-text-secondary` | 次要文本 - 浅灰 |
| Border Color | #9d9d9d | `border-border-color` | 边框颜色 |

### 字体配置

- **字体族**: PingFang SC, -apple-system, BlinkMacSystemFont, Segoe UI, Arial, sans-serif
- **字体大小**:
  - `text-base` (16px) - 常规文本
  - `text-lg` (18px) - 正文文本和标签
  - `text-xl` (22px) - 标题
  - `text-4xl` (36px) - 大数字显示

### 间距系统

- **最小间距**: 10px (`space-min`)
- **图标尺寸**: 24px (`w-icon`, `h-icon`)
- **行高**: 1.5 (`leading-relaxed`)

## 使用示例

### 基本按钮
```jsx
<button className="bg-highlight hover:bg-blue-600 text-text-primary font-semibold py-3 px-6 rounded-lg transition-colors duration-200">
  主要按钮
</button>
```

### 颜色背景
```jsx
<div className="bg-primary text-text-primary p-4">
  主色背景
</div>
```

### 图标容器
```jsx
<div className="w-icon h-icon bg-highlight rounded flex items-center justify-center">
  📱
</div>
```

## 文件结构

```
src/
├── index.css          # 主样式文件，包含 @theme 配置
├── App.tsx           # 主应用组件
├── components/
│   └── ThemeDemo.tsx # 主题演示组件
└── ...

tailwind.config.js    # Tailwind 配置
vite.config.ts        # Vite 配置
```

## 开发命令

```bash
# 启动开发服务器
npm run dev

# 构建项目
npm run build

# 预览构建结果
npm run preview
```

## 注意事项

1. 使用 Tailwind CSS v4 的新语法 `@import 'tailwindcss'`
2. 主题配置使用 `@theme` 指令在 CSS 中定义
3. 自定义颜色可以通过 Tailwind 类名直接使用
4. 图标尺寸统一为 24px，符合设计规范
5. 所有颜色和尺寸都基于设计规范文档配置
