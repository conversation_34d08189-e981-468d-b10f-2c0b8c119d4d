{"name": "graph-example", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@tailwindcss/vite": "^4.1.11", "@types/react-router-dom": "^5.3.3", "buffer": "^6.0.3", "cors": "^2.8.5", "events": "^3.3.0", "express": "^5.1.0", "graphology": "^0.26.0", "graphology-layout-forceatlas2": "^0.10.1", "npm": "^11.4.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.7.1", "sigma": "^3.0.2", "stream-browserify": "^3.0.0", "thrift": "^0.22.0", "util": "^0.12.5", "vite-plugin-node-polyfills": "^0.24.0"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/thrift": "^0.10.17", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4"}}