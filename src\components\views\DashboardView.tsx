// 性能演示视图组件 - 严格按照 Figma 设计稿

import React, { useState, useCallback, useRef } from 'react';
import { useGraphQuery } from '../../hooks/useGraphQuery';
import SigmaGraphVisualization from '../SigmaGraphVisualization';
import UserSearchAutocomplete from '../UserSearchAutocomplete';
import { useToast } from '../../contexts/ToastContext';
import type { UserInfo } from '../UserSearchAutocomplete';


// 图片资源常量（来自 Figma）
const imgDataDisplay = "http://localhost:3845/assets/ee5004a96a8c0c4b1af1a6309de6085c2aa5fa28.svg";
const imgPeopleSearchOne = "http://localhost:3845/assets/ba649f452733c48712ec94c43b48a27331313092.svg";
const imgPerformanceIcon = "http://localhost:3845/assets/ad98e5de54c74e3e33ab1df8e584f39cb3f438b5.svg";

const imgPolygon = "http://localhost:3845/assets/ce2e51008691ac95212a77490d7dea0b82a0c8f9.svg";
const imgEllipse = "http://localhost:3845/assets/f2bf53e74fd66573990e7901b47fd2efda337faa.svg";

const DashboardView: React.FC = () => {
  const [selectedUser, setSelectedUser] = useState<UserInfo | null>(null);
  const [expandDepth, setExpandDepth] = useState('');
  const [recommendSimilar, setRecommendSimilar] = useState(false);
  // Toast 功能
  const { showSuccess } = useToast();

  // 查询完成回调 - 使用 useRef 来避免依赖问题
  const showSuccessRef = useRef(showSuccess);
  showSuccessRef.current = showSuccess;

  const handleQueryComplete = useCallback((duration: number) => {
    showSuccessRef.current(`计算完成！耗时 ${duration} 秒`);
  }, []);

  // 使用图查询 Hook
  const {
    data,
    loading,
    error,
    executeQuery,
    clearData,
    clearError,
  } = useGraphQuery(handleQueryComplete);
console.log('查询结果:', data);

  // 处理查询提交
  const handleStartQuery = async () => {
    if (!selectedUser) {
      alert('请先选择一个用户');
      return;
    }
    const depth = parseInt(expandDepth) || 2;
    await executeQuery(selectedUser.name, depth, recommendSimilar, selectedUser.id);
  };

  // 处理取消操作
  const handleCancel = () => {
    clearData();
    setSelectedUser(null);
    setExpandDepth('');
    setRecommendSimilar(false);
  };



  // 正常模式的渲染
  return (
    <div className="min-h-screen space-y-4">
      {/* 引言模块 - 按 Figma 尺寸：h-[117px] */}
      <div className="bg-[rgba(60,60,60,0.8)] rounded-[20px] px-8 py-4 h-[117px] flex flex-col justify-center">
        <h2 className="font-semibold text-[22px] text-[#679cff] mb-2" style={{fontFamily: 'PingFang SC'}}>
          计算引擎性能可视化演示
        </h2>
        <p className="text-[16px] text-white leading-normal" style={{fontFamily: 'PingFang SC'}}>
          本演示展现图计算引擎处理大规模复杂关系网络的高效性能。输入用户名后，系统会快速计算并呈现该用户的社交关系网络，同时推荐相似用户，计算过程与性能指标也会同步显示。
        </p>
      </div>

      {/* 主要内容区域 */}
      <div className="flex gap-4">
        {/* 左侧控制面板 - 按 Figma 尺寸：w-[560px] */}
        <div className="w-[560px] space-y-4">
          {/* 用户查询模块 - 按 Figma 尺寸：h-[338px] */}
          <div className="bg-[rgba(60,60,60,0.8)] rounded-[20px] px-8 py-6 h-[338px]">
            {/* 标题和图标 */}
            <div className="flex items-center gap-4 mb-4">
              <img src={imgPeopleSearchOne} alt="用户查询" className="w-10 h-10" />
              <h3 className="font-semibold text-[22px] text-white" style={{fontFamily: 'PingFang SC'}}>数据查询</h3>
            </div>

            {/* 用户名搜索 - 标签左，自动完成组件右 */}
            <div className="flex items-center gap-4 mb-3">
              <label className="font-semibold text-[18px] text-white w-20" style={{fontFamily: 'PingFang SC'}}>名称</label>
              <UserSearchAutocomplete
                value={selectedUser}
                onChange={setSelectedUser}
                placeholder="请输入名称搜索"
                className="w-[320px]"
              />
            </div>

            {/* 扩大深度输入 - 标签左，输入框右 */}
            <div className="flex items-center gap-4 mb-4">
              <label className="font-semibold text-[18px] text-white w-20" style={{fontFamily: 'PingFang SC'}}>扩大深度</label>
              <input
                type="text"
                value={expandDepth}
                onChange={(e) => setExpandDepth(e.target.value)}
                placeholder="请输入1~10之间的数字"
                className="h-[38px] px-4 bg-transparent w-[320px] border-2 border-white rounded-[5px] text-white placeholder-[#d9d9d9] focus:outline-none"
                style={{fontFamily: 'PingFang SC'}}
              />
            </div>

            {/* 推荐相似用户开关 */}
            <div className="flex items-center gap-4 mb-4">
              <div className="relative">
                <div
                  className={`w-20 h-10 rounded-[22px] cursor-pointer transition-colors ${
                    recommendSimilar ? 'bg-[#0090ea]' : 'bg-[#555555]'
                  }`}
                  onClick={() => setRecommendSimilar(!recommendSimilar)}
                >
                  <div className={`w-10 h-10 transition-transform ${
                    recommendSimilar ? 'translate-x-10' : 'translate-x-0'
                  }`}>
                    <img src={imgEllipse} alt="" className="w-full h-full" />
                  </div>
                </div>
              </div>
              <span className="text-[16px] text-white" style={{fontFamily: 'PingFang SC'}}>推荐相似用户</span>
            </div>

            {/* 按钮组 */}
            <div className="flex gap-4">
              <button
                onClick={handleStartQuery}
                disabled={loading || !selectedUser}
                className="w-[120px] h-10 bg-[#0090ea] rounded-[10px] text-[16px] text-white hover:bg-[#0090ea]/80 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                style={{fontFamily: 'PingFang SC'}}
              >
                {loading ? '查询中...' : '开始计算'}
              </button>

              <button
                onClick={handleCancel}
                className="w-[120px] h-10 border-2 border-[#d9d9d9] rounded-[10px] text-[16px] text-white hover:bg-white/10 transition-colors"
                style={{fontFamily: 'PingFang SC'}}
              >
                取消
              </button>

              <button className="w-[120px] h-10 border-2 border-[#d9d9d9] rounded-[10px] text-[16px] text-white hover:bg-white/10 transition-colors flex items-center justify-center gap-2" style={{fontFamily: 'PingFang SC'}}>
                更多
                <img src={imgPolygon} alt="" className="w-4 h-4 rotate-90" />
              </button>
            </div>
          </div>

          {/* 性能指标模块 - 按 Figma 尺寸：h-[457px] */}
          <div className="bg-[rgba(60,60,60,0.8)] rounded-[20px] px-8 py-6 h-[457px]">
            {/* 标题和图标 */}
            <div className="flex items-center gap-4 mb-4">
              <img src={imgPerformanceIcon} alt="性能指标" className="w-10 h-10" />
              <h3 className="font-semibold text-[22px] text-white" style={{fontFamily: 'PingFang SC'}}>性能指标</h3>
            </div>

            {/* 指标网格 */}
            <div className="grid grid-cols-2 gap-x-8 gap-y-4">
              {/* 总结点数 */}
              <div>
                <div className="font-semibold text-[18px] text-white mb-1" style={{fontFamily: 'PingFang SC'}}>总节点数</div>
                <div className="font-semibold text-[36px] text-white" style={{fontFamily: 'PingFang SC'}}>
                  {data?.statistics.totalNodes || 0}
                </div>
              </div>

              {/* 总关系数 */}
              <div>
                <div className="font-semibold text-[18px] text-white mb-1" style={{fontFamily: 'PingFang SC'}}>总关系数</div>
                <div className="font-semibold text-[36px] text-white" style={{fontFamily: 'PingFang SC'}}>
                  {data?.statistics.totalEdges || 0}
                </div>
              </div>

              {/* 最大连接数 */}
              <div>
                <div className="font-semibold text-[18px] text-white mb-1" style={{fontFamily: 'PingFang SC'}}>最大连接数</div>
                <div className="font-semibold text-[36px] text-white" style={{fontFamily: 'PingFang SC'}}>
                  {data?.statistics.maxConnections || 0}
                </div>
              </div>

              {/* 平均连接数 */}
              <div>
                <div className="font-semibold text-[18px] text-white mb-1" style={{fontFamily: 'PingFang SC'}}>平均连接数</div>
                <div className="font-semibold text-[36px] text-white" style={{fontFamily: 'PingFang SC'}}>
                  {data?.statistics.avgConnections || 0}
                </div>
              </div>

              {/* 推荐用户数 */}
              <div className="col-span-1">
                <div className="font-semibold text-[18px] text-white mb-1" style={{fontFamily: 'PingFang SC'}}>推荐用户数</div>
                <div className="font-semibold text-[36px] text-white" style={{fontFamily: 'PingFang SC'}}>
                  {data?.statistics.recommendedUsers || 0}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 右侧数据展示区域 - 按 Figma 尺寸：h-[811px] */}
        <div className="flex-1">
          <div
          id='container'
            className="bg-[rgba(60,60,60,0.8)] rounded-[20px] px-8 py-6 h-[811px] flex flex-col"
          >
            {/* 标题和工具栏 */}
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center gap-4">
                <img src={imgDataDisplay} alt="数据展示" className="w-10 h-10" />
                <h3 className="font-semibold text-[22px] text-white" style={{fontFamily: 'PingFang SC'}}>数据展示</h3>
              </div>

            
            </div>

            {/* 分割线 */}
            <div className="w-full h-0 border-t border-gray-600 mb-4" />

            {/* 图谱展示区 - 使用 SigmaJS */}
            <div className="flex-1">
              <SigmaGraphVisualization
                data={data}
                loading={loading}
                // onNodeClick={(nodeId) => console.log('点击节点:', nodeId)}
                // onEdgeClick={(edgeId) => console.log('点击边:', edgeId)}
              />
            </div>

            {/* 错误提示 */}
            {error && (
              <div className="absolute top-20 left-4 right-4 bg-red-500/80 text-white px-4 py-2 rounded z-10">
                <div className="flex justify-between items-center">
                  <span>{error}</span>
                  <button
                    onClick={clearError}
                    className="ml-4 text-white hover:text-gray-200"
                  >
                    ✕
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardView;
