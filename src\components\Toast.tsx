// 轻量级 Toast 提示组件

import React, { useEffect, useState } from 'react';

// Figma 资源
const imgRectangle74 = "http://localhost:3845/assets/30cc9d8dc4275444a1f6b2909421379d091b5183.svg";
const imgCheckOne1 = "http://localhost:3845/assets/7298776139a17561010da2ce87a78d26fd1b3bca.svg";

interface ToastProps {
  message: string;
  duration?: number; // 显示时长（毫秒）
  onClose?: () => void;
  type?: 'success' | 'info' | 'warning' | 'error';
}

const Toast: React.FC<ToastProps> = ({ 
  message, 
  duration = 3000, 
  onClose,
  type = 'success' 
}) => {
  const [isVisible, setIsVisible] = useState(true);
  const [isAnimating, setIsAnimating] = useState(false);

  useEffect(() => {
    // 入场动画
    setTimeout(() => setIsAnimating(true), 10);

    // 自动关闭
    const timer = setTimeout(() => {
      handleClose();
    }, duration);

    return () => clearTimeout(timer);
  }, [duration]);

  const handleClose = () => {
    setIsAnimating(false);
    setTimeout(() => {
      setIsVisible(false);
      onClose?.();
    }, 300); // 等待退场动画完成
  };

  if (!isVisible) return null;

  // 解析消息中的时间数字（假设格式为 "计算完成！耗时 X.XX 秒"）
  const parseMessage = (msg: string) => {
    const match = msg.match(/计算完成！耗时\s+([\d.]+)\s+秒/);
    if (match) {
      return {
        prefix: '计算完成！共耗时',
        time: match[1],
        suffix: '秒'
      };
    }
    return {
      prefix: msg,
      time: '',
      suffix: ''
    };
  };

  const { prefix, time, suffix } = parseMessage(message);

  return (
    <div
      className={`fixed top-4 right-4 z-50 transition-all duration-300 ${
        isAnimating ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'
      }`}
      style={{
        width: '800px',
        height: '200px'
      }}
    >
      {/* 背景图片 - 按 Figma 设计 */}
      <div className="absolute h-[200px] w-[800px]">
        <div className="absolute bottom-[-6%] left-[-0.5%] right-[-1.5%] top-[-2%]">
          <img
            alt=""
            className="block max-w-none size-full"
            src={imgRectangle74}
          />
        </div>
      </div>

      {/* 内容层 - 居中对齐 */}
      <div className="absolute inset-0 flex items-center justify-center">
        {/* 内容容器 */}
        <div className="flex items-center gap-4">
          {/* 勾选图标 */}
          <div className="size-10 flex-shrink-0">
            <img alt="" className="block max-w-none size-full" src={imgCheckOne1} />
          </div>

          {/* 文字内容 - 居中排列 */}
          <div className="flex items-center gap-2">
            <span
              className="font-['PingFang_SC:Semibold',_sans-serif] not-italic text-[#ffffff] text-[22px] text-nowrap"
            >
              {prefix}
            </span>

            {time && (
              <span
                className="font-['PingFang_SC:Semibold',_sans-serif] not-italic text-[#ffffff] text-[22px] text-nowrap"
              >
                {time}
              </span>
            )}

            {time && (
              <span
                className="font-['PingFang_SC:Semibold',_sans-serif] not-italic text-[#ffffff] text-[22px] text-nowrap"
              >
                {suffix}
              </span>
            )}
          </div>
        </div>

        {/* 关闭按钮 */}
        <button
          onClick={handleClose}
          className="absolute top-4 right-4 w-6 h-6 flex items-center justify-center rounded-full hover:bg-white hover:bg-opacity-20 transition-colors"
        >
          <span className="text-white text-lg">✕</span>
        </button>
      </div>
    </div>
  );
};

export default Toast;
