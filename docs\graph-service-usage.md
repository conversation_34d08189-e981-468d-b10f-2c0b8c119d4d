# 图数据服务使用指南

## 概述

本项目提供了一个完整的图数据查询服务模块，支持 Thrift 协议的图数据库操作。该服务支持两种模式：

1. **Mock 模式**: 使用内置的模拟数据，适合开发和测试
2. **生产模式**: 连接真实的 Thrift 图数据库服务

## 快速开始

### 1. 安装依赖

项目已经包含了必要的依赖，包括 `thrift` 包。

### 2. 配置环境

复制 `.env.example` 为 `.env.local` 并配置：

```bash
cp .env.example .env.local
```

编辑 `.env.local`:

```env
# 开发模式使用 Mock 数据
REACT_APP_USE_MOCK_GRAPH=true

# 生产模式的 Thrift 服务器配置
REACT_APP_THRIFT_HOST=localhost
REACT_APP_THRIFT_PORT=8080
REACT_APP_THRIFT_PROTOCOL=http
```

### 3. 在 React 组件中使用

```tsx
import React, { useEffect, useState } from 'react';
import { useGraphService } from '../services/graph';

function MyComponent() {
  const graphService = useGraphService();
  const [graphs, setGraphs] = useState<string[]>([]);

  useEffect(() => {
    const loadGraphs = async () => {
      const result = await graphService.getGraphs();
      if (result.success) {
        setGraphs(result.data);
      }
    };
    
    loadGraphs();
  }, []);

  return (
    <div>
      <h2>可用的图:</h2>
      <ul>
        {graphs.map(graph => (
          <li key={graph}>{graph}</li>
        ))}
      </ul>
    </div>
  );
}
```

## API 参考

### GraphService 主要方法

#### 基础操作

- `ping()`: 测试服务连接
- `getGraphs()`: 获取所有图列表
- `getGraphInfo(graphName)`: 获取图的详细信息
- `getVertexTypes(graphName)`: 获取图的顶点类型
- `getEdgeTypes(graphName)`: 获取图的边类型

#### 数据查询

- `getVertexCount(graphName, vertexType?)`: 获取顶点数量
- `getEdgeCount(graphName, edgeType?)`: 获取边数量
- `getVertices(graphName, vertexType?, page?)`: 分页获取顶点
- `getVertexById(graphName, vertexId)`: 根据ID获取顶点

#### 数据操作

- `createGraph(graphName)`: 创建新图
- `insertVertex(graphName, vertexType, properties)`: 插入顶点
- `insertEdge(graphName, edgeType, sourceId, targetId, properties)`: 插入边

#### 高级查询

- `query(params)`: 执行复杂图查询
- `getGraphStats(graphName)`: 获取图统计信息

### 数据类型

```typescript
interface GraphInfo {
  name: string;
  vertexTypes: string[];
  edgeTypes: string[];
  vertexCount: number;
  edgeCount: number;
}

interface Vertex {
  id: number;
  type: string;
  properties: Record<string, any>;
}

interface QueryResult<T> {
  success: boolean;
  data: T;
  error?: string;
  count?: number;
}
```

## Mock 数据

### 内置 Mock 数据

服务包含两个示例图：

1. **social_network**: 社交网络图
   - 顶点类型: User, Post, Comment
   - 边类型: follows, likes, comments
   - 包含 100 个用户和 100 个帖子

2. **knowledge_base**: 知识图谱
   - 顶点类型: Person, Organization, Location, Event
   - 边类型: works_at, located_in, participated_in
   - 包含 50 个人物和 30 个组织

### 自定义 Mock 数据

可以修改 `MockDataProvider.ts` 来添加自定义的 mock 数据：

```typescript
// 在 initializeMockData() 方法中添加新图
const customGraph: GraphInfo = {
  name: 'my_custom_graph',
  vertexTypes: ['CustomVertex'],
  edgeTypes: ['CustomEdge'],
  vertexCount: 100,
  edgeCount: 200
};

this.graphs.set('my_custom_graph', customGraph);
```

## 使用 Camouflage 进行高级 Mock

### 安装 Camouflage

```bash
npm install -g camouflage-server
```

### 启动 Camouflage 服务

```bash
camouflage --config camouflage-config.yml
```

### 创建 Thrift Mock 文件

在 `thrift/mocks` 目录下创建 mock 文件：

```
thrift/mocks/
  GraphCalculator/
    ping.mock
    showGraphs.mock
    getVertexCount.mock
```

示例 mock 文件 (`showGraphs.mock`):

```json
{
  "success": true,
  "data": ["social_network", "knowledge_base", "custom_graph"]
}
```

## 生产环境配置

### 连接真实 Thrift 服务

1. 设置环境变量：

```env
REACT_APP_USE_MOCK_GRAPH=false
REACT_APP_THRIFT_HOST=your-thrift-server.com
REACT_APP_THRIFT_PORT=9090
REACT_APP_THRIFT_PROTOCOL=https
```

2. 确保 Thrift 服务器支持 HTTP 传输协议

### 错误处理

服务会自动处理网络错误和协议错误，返回统一的 `QueryResult` 格式：

```typescript
const result = await graphService.getGraphs();
if (!result.success) {
  console.error('Error:', result.error);
  // 处理错误
} else {
  console.log('Data:', result.data);
  // 处理成功结果
}
```

## 示例组件

项目包含了一个完整的示例组件 `GraphDataExplorer`，展示了如何：

- 连接测试
- 图列表显示
- 图信息查看
- 顶点数据浏览
- 统计信息展示

可以在 `Dashboard` 组件中引入使用：

```tsx
import GraphDataExplorer from './GraphDataExplorer';

function Dashboard() {
  return (
    <div>
      <GraphDataExplorer />
    </div>
  );
}
```

## 故障排除

### 常见问题

1. **Thrift 连接失败**
   - 检查服务器地址和端口
   - 确认服务器支持 HTTP 传输
   - 检查网络连接

2. **Mock 数据不显示**
   - 确认 `REACT_APP_USE_MOCK_GRAPH=true`
   - 检查浏览器控制台错误

3. **类型错误**
   - 确认 TypeScript 配置正确
   - 检查 Thrift 生成的类型文件

### 调试技巧

1. 启用详细日志：

```typescript
const config: GraphServiceConfig = {
  useMock: false,
  thriftServer: {
    host: 'localhost',
    port: 8080,
    protocol: 'http'
  }
};

const service = createGraphService(config);
```

2. 使用浏览器开发者工具查看网络请求

3. 检查 Thrift 服务器日志

## 扩展开发

### 添加新的查询方法

1. 在 `ThriftClient.ts` 中添加 Thrift 调用
2. 在 `MockDataProvider.ts` 中添加 Mock 实现
3. 在 `GraphService.ts` 中添加统一接口
4. 更新类型定义

### 自定义配置

可以创建自定义的服务实例：

```typescript
import { createGraphService } from '../services/graph';

const customService = createGraphService({
  useMock: false,
  thriftServer: {
    host: 'custom-server.com',
    port: 9090,
    protocol: 'https'
  }
});
```
