// SigmaJS 图表可视化组件

import React, { useEffect, useRef, useState, useCallback } from "react";
import Sigma from "sigma";
import Graph from "graphology";
import forceAtlas2 from "graphology-layout-forceatlas2";
import InfoTooltip from "./InfoTooltip";
import InfoSidebar from "./InfoSidebar";
import { useFullscreen } from "../hooks/useFullscreen";
import type { GraphNode, GraphEdge, QueryResponse } from "../hooks/useGraphQuery";

// 工具栏图标
import maximizeIcon from "../assets/icons/maximize.svg";
import minimizeIcon from "../assets/icons/minimize.svg";
import imgZoomIn from "../assets/icons/zoom-in.svg";
import imgZoomOut from "../assets/icons/zoom-out.svg";
// 获取节点类型的中文标签
const getNodeTypeLabel = (nodeType: string | undefined): string => {
  switch (nodeType) {
    case 'ego':
      return '中心用户';
    case 'Circle':
      return '聚合分类';
    case 'friend':
      return '朋友节点';
    default:
      return '未知类型';
  }
};


interface SigmaGraphVisualizationProps {
  data: QueryResponse | null;
  loading?: boolean;
  onNodeClick?: (nodeId: string) => void;
  onEdgeClick?: (edgeId: string) => void;
}

const SigmaGraphVisualization: React.FC<SigmaGraphVisualizationProps> = ({ data, loading = false, onNodeClick, onEdgeClick }) => {
  const sigmaRef = useRef<Sigma | null>(null);
  const graphRef = useRef<Graph | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  // 初始化状态 - 确保 Sigma 实例完全准备好
  const [isInitialized, setIsInitialized] = useState(false);

  // 缩放状态
  const [zoomLevel, setZoomLevel] = useState(1);

  // 全屏功能
  const { isFullscreen, toggleFullscreen } = useFullscreen();

  // 缩放控制功能
  const handleZoomIn = useCallback(() => {
    if (sigmaRef.current) {
      sigmaRef.current.getCamera().animatedZoom({ duration: 300 });
      setZoomLevel((prev) => Math.round(prev * 1.2 * 10) / 10);
    }
  }, []);

  const handleZoomOut = useCallback(() => {
    if (sigmaRef.current) {
      sigmaRef.current.getCamera().animatedUnzoom({ duration: 300 });
      setZoomLevel((prev) => Math.round((prev / 1.2) * 10) / 10);
    }
  }, []);

  const handleResetZoom = useCallback(() => {
    if (sigmaRef.current) {
      sigmaRef.current.getCamera().animatedReset({ duration: 500 });
      setZoomLevel(1);
    }
  }, []);



  // 使用 useRef 稳定化回调函数
  const onNodeClickRef = useRef(onNodeClick);
  const onEdgeClickRef = useRef(onEdgeClick);
  onNodeClickRef.current = onNodeClick;
  onEdgeClickRef.current = onEdgeClick;

  // 浮窗状态
  const [tooltip, setTooltip] = useState<{
    visible: boolean;
    x: number;
    y: number;
    title: string;
    content: Record<string, any>;
    type: "node" | "edge";
  }>({
    visible: false,
    x: 0,
    y: 0,
    title: "",
    content: {},
    type: "node",
  });

  // 侧边栏状态
  const [sidebar, setSidebar] = useState<{
    visible: boolean;
    title: string;
    content: Record<string, any>;
    type: "node" | "edge";
    id: string;
  }>({
    visible: false,
    title: "",
    content: {},
    type: "node",
    id: "",
  });

  // 力导向布局函数
  const applyForceAtlas2Layout = useCallback((graph: Graph) => {
    console.log('开始应用力导向布局');

    // 为没有位置的节点设置随机初始位置 - 更小的范围让节点更聚集
    graph.forEachNode((nodeId) => {
      if (!graph.hasNodeAttribute(nodeId, 'x') || !graph.hasNodeAttribute(nodeId, 'y')) {
        graph.setNodeAttribute(nodeId, 'x', Math.random() * 200 - 100);
        graph.setNodeAttribute(nodeId, 'y', Math.random() * 200 - 100);
      }
    });

    // 配置力导向布局参数 - 调整为更紧密的簇
    const settings = {
      iterations: 80,          // 增加迭代次数，让布局更稳定
      settings: {
        gravity: 2,             // 增加重力，让节点更聚集
        scalingRatio: 2,        // 减少缩放比例，让簇更紧密
        strongGravityMode: true, // 启用强重力模式
        barnesHutOptimize: true,
        barnesHutTheta: 0.8,    // 调整精度参数
        linLogMode: false,
        adjustSizes: false,
        edgeWeightInfluence: 2,  // 增加边权重影响，让连接的节点更靠近
        slowDown: 2             // 增加阻尼，让布局更稳定
      }
    };

    // 应用力导向布局
    forceAtlas2.assign(graph, settings);

    console.log('力导向布局应用完成');
  }, []);

  // 重新应用布局
  const handleResetLayout = useCallback(() => {
    if (graphRef.current) {
      console.log('重新应用力导向布局');
      applyForceAtlas2Layout(graphRef.current);

      // 布局完成后重置视图
      setTimeout(() => {
        if (sigmaRef.current) {
          sigmaRef.current.refresh();
          sigmaRef.current.getCamera().animatedReset({ duration: 1000 });
          setZoomLevel(1);
        }
      }, 100);
    }
  }, [applyForceAtlas2Layout]);

  // 初始化图表
  useEffect(() => {
    const initializeGraph = () => {
      if (!containerRef.current) {
        // 容器还没准备好，等待下一次渲染
        requestAnimationFrame(initializeGraph);
        return;
      }

      console.log("SigmaGraphVisualization: 开始初始化图表");

      // 创建图实例
      const graph = new Graph();
      graphRef.current = graph;

      // 创建 Sigma 实例 - 使用最简配置
      const sigma = new Sigma(graph, containerRef.current, {
        renderLabels: true,
        enableEdgeEvents: true,
        renderEdgeLabels: false,
        // edgeReducer: (edge, attributes) => {
        //   const res: Partial<EdgeDisplayData> = { ...attributes };

        //   res.size = (res.size || 1) * 1.5;
        //   res.zIndex = 1;

        //   return res;
        // },
      });

      console.log("SigmaGraphVisualization: 图表初始化完成", { graph, sigma });

      sigmaRef.current = sigma;

      // 设置初始化完成状态
      setIsInitialized(true);
      console.log("SigmaGraphVisualization: 初始化状态设置为 true");

      // 添加事件监听

      // 节点悬停事件
      sigma.on("enterNode", (event) => {
        const nodeId = event.node;
        const nodeAttributes = graph.getNodeAttributes(nodeId);
        setTooltip({
          visible: true,
          x: event.event.x, // 简化坐标处理
          y: event.event.y,
          title: nodeAttributes.label || nodeId,
          content: {
            ID: nodeId,
            类型: getNodeTypeLabel(nodeAttributes.nodeType),
            大小: nodeAttributes.size,
            颜色: nodeAttributes.color,
            位置: `(${Math.round(nodeAttributes.x)}, ${Math.round(nodeAttributes.y)})`,
            特征: nodeAttributes.features ? nodeAttributes.features.join(', ') : '无',
          },
          type: "node",
        });
      });

      sigma.on("leaveNode", () => {
        setTooltip((prev) => ({ ...prev, visible: false }));
      });

      // 节点点击事件
      sigma.on("clickNode", (event) => {
        const nodeId = event.node;
        const nodeAttributes = graph.getNodeAttributes(nodeId);

        setSidebar({
          visible: true,
          title: nodeAttributes.label || nodeId,
          content: {
            节点ID: nodeId,
            类型: getNodeTypeLabel(nodeAttributes.nodeType),
            标签: nodeAttributes.label,
            特征标签: nodeAttributes.features ? nodeAttributes.features.join(', ') : '无',
            大小: nodeAttributes.size,
            颜色: nodeAttributes.color,
       
            度数: graph.degree(nodeId),
            入度: graph.inDegree(nodeId),
            出度: graph.outDegree(nodeId),
          },
          type: "node",
          id: nodeId,
        });

        console.log("点击节点:", nodeId);
        onNodeClickRef.current?.(nodeId);
      });

      // 边悬停事件
      sigma.on("enterEdge", (event) => {
        const edgeId = event.edge;
        const edgeAttributes = graph.getEdgeAttributes(edgeId);
        console.log(event);
        setTooltip({
          visible: true,
          x: event.event.x, // 简化坐标处理
          y: event.event.y,
          title: `关系 ${edgeId}`,
          content: {
            ID: edgeId,
            源节点: graph.source(edgeId),
            目标节点: graph.target(edgeId),
            权重: edgeAttributes.weight || 1,
            颜色: edgeAttributes.color,
          },
          type: "edge",
        });
      });

      sigma.on("leaveEdge", () => {
        setTooltip((prev) => ({ ...prev, visible: false }));
      });

      // 边点击事件
      sigma.on("clickEdge", (event) => {
        const edgeId = event.edge;
        const edgeAttributes = graph.getEdgeAttributes(edgeId);

        setSidebar({
          visible: true,
          title: `关系 ${edgeId}`,
          content: {
            关系ID: edgeId,
            源节点: graph.source(edgeId),
            目标节点: graph.target(edgeId),
            权重: edgeAttributes.weight || 1,
            颜色: edgeAttributes.color,
            类型: "关系边",
          },
          type: "edge",
          id: edgeId,
        });

        console.log("点击边:", edgeId);
        onEdgeClickRef.current?.(edgeId);
      });
    };
    // 开始初始化
    initializeGraph();

    // 清理函数
    return () => {
      console.log("SigmaGraphVisualization: 开始清理");
      setIsInitialized(false);
      sigmaRef.current?.kill();
      sigmaRef.current = null;
      graphRef.current = null;
      console.log("SigmaGraphVisualization: 清理完成");
    };
  }, []); // 移除依赖，避免重新初始化

  // 数据渲染方法 - 抽取为独立函数
  const renderGraphData = useCallback(
    (graphData: QueryResponse) => {
      if (!isInitialized || !graphRef.current || !sigmaRef.current) {
        console.log("Sigma 实例未初始化完成，无法渲染", {
          isInitialized,
          hasGraph: !!graphRef.current,
          hasSigma: !!sigmaRef.current,
        });
        return false;
      }

      console.log("SigmaGraphVisualization: 开始渲染图表数据", {
        nodes: graphData.nodes.length,
        edges: graphData.edges.length,
        hasGraph: !!graphRef.current,
        hasSigma: !!sigmaRef.current,
      });

      const graph = graphRef.current;
      const sigma = sigmaRef.current;

      try {
        // 清空现有数据
        graph.clear();

        // 添加节点
        graphData.nodes.forEach((node: GraphNode) => {
          graph.addNode(node.id, {
            label: node.label,
            x: node.x || Math.random() * 100,
            y: node.y || Math.random() * 100,
            size: node.size || 10,
            color: node.color || "#1D77FF",
            // 移除 type 属性，使用默认渲染器
            // type: node.type,
            nodeType: node.type,  // 保存类型信息但不用于渲染
            features: node.features || [],  // 添加 features 属性
          });
        });

        // 添加边 - 检查重复边
        graphData.edges.forEach((edge: GraphEdge) => {
          if (graph.hasNode(edge.source) && graph.hasNode(edge.target)) {
            // 检查是否已存在相同的边
            if (!graph.hasEdge(edge.source, edge.target)) {
              graph.addEdgeWithKey(edge.id, edge.source, edge.target, {
                weight: edge.weight || 1,
                color: edge.color || "#666",
              });
            } else {
              console.log(`跳过重复边: ${edge.source} -> ${edge.target}`);
            }
          }
        });

        // 应用力导向布局
        applyForceAtlas2Layout(graph);

        // 刷新渲染
        sigma.refresh();

        // 自动缩放到合适大小
        setTimeout(() => {
          sigma.getCamera().animatedReset({ duration: 1000 });
        }, 100);

        console.log("图表数据渲染完成");
        return true;
      } catch (error) {
        console.error("渲染图表数据时出错:", error);
        return false;
      }
    },
    [isInitialized, applyForceAtlas2Layout]
  );

  // 监听数据变化和初始化状态，只有在初始化完成后才渲染数据
  useEffect(() => {
    if (!data || !isInitialized) {
      console.log("数据或初始化状态未准备好", {
        hasData: !!data,
        isInitialized,
      });
      return;
    }

    console.log("数据和初始化状态都准备好，开始渲染:", data);

    // 直接渲染，不需要重试机制，因为已经确保初始化完成
    const success = renderGraphData(data);
    if (!success) {
      console.error("渲染失败，但初始化状态显示已完成，可能存在其他问题");
    }
  }, [data, isInitialized, renderGraphData]);

  // 处理加载状态
  if (loading) {
    return (
      <div className="w-full h-full flex items-center justify-center bg-gray-900 rounded-lg">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-primary-blue border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-white" style={{ fontFamily: "PingFang SC" }}>
            正在加载图数据...
          </p>
        </div>
      </div>
    );
  }

  // 处理无数据状态
  if (!data || data.nodes.length === 0) {
    return (
      <div className="w-full h-full flex items-center justify-center bg-gray-900 rounded-lg">
        <div className="text-center">
          <div className="w-16 h-16 bg-primary-blue/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-primary-blue" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
            </svg>
          </div>
          <p className="text-white text-[22px] font-semibold mb-2" style={{ fontFamily: "PingFang SC" }}>
            图谱展示区
          </p>
          <p className="text-white text-[22px]" style={{ fontFamily: "PingFang SC" }}>
            开始展示全貌...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="relative w-full h-full">
      {/* 工具栏 */}
      <div className="absolute right-0 -top-[70px] flex items-center gap-2">
        <img src={imgZoomIn} onClick={handleZoomIn} alt="放大" className="w-9 h-9 cursor-pointer" />
        <div className="font-semibold text-[18px] text-[#d9d9d9] px-3" style={{ fontFamily: "PingFang SC" }}>
          {" "}
          {Math.round(zoomLevel * 100)}%
        </div>
        <img src={imgZoomOut} onClick={handleZoomOut} alt="缩小" className="w-9 h-9 cursor-pointer" />

        {/* 全屏按钮 */}
        <button onClick={toggleFullscreen} className="w-8 h-8 cursor-pointer ml-2" title={isFullscreen ? "退出全屏" : "进入全屏"}>
          <img src={isFullscreen ? minimizeIcon : maximizeIcon} alt={isFullscreen ? "退出全屏" : "进入全屏"} className="w-full h-full" />
        </button>
      </div>

      {/* Sigma 容器 */}
      <div ref={containerRef} className="w-full h-full bg-white rounded-lg" style={{ minHeight: "400px" }} />

      {/* 信息浮窗 */}
      <InfoTooltip x={tooltip.x} y={tooltip.y} visible={tooltip.visible} title={tooltip.title} content={tooltip.content} type={tooltip.type} />

      {/* 信息侧边栏 */}
      <InfoSidebar visible={sidebar.visible} onClose={() => setSidebar((prev) => ({ ...prev, visible: false }))} title={sidebar.title} content={sidebar.content} type={sidebar.type} id={sidebar.id} />

      {/* 图表信息和图例 */}
      <div className="absolute bottom-4 left-4 bg-black/50 text-white px-3 py-2 rounded text-sm space-y-2">
        {/* 统计信息 */}
        <div className="border-b border-gray-600 pb-2">
          <div>节点: {data.nodes.length}</div>
          <div>边: {data.edges.length}</div>
        </div>

        {/* 节点类型图例 */}
        <div>
          <div className="text-xs text-gray-300 mb-1">节点类型:</div>
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded-full bg-[#3498db]"></div>
              <span className="text-xs">中心用户 (ego)</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2.5 h-2.5 rounded-full bg-[#f39c12]"></div>
              <span className="text-xs">聚合分类 (Circle)</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 rounded-full bg-[#e74c3c]"></div>
              <span className="text-xs">朋友节点 (friend)</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SigmaGraphVisualization;
