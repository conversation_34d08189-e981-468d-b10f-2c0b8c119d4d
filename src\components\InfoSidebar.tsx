// 信息侧边栏组件

import React from 'react';

interface SidebarProps {
  visible: boolean;
  onClose: () => void;
  title: string;
  content: Record<string, any>;
  type: 'node' | 'edge';
  id: string;
}

const InfoSidebar: React.FC<SidebarProps> = ({ 
  visible, 
  onClose, 
  title, 
  content, 
  type, 
  id 
}) => {
  if (!visible) return null;

  const getTypeColor = () => {
    return type === 'node' ? '#1D77FF' : '#FB956B';
  };

  const getTypeIcon = () => {
    return type === 'node' ? '●' : '—';
  };

  return (
    <>
      
      {/* 侧边栏 */}
      <div 
        className={`fixed right-0 top-0 h-full w-96 bg-gray-900 border-l border-gray-700 shadow-xl z-50 transform transition-transform duration-300 ${
          visible ? 'translate-x-0' : 'translate-x-full'
        }`}
        style={{ fontFamily: 'PingFang SC' }}
      >
        {/* 头部 */}
        <div className="flex items-center justify-between p-4 border-b border-gray-700">
          <div className="flex items-center gap-3">
            <span 
              className="text-2xl font-bold"
              style={{ color: getTypeColor() }}
            >
              {getTypeIcon()}
            </span>
            <div>
              <h3 className="text-white font-semibold text-lg">{title}</h3>
              <p className="text-gray-400 text-sm">
                {type === 'node' ? '节点信息' : '关系信息'} · ID: {id}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors p-2 rounded-lg hover:bg-gray-800"
          >
            <svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
            </svg>
          </button>
        </div>

        {/* 内容区域 */}
        <div className="p-4 overflow-y-auto h-full pb-20 flex-col">
            {/* 基本信息 */}
          <div className="mb-6">
            <h4 className="text-white font-semibold mb-3 flex items-center gap-2">
              <span className="w-2 h-2 rounded-full" style={{ backgroundColor: getTypeColor() }}></span>
              基本信息
            </h4>
            <div className="space-y-3">
              <div className="bg-gray-800 rounded-lg p-3">
                <div className="flex justify-between items-center">
                  <span className="text-gray-300 text-sm">ID</span>
                  <span className="text-white font-mono text-sm">{id}</span>
                </div>
              </div>
              <div className="bg-gray-800 rounded-lg p-3">
                <div className="flex justify-between items-center">
                  <span className="text-gray-300 text-sm">类型</span>
                  <span className="text-white text-sm">
                    {type === 'node' ? '节点' : '关系'}
                  </span>
                </div>
              </div>
              <div className="bg-gray-800 rounded-lg p-3">
                <div className="flex justify-between items-center">
                  <span className="text-gray-300 text-sm">标签</span>
                  <span className="text-white text-sm">{title}</span>
                </div>
              </div>
            </div>
          </div>

          {/* 详细属性 */}
          {Object.keys(content).length > 0 && (
            <div className="mb-6">
              <h4 className="text-white font-semibold mb-3 flex items-center gap-2">
                <span className="w-2 h-2 rounded-full bg-yellow-500"></span>
                详细属性
              </h4>
              <div className="space-y-2">
                {Object.entries(content).map(([key, value]) => (
                  <div key={key} className="bg-gray-800 rounded-lg p-3">
                    <div className="flex flex-col gap-1">
                      <span className="text-gray-300 text-sm font-medium">{key}</span>
                      <div className="text-white text-sm break-all">
                        {typeof value === 'object' ? (
                          <pre className="text-xs bg-gray-700 p-2 rounded overflow-x-auto">
                            {JSON.stringify(value, null, 2)}
                          </pre>
                        ) : (
                          <span className="font-mono">{String(value)}</span>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

   
        </div>
      </div>
    </>
  );
};

export default InfoSidebar;
