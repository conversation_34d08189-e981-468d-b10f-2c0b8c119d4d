// 用户搜索自动完成组件

import React, { useState, useEffect, useRef, useCallback } from 'react';
import graphService from '../services/graph';
import type { SimplePageQuery_Req, PageList_Res } from '../services/graph/types';

// 用户信息接口
export interface UserInfo {
  id: number;
  name: string;
  properties?: Record<string, any>;
}

interface UserSearchAutocompleteProps {
  value?: UserInfo | null;
  onChange: (user: UserInfo | null) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
}

const UserSearchAutocomplete: React.FC<UserSearchAutocompleteProps> = ({
  value,
  onChange,
  placeholder = "请输入用户名搜索",
  disabled = false,
  className = "",
}) => {
  const [inputValue, setInputValue] = useState('');
  const [suggestions, setSuggestions] = useState<UserInfo[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [highlightedIndex, setHighlightedIndex] = useState(-1);
  
  const inputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const debounceRef = useRef<NodeJS.Timeout>();

  // Mock用户数据
  const mockUsers: UserInfo[] = [
    { id: 1, name: "张伟", properties: { type: 'ego', department: '技术部', level: '高级' } },
    { id: 2, name: "王芳", properties: { type: 'ego', department: '技术部', level: '高级' } },
    { id: 3, name: "李娜", properties: { type: 'ego', department: '技术部', level: '高级' } },
    { id: 4, name: "刘强", properties: { type: 'ego', department: '技术部', level: '高级' } },
    { id: 5, name: "陈静", properties: { type: 'ego', department: '技术部', level: '高级' } },
    { id: 6, name: "杨洋", properties: { type: 'ego', department: '产品部', level: '中级' } },
    { id: 7, name: "赵敏", properties: { type: 'ego', department: '产品部', level: '中级' } },
    { id: 8, name: "黄磊", properties: { type: 'ego', department: '产品部', level: '中级' } },
    { id: 9, name: "周杰", properties: { type: 'ego', department: '产品部', level: '中级' } },
    { id: 10, name: "吴彦祖", properties: { type: 'ego', department: '产品部', level: '中级' } },
    { id: 11, name: "徐静蕾", properties: { type: 'ego', department: '设计部', level: '中级' } },
    { id: 12, name: "孙俪", properties: { type: 'ego', department: '设计部', level: '中级' } },
    { id: 13, name: "朱亚文", properties: { type: 'ego', department: '设计部', level: '中级' } },
    { id: 14, name: "马伊琍", properties: { type: 'ego', department: '设计部', level: '中级' } },
    { id: 15, name: "冯绍峰", properties: { type: 'ego', department: '设计部', level: '中级' } },
    { id: 16, name: "袁泉", properties: { type: 'ego', department: '运营部', level: '初级' } },
    { id: 17, name: "胡歌", properties: { type: 'ego', department: '运营部', level: '初级' } },
    { id: 18, name: "刘诗诗", properties: { type: 'ego', department: '运营部', level: '初级' } },
    { id: 19, name: "唐嫣", properties: { type: 'ego', department: '运营部', level: '初级' } },
    { id: 20, name: "杨幂", properties: { type: 'ego', department: '运营部', level: '初级' } }
  ];

  // 当外部 value 变化时，更新输入框显示
  useEffect(() => {
    if (value) {
      setInputValue(value.name);
    } else {
      setInputValue('');
    }
  }, [value]);

  // 防抖搜索函数 - 使用 simplePageQuery
  const debouncedSearch = useCallback(async (searchTerm: string) => {
    if (searchTerm.trim().length < 1) {
      setSuggestions([]);
      setIsOpen(false);
      return;
    }

    setLoading(true);
    try {
      console.log('搜索用户:', searchTerm);

      // 先获取图列表
      const graphs = await graphService.getGraphs();
      const graphName = Array.isArray(graphs) && graphs.length > 0 ? graphs[0] : 'default';

      // 构建 simplePageQuery 请求
      const queryReq: SimplePageQuery_Req = {
        Ontology: graphName,
        veName: 'User', // 假设用户节点类型为 User
        vertexFlag: true, // 查询节点
        filter: {
          filterMap: {
            name: searchTerm // 按名称模糊搜索
          },
          targetFilter: null
        },
        pageSize: 10, // 最多返回10个结果
        scrollId: -1 // 第一页
      };

      console.log('simplePageQuery 请求:', queryReq);

      let result: PageList_Res;
      try {
        result = await graphService.simplePageQuery(queryReq);
        console.log('simplePageQuery 结果:', result);
        console.log('结果类型:', typeof result);
        console.log('结果 JSON:', JSON.stringify(result, null, 2));
      } catch (queryError) {
        console.error('simplePageQuery 执行失败:', queryError);
        throw queryError;
      }

      // 将 ID 列表转换为用户信息（GraphService 已经处理了 Buffer 转换）
      const users: UserInfo[] = [];
      if (result.ids && Array.isArray(result.ids) && result.ids.length > 0) {
        console.log('处理用户 IDs:', result.ids);

        // 现在 IDs 应该已经是数字了
        for (const id of result.ids.slice(0, 10)) { // 限制最多10个
          if (typeof id === 'number') {
            users.push({
              id: id,
              name: `用户 ${id}`,
              properties: {}
            });
          } else {
            console.warn('ID 仍然不是数字格式:', id, typeof id);
          }
        }
      } else {
        console.log('没有找到用户 IDs 或格式不正确');
      }

      console.log('转换后的用户列表:', users);
      setSuggestions(users);
      setIsOpen(users.length > 0);
      setHighlightedIndex(-1);
    } catch (error) {
      console.error('simplePageQuery 用户搜索失败:', error);

      // 回退到原来的查询方法
      try {
        console.log('尝试使用 userQueryService.queryUserByName 作为回退方案');
        const { userQueryService } = await import('../services/graph/UserQueryService');
        const users = await userQueryService.queryUserByName(searchTerm);
        console.log('回退查询结果:', users);

        setSuggestions(users);
        setIsOpen(users.length > 0);
        setHighlightedIndex(-1);
      } catch (fallbackError) {
        console.error('回退查询也失败:', fallbackError);
        setSuggestions([]);
        setIsOpen(false);
      }
    } finally {
      setLoading(false);
    }
  }, []);

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);
    
    // 如果输入值变化，清除当前选择
    if (value && newValue !== value.name) {
      onChange(null);
    }

    // 清除之前的防抖定时器
    if (debounceRef.current) {
      clearTimeout(debounceRef.current);
    }

    // 设置新的防抖定时器
    debounceRef.current = setTimeout(() => {
      // debouncedSearch(newValue);
      // mock数据
      mockUserSearch(newValue);
    }, 300);
  };

  // 处理选择用户
  const handleSelectUser = (user: UserInfo) => {
    setInputValue(user.name);
    setSuggestions([]);
    setIsOpen(false);
    setHighlightedIndex(-1);
    onChange(user);
  };

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen || suggestions.length === 0) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setHighlightedIndex(prev => 
          prev < suggestions.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setHighlightedIndex(prev => 
          prev > 0 ? prev - 1 : suggestions.length - 1
        );
        break;
      case 'Enter':
        e.preventDefault();
        if (highlightedIndex >= 0 && highlightedIndex < suggestions.length) {
          handleSelectUser(suggestions[highlightedIndex]);
        }
        break;
      case 'Escape':
        setIsOpen(false);
        setHighlightedIndex(-1);
        break;
    }
  };

  // 处理失去焦点
  const handleBlur = () => {
    // 延迟关闭，允许点击下拉项
    setTimeout(() => {
      setIsOpen(false);
      setHighlightedIndex(-1);
    }, 200);
  };

  // 处理获得焦点
  const handleFocus = () => {
    if (suggestions.length > 0) {
      setIsOpen(true);
    }
  };

  // 清理定时器
  useEffect(() => {
    return () => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current);
      }
    };
  }, []);

  return (
    <div className={`relative ${className}`}>
      {/* 输入框 */}
      <div className="relative">
        <input
          ref={inputRef}
          type="text"
          value={inputValue}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onBlur={handleBlur}
          onFocus={handleFocus}
          placeholder={placeholder}
          disabled={disabled}
          className="w-full h-[38px] px-4 pr-10 bg-transparent border-2 border-white rounded-[5px] text-white placeholder-[#d9d9d9] focus:outline-none focus:border-[#0090ea] disabled:opacity-50"
          style={{fontFamily: 'PingFang SC'}}
        />
        
        {/* 加载指示器 */}
        {loading && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
          </div>
        )}
        
        {/* 下拉箭头 */}
        {!loading && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-white">
            <svg className="w-4 h-4" viewBox="0 0 24 24" fill="currentColor">
              <path d="M7 10l5 5 5-5z"/>
            </svg>
          </div>
        )}
      </div>

      {/* 下拉列表 */}
      {isOpen && suggestions.length > 0 && (
        <div 
          ref={dropdownRef}
          className="absolute z-50 w-full mt-1 bg-gray-800 border border-gray-600 rounded-[5px] shadow-lg max-h-60 overflow-y-auto"
        >
          {suggestions.map((user, index) => (
            <div
              key={user.id}
              className={`px-4 py-3 cursor-pointer transition-colors ${
                index === highlightedIndex 
                  ? 'bg-[#0090ea] text-white' 
                  : 'text-white hover:bg-gray-700'
              }`}
              onClick={() => handleSelectUser(user)}
            >
              <div className="font-medium" style={{fontFamily: 'PingFang SC'}}>
                {user.name}
              </div>
              <div className="text-sm text-gray-400" style={{fontFamily: 'PingFang SC'}}>
                ID: {user.id}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* 无结果提示 */}
      {isOpen && !loading && inputValue.trim() && suggestions.length === 0 && (
        <div className="absolute z-50 w-full mt-1 bg-gray-800 border border-gray-600 rounded-[5px] shadow-lg">
          <div className="px-4 py-3 text-gray-400" style={{fontFamily: 'PingFang SC'}}>
            未找到匹配的用户
          </div>
        </div>
      )}
    </div>
  );
};

export default UserSearchAutocomplete;
