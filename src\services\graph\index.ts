// 图服务模块的统一导出

import * as thrift from 'thrift';
// @ts-ignore - Generated thrift file doesn't have proper types
import { Client as GraphCalculatorClient } from '../../../rpc/gen-nodejs/GraphCalculator.mjs';

console.log('Graph service imports successful');

const host = 'localhost';
const port = 8080;
const options = {
  protocol: thrift.TJSONProtocol,
  path: '/graphcalculator',
  headers: {
    'Content-Type': 'application/vnd.apache.thrift.json',
  },
  useCORS: true,
};

let connectionError: Error | null = null;

// Create a Thrift connection and client
function createThriftClient() {
  const connection = thrift.createXHRConnection(host, port, options);
  const client = thrift.createXHRClient(GraphCalculatorClient, connection);

  console.log('Graph client created');

  // Handle connection errors
  connection.on('error', (error) => {
    console.error('Thrift connection error:', error);
    connectionError = error;
  });

  return client;
}

class GraphService {
  private client: any;

  constructor() {
    this.client = createThriftClient();
  }

  // Method to call showGraphs
  async getGraphs() {
    const timeout = new Promise((_, reject) => {
      setTimeout(() => {
        if (connectionError) {
          reject(new Error(`showGraphs request timed out. Connection error: ${connectionError.message}`));
        } else {
          reject(new Error('showGraphs request timed out'));
        }
      }, 5000); // Set timeout to 5 seconds
    });

    // Wrap the Q.defer().promise in a native Promise
    const showGraphsPromise = new Promise((resolve, reject) => {
      this.client.showGraphs()
        .then(resolve)
        .catch(reject);
    });

    // Use Promise.race to handle timeout
    return Promise.race([showGraphsPromise, timeout]);
  }

  // Method to ping the service
  async ping() {
    const timeout = new Promise((_, reject) => {
      setTimeout(() => {
        if (connectionError) {
          reject(new Error(`ping request timed out. Connection error: ${connectionError.message}`));
        } else {
          reject(new Error('ping request timed out'));
        }
      }, 5000);
    });

    const pingPromise = new Promise((resolve, reject) => {
      this.client.ping()
        .then(resolve)
        .catch(reject);
    });

    return Promise.race([pingPromise, timeout]);
  }

  // Method to get vertex types
  async getVertexTypes(graphName: string) {
    const timeout = new Promise((_, reject) => {
      setTimeout(() => {
        if (connectionError) {
          reject(new Error(`showVertexes request timed out. Connection error: ${connectionError.message}`));
        } else {
          reject(new Error(`showVertexes request timed out for graph: ${graphName}`));
        }
      }, 5000);
    });

    const showVertexesPromise = new Promise((resolve, reject) => {
      this.client.showVertexes(graphName)
        .then(resolve)
        .catch(reject);
    });

    return Promise.race([showVertexesPromise, timeout]);
  }

  // Method to get edge types
  async getEdgeTypes(graphName: string) {
    const timeout = new Promise((_, reject) => {
      setTimeout(() => {
        if (connectionError) {
          reject(new Error(`showEdges request timed out. Connection error: ${connectionError.message}`));
        } else {
          reject(new Error(`showEdges request timed out for graph: ${graphName}`));
        }
      }, 5000);
    });

    const showEdgesPromise = new Promise((resolve, reject) => {
      this.client.showEdges(graphName)
        .then(resolve)
        .catch(reject);
    });

    return Promise.race([showEdgesPromise, timeout]);
  }

  // Method to get vertex count
  async getVertexCount(graphName: string, vertexType: string) {
    const timeout = new Promise((_, reject) => {
      setTimeout(() => {
        if (connectionError) {
          reject(new Error(`getVertexCount request timed out. Connection error: ${connectionError.message}`));
        } else {
          reject(new Error(`getVertexCount request timed out for graph: ${graphName}, type: ${vertexType}`));
        }
      }, 5000);
    });

    const getVertexCountPromise = new Promise((resolve, reject) => {
      this.client.getVertexCount(graphName, vertexType)
        .then(resolve)
        .catch(reject);
    });

    return Promise.race([getVertexCountPromise, timeout]);
  }

  // Method to get edge count
  async getEdgeCount(graphName: string, edgeType: string) {
    const timeout = new Promise((_, reject) => {
      setTimeout(() => {
        if (connectionError) {
          reject(new Error(`getEdgeCount request timed out. Connection error: ${connectionError.message}`));
        } else {
          reject(new Error(`getEdgeCount request timed out for graph: ${graphName}, type: ${edgeType}`));
        }
      }, 5000);
    });

    const getEdgeCountPromise = new Promise((resolve, reject) => {
      this.client.getEdgeCount(graphName, edgeType)
        .then(resolve)
        .catch(reject);
    });

    return Promise.race([getEdgeCountPromise, timeout]);
  }

  // Method to scan vertices
  async scanVertex(graphName: string, vertexType: string, offset: number, count: number) {
    const timeout = new Promise((_, reject) => {
      setTimeout(() => {
        if (connectionError) {
          reject(new Error(`scanVertex request timed out. Connection error: ${connectionError.message}`));
        } else {
          reject(new Error(`scanVertex request timed out for graph: ${graphName}, type: ${vertexType}`));
        }
      }, 5000);
    });

    const scanVertexPromise = new Promise((resolve, reject) => {
      this.client.scanVertex(graphName, vertexType, offset, count)
        .then(resolve)
        .catch(reject);
    });

    return Promise.race([scanVertexPromise, timeout]);
  }

  // Method to create new graph
  async createGraph(graphName: string) {
    const timeout = new Promise((_, reject) => {
      setTimeout(() => {
        if (connectionError) {
          reject(new Error(`makeNewGraph request timed out. Connection error: ${connectionError.message}`));
        } else {
          reject(new Error(`makeNewGraph request timed out for graph: ${graphName}`));
        }
      }, 5000);
    });

    const makeNewGraphPromise = new Promise((resolve, reject) => {
      this.client.makeNewGraph(graphName)
        .then(resolve)
        .catch(reject);
    });

    return Promise.race([makeNewGraphPromise, timeout]);
  }

  // Method to insert vertex
  async insertVertex(graphName: string, vertexType: string, vertexJson: string) {
    const timeout = new Promise((_, reject) => {
      setTimeout(() => {
        if (connectionError) {
          reject(new Error(`insertVertex request timed out. Connection error: ${connectionError.message}`));
        } else {
          reject(new Error(`insertVertex request timed out for graph: ${graphName}, type: ${vertexType}`));
        }
      }, 5000);
    });

    const insertVertexPromise = new Promise((resolve, reject) => {
      this.client.insertVertex(graphName, vertexType, vertexJson)
        .then(resolve)
        .catch(reject);
    });

    return Promise.race([insertVertexPromise, timeout]);
  }

  // Method to insert edge
  async insertEdge(graphName: string, edgeType: string, edgeJson: string) {
    const timeout = new Promise((_, reject) => {
      setTimeout(() => {
        if (connectionError) {
          reject(new Error(`insertEdge request timed out. Connection error: ${connectionError.message}`));
        } else {
          reject(new Error(`insertEdge request timed out for graph: ${graphName}, type: ${edgeType}`));
        }
      }, 5000);
    });

    const insertEdgePromise = new Promise((resolve, reject) => {
      this.client.insertEdge(graphName, edgeType, edgeJson)
        .then(resolve)
        .catch(reject);
    });

    return Promise.race([insertEdgePromise, timeout]);
  }

  // Method for filter query (用于复杂查询)
  async filterQuery(queryReq: any, midFilterFlag: number = 0) {
    const timeout = new Promise((_, reject) => {
      setTimeout(() => {
        if (connectionError) {
          reject(new Error(`filterQuery request timed out. Connection error: ${connectionError.message}`));
        } else {
          reject(new Error('filterQuery request timed out'));
        }
      }, 10000); // 复杂查询使用更长的超时时间
    });

    const filterQueryPromise = new Promise((resolve, reject) => {
      this.client.filterQuery(queryReq, midFilterFlag)
        .then(resolve)
        .catch(reject);
    });

    return Promise.race([filterQueryPromise, timeout]);
  }

  // Method for regular query (常规查询)
  async regulerQuery(queryReq: any) {
    const timeout = new Promise((_, reject) => {
      setTimeout(() => {
        if (connectionError) {
          reject(new Error(`regulerQuery request timed out. Connection error: ${connectionError.message}`));
        } else {
          reject(new Error('regulerQuery request timed out'));
        }
      }, 10000);
    });

    const regulerQueryPromise = new Promise((resolve, reject) => {
      this.client.regulerQuery(queryReq)
        .then(resolve)
        .catch(reject);
    });

    return Promise.race([regulerQueryPromise, timeout]);
  }

  // Method for recommend calculation (推荐计算)
  async recommendCalc(queryReq: any) {
    const timeout = new Promise((_, reject) => {
      setTimeout(() => {
        if (connectionError) {
          reject(new Error(`recommendCalc request timed out. Connection error: ${connectionError.message}`));
        } else {
          reject(new Error('recommendCalc request timed out'));
        }
      }, 15000); // 推荐算法需要更长时间
    });

    const recommendCalcPromise = new Promise((resolve, reject) => {
      this.client.recommendCalc(queryReq)
        .then(resolve)
        .catch(reject);
    });

    return Promise.race([recommendCalcPromise, timeout]);
  }

  // Method for similar mutual calculation (相似度计算)
  async similarMutualCalc(queryReq: any) {
    const timeout = new Promise((_, reject) => {
      setTimeout(() => {
        if (connectionError) {
          reject(new Error(`similarMutualCalc request timed out. Connection error: ${connectionError.message}`));
        } else {
          reject(new Error('similarMutualCalc request timed out'));
        }
      }, 15000);
    });

    const similarMutualCalcPromise = new Promise((resolve, reject) => {
      this.client.similarMutualCalc(queryReq)
        .then(resolve)
        .catch(reject);
    });

    return Promise.race([similarMutualCalcPromise, timeout]);
  }

  // Method for path finding calculation (路径查找)
  async pathFindCalc(queryReq: any) {
    const timeout = new Promise((_, reject) => {
      setTimeout(() => {
        if (connectionError) {
          reject(new Error(`pathFindCalc request timed out. Connection error: ${connectionError.message}`));
        } else {
          reject(new Error('pathFindCalc request timed out'));
        }
      }, 15000);
    });

    const pathFindCalcPromise = new Promise((resolve, reject) => {
      this.client.pathFindCalc(queryReq)
        .then(resolve)
        .catch(reject);
    });

    return Promise.race([pathFindCalcPromise, timeout]);
  }

  // Method to get vertex info by ID
  async getVertexInfoById(graphName: string, vertexType: string, vertexId: number) {
    const timeout = new Promise((_, reject) => {
      setTimeout(() => {
        if (connectionError) {
          reject(new Error(`getVertexInfoById request timed out. Connection error: ${connectionError.message}`));
        } else {
          reject(new Error(`getVertexInfoById request timed out for graph: ${graphName}, vertex: ${vertexId}`));
        }
      }, 5000);
    });

    const getVertexInfoPromise = new Promise((resolve, reject) => {
      this.client.getVertexInfoById(graphName, vertexType, vertexId)
        .then(resolve)
        .catch(reject);
    });

    return Promise.race([getVertexInfoPromise, timeout]);
  }

  // Method for simple page query (简单分页查询)
  async simplePageQuery(queryReq: any) {
    console.log('GraphService.simplePageQuery 开始执行，请求参数:', queryReq);

    const timeout = new Promise((_, reject) => {
      setTimeout(() => {
        if (connectionError) {
          reject(new Error(`simplePageQuery request timed out. Connection error: ${connectionError.message}`));
        } else {
          reject(new Error('simplePageQuery request timed out'));
        }
      }, 15000); // 增加超时时间到15秒
    });

    const simplePageQueryPromise = new Promise((resolve, reject) => {
      console.log('调用 this.client.simplePageQuery...');

      // 尝试捕获更底层的错误
      try {
        this.client.simplePageQuery(queryReq)
          .then((result) => {
       
            resolve(result);
          })
          .catch((error) => {
     

            // 如果是 JSON 解析错误，尝试获取原始响应
            if (error.message && error.message.includes('Unexpected')) {
              console.error('这是一个 JSON 解析错误，可能是服务器返回了非 JSON 格式的数据');
            }

            reject(error);
          });
      } catch (syncError) {
        console.error('同步调用错误:', syncError);
        reject(syncError);
      }
    });

    try {
      const rawResult = await Promise.race([simplePageQueryPromise, timeout]);
      console.log('simplePageQuery 原始结果:', rawResult);

      // 处理返回的数据，将 Buffer 格式的 ID 转换为数字
      const processedResult = this.processPageListResult(rawResult);
      console.log('simplePageQuery 处理后结果:', processedResult);

      return processedResult;
    } catch (error) {
      console.error('simplePageQuery 执行失败:', error);
      throw error;
    }
  }

  // 处理 PageList_Res 结果，将 Buffer 格式的 ID 转换为数字
  private processPageListResult(rawResult: any): any {
    if (!rawResult || !rawResult.ids || !Array.isArray(rawResult.ids)) {
      return rawResult;
    }

    const processedIds: number[] = [];

    for (const idObj of rawResult.ids) {
      let actualId: number;

      if (typeof idObj === 'number') {
        // 如果是直接的数字
        actualId = idObj;
      } else if (idObj && typeof idObj === 'object' && idObj.buffer) {
        // 如果是 Buffer 对象，需要转换为数字
        try {
          const buffer = idObj.buffer;
          console.log('Buffer 对象详情:', idObj);
          console.log('Buffer 类型:', buffer.constructor.name);
          console.log('Buffer 内容:', buffer);

          if (buffer instanceof Uint8Array) {
            // 处理 Uint8Array 格式的 Buffer（64 位整数的大端序）
            let value = 0;
            for (let i = 0; i < buffer.length; i++) {
              value = value * 256 + buffer[i];
            }
            actualId = value;
            console.log(`Uint8Array [${Array.from(buffer).join(',')}] 转换为 ID: ${actualId}`);
          } else if (buffer.type === 'Buffer' && Array.isArray(buffer.data)) {
            // 处理传统的 Buffer 格式
            let value = 0;
            for (let i = 0; i < buffer.data.length; i++) {
              value = value * 256 + buffer.data[i];
            }
            actualId = value;
            console.log(`Buffer ${JSON.stringify(buffer.data)} 转换为 ID: ${actualId}`);
          } else {
            console.warn('未知的 Buffer 格式:', idObj);
            console.warn('Buffer 详情:', buffer);
            continue;
          }
        } catch (error) {
          console.error('转换 Buffer ID 失败:', error, idObj);
          continue;
        }
      } else {
        console.warn('未知的 ID 格式:', idObj);
        continue;
      }

      processedIds.push(actualId);
    }

    return {
      ...rawResult,
      ids: processedIds
    };
  }
}

// Create and export the default instance
const graphService = new GraphService();
export default graphService;

// Also export the class for custom instances
export { GraphService };
