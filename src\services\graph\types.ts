// 图数据服务相关的 TypeScript 类型定义

// Thrift 生成的类型定义（手动定义以避免导入问题）
export interface VertexPk {
  id: number;
  id_l: number;
  id_s: string;
  vertexName: string;
}

export interface EdgePk {
  id: number;
  edgeName: string;
  srcid: number;
  dstid: number;
}

export interface VertexID {
  id: number;
  name: string;
}

export interface EPair {
  eid: number;
  vid: number;
}

export interface ReadFilterQuery_Req {
  graph: string;
  startVertex: string;
  startIds: number[];
  throughEdges: string[];
  targetEdgeNodeName: string;
  isReturnEdge: number;
  count: number;
  stepLayerLimit: number;
  filterMap: Record<string, any>;
  targetFilter: any;
}

export interface ReadRegulerQuery_Req {
  graph: string;
  startVertex: string;
  startIds: number[];
  throughEdges: string[];
  targetEdgeNodeName: string;
  isReturnEdge: number;
  count: number;
  filterVector: any[];
}

export interface GraphEntityResult_Res {
  vids: VertexPk[];
  eids: EdgePk[];
}

export interface PageList_Res {
  ids: number[];
  scrollId: number;
}

// 过滤条件集合
export interface ReadFilterSet_Req {
  filterMap: Record<string, any>;
  targetFilter?: any;
}

// 简单分页查询请求
export interface SimplePageQuery_Req {
  Ontology: string;        // 本体名称标识
  veName: string;          // 点或边的名称
  vertexFlag: boolean;     // 是否是点
  filter: ReadFilterSet_Req; // 过滤条件
  pageSize: number;        // 每页数量
  scrollId: number;        // 翻页参数，第一页传-1
}

// 服务配置
export interface GraphServiceConfig {
  useMock: boolean;
  thriftServer?: {
    host: string;
    port: number;
    protocol: 'http' | 'https';
  };
}

// 图的基本信息
export interface GraphInfo {
  name: string;
  vertexTypes: string[];
  edgeTypes: string[];
  vertexCount: number;
  edgeCount: number;
}

// 顶点数据
export interface Vertex {
  id: number;
  type: string;
  properties: Record<string, any>;
}

// 边数据
export interface Edge {
  id: number;
  type: string;
  sourceId: number;
  targetId: number;
  properties: Record<string, any>;
}

// 查询结果
export interface QueryResult<T = any> {
  success: boolean;
  data: T;
  error?: string;
  count?: number;
}

// 分页查询参数
export interface PageQuery {
  offset: number;
  limit: number;
}

// 过滤条件
export interface FilterCondition {
  field: string;
  operator: 'eq' | 'ne' | 'gt' | 'gte' | 'lt' | 'lte' | 'in' | 'contains';
  value: any;
}

// 图查询参数
export interface GraphQueryParams {
  graph: string;
  startVertex?: string;
  startIds?: number[];
  throughEdges?: string[];
  targetVertex?: string;
  filters?: FilterCondition[];
  page?: PageQuery;
}

// 路径查询结果
export interface PathResult {
  path: number[];
  edges: number[];
  length: number;
}

// 统计信息
export interface GraphStats {
  graphName: string;
  vertexCount: number;
  edgeCount: number;
  vertexTypes: Record<string, number>;
  edgeTypes: Record<string, number>;
}
