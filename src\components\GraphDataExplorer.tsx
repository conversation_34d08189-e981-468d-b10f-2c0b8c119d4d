// 图数据浏览器组件 - 展示如何使用图服务

import React, { useState, useEffect } from 'react';
import graphService from '../services/graph';

interface GraphDataExplorerProps {
  className?: string;
}

const GraphDataExplorer: React.FC<GraphDataExplorerProps> = ({ className = '' }) => {
  // 状态管理
  const [graphs, setGraphs] = useState<string[]>([]);
  const [selectedGraph, setSelectedGraph] = useState<string>('');
  const [vertexTypes, setVertexTypes] = useState<string[]>([]);
  const [edgeTypes, setEdgeTypes] = useState<string[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string>('');
  const [pingResult, setPingResult] = useState<string>('');

  // 初始化加载
  useEffect(() => {
    testPing();
    loadGraphs();
  }, []);

  // 当选择的图改变时，加载图信息
  useEffect(() => {
    if (selectedGraph) {
      loadVertexTypes(selectedGraph);
      loadEdgeTypes(selectedGraph);
    }
  }, [selectedGraph]);

  const testPing = async () => {
    try {
      await graphService.ping();
      setPingResult('✅ 连接成功');
    } catch (err) {
      setPingResult(`❌ 连接失败: ${err}`);
    }
  };

  const loadGraphs = async () => {
    setLoading(true);
    setError('');

    try {
      const result = await graphService.getGraphs();
      const graphList = Array.isArray(result) ? result : [];
      setGraphs(graphList);
      if (graphList.length > 0) {
        setSelectedGraph(graphList[0]);
      }
    } catch (err) {
      setError(`Error loading graphs: ${err}`);
    } finally {
      setLoading(false);
    }
  };

  const loadVertexTypes = async (graphName: string) => {
    try {
      const result = await graphService.getVertexTypes(graphName);
      const vertexTypeList = Array.isArray(result) ? result : [];
      setVertexTypes(vertexTypeList);
    } catch (err) {
      console.error(`Error loading vertex types: ${err}`);
    }
  };

  const loadEdgeTypes = async (graphName: string) => {
    try {
      const result = await graphService.getEdgeTypes(graphName);
      const edgeTypeList = Array.isArray(result) ? result : [];
      setEdgeTypes(edgeTypeList);
    } catch (err) {
      console.error(`Error loading edge types: ${err}`);
    }
  };

  const createNewGraph = async () => {
    const graphName = prompt('请输入新图的名称:');
    if (graphName) {
      try {
        await graphService.createGraph(graphName);
        alert('图创建成功!');
        loadGraphs(); // 重新加载图列表
      } catch (err) {
        alert(`创建图失败: ${err}`);
      }
    }
  };

  return (
    <div className={`p-6 bg-primary-dark rounded-lg shadow-lg border border-gray-800 ${className}`}>
      <h2 className="text-spec-header font-bold mb-6 text-text-primary font-arial">图数据浏览器</h2>

      {/* 连接状态 */}
      <div className="mb-4 p-3 bg-gray-900 rounded border border-gray-700">
        <h3 className="font-semibold mb-2 text-text-primary font-arial">连接状态:</h3>
        <p className="text-spec-regular text-text-secondary font-arial">{pingResult || '检测中...'}</p>
      </div>

      {/* 错误信息 */}
      {error && (
        <div className="mb-4 p-3 bg-primary-orange/10 border border-primary-orange/30 rounded text-primary-orange font-arial">
          {error}
        </div>
      )}

      {/* 加载状态 */}
      {loading && (
        <div className="mb-4 p-3 bg-primary-blue/10 border border-primary-blue/30 rounded text-primary-blue font-arial">
          加载中...
        </div>
      )}

      {/* 图列表 */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-spec-body font-semibold text-text-primary font-arial">图列表</h3>
          <button
            onClick={createNewGraph}
            className="px-4 py-2 bg-primary-blue text-text-primary rounded hover:bg-primary-blue/80 transition-colors duration-200 font-arial font-semibold"
          >
            创建新图
          </button>
        </div>

        {graphs.length > 0 ? (
          <select
            value={selectedGraph}
            onChange={(e) => setSelectedGraph(e.target.value)}
            className="w-full p-2 bg-gray-900 border border-gray-700 rounded text-text-primary focus:border-primary-blue focus:outline-none font-arial"
          >
            {graphs.map((graph) => (
              <option key={graph} value={graph} className="bg-gray-900 text-text-primary">
                {graph}
              </option>
            ))}
          </select>
        ) : (
          <p className="text-text-secondary font-arial">暂无图数据</p>
        )}
      </div>

      {/* 图信息 */}
      {selectedGraph && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* 顶点类型 */}
          <div>
            <h4 className="text-spec-regular font-semibold mb-3 text-text-primary font-arial">顶点类型</h4>
            {vertexTypes.length > 0 ? (
              <ul className="space-y-1">
                {vertexTypes.map((type) => (
                  <li key={type} className="p-2 bg-primary-yellow/10 border border-primary-yellow/30 rounded text-spec-regular text-primary-yellow font-arial">
                    {type}
                  </li>
                ))}
              </ul>
            ) : (
              <p className="text-text-secondary text-spec-regular font-arial">暂无顶点类型</p>
            )}
          </div>

          {/* 边类型 */}
          <div>
            <h4 className="text-spec-regular font-semibold mb-3 text-text-primary font-arial">边类型</h4>
            {edgeTypes.length > 0 ? (
              <ul className="space-y-1">
                {edgeTypes.map((type) => (
                  <li key={type} className="p-2 bg-primary-orange/10 border border-primary-orange/30 rounded text-spec-regular text-primary-orange font-arial">
                    {type}
                  </li>
                ))}
              </ul>
            ) : (
              <p className="text-text-secondary text-spec-regular font-arial">暂无边类型</p>
            )}
          </div>
        </div>
      )}

      {/* 操作按钮 */}
      <div className="mt-6 flex space-x-4">
        <button
          onClick={testPing}
          className="px-4 py-2 bg-primary-yellow text-primary-dark rounded hover:bg-primary-yellow/80 transition-colors duration-200 font-semibold font-arial text-spec-regular"
        >
          测试连接
        </button>
        <button
          onClick={loadGraphs}
          className="px-4 py-2 bg-primary-blue text-text-primary rounded hover:bg-primary-blue/80 transition-colors duration-200 font-semibold font-arial text-spec-regular"
        >
          刷新图列表
        </button>
      </div>
    </div>
  );
};

export default GraphDataExplorer;