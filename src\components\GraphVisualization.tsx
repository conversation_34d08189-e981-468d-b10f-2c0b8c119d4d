import React, { useEffect, useRef, useState } from 'react';

interface Node {
  id: string;
  x: number;
  y: number;
  radius: number;
  color: string;
  label: string;
}

interface Edge {
  from: string;
  to: string;
}

interface GraphVisualizationProps {
  zoomLevel: number;
  isActive: boolean;
}

const GraphVisualization: React.FC<GraphVisualizationProps> = ({ zoomLevel, isActive }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [nodes, setNodes] = useState<Node[]>([]);
  const [edges, setEdges] = useState<Edge[]>([]);
  const [animationFrame, setAnimationFrame] = useState<number>(0);

  // 初始化图谱数据
  useEffect(() => {
    const initialNodes: Node[] = [
      { id: '1', x: 200, y: 150, radius: 20, color: '#1D77FF', label: '用户A' },
      { id: '2', x: 350, y: 100, radius: 15, color: '#FB956B', label: '用户B' },
      { id: '3', x: 300, y: 250, radius: 18, color: '#F2BC29', label: '用户C' },
      { id: '4', x: 150, y: 300, radius: 12, color: '#1D77FF', label: '用户D' },
      { id: '5', x: 400, y: 200, radius: 16, color: '#FB956B', label: '用户E' },
    ];

    const initialEdges: Edge[] = [
      { from: '1', to: '2' },
      { from: '1', to: '3' },
      { from: '2', to: '5' },
      { from: '3', to: '4' },
      { from: '4', to: '1' },
    ];

    setNodes(initialNodes);
    setEdges(initialEdges);
  }, []);

  // 绘制图谱
  const drawGraph = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // 清空画布
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // 应用缩放
    const scale = zoomLevel / 100;
    ctx.save();
    ctx.scale(scale, scale);

    // 绘制边
    ctx.strokeStyle = '#9D9D9D';
    ctx.lineWidth = 2;
    edges.forEach(edge => {
      const fromNode = nodes.find(n => n.id === edge.from);
      const toNode = nodes.find(n => n.id === edge.to);
      
      if (fromNode && toNode) {
        ctx.beginPath();
        ctx.moveTo(fromNode.x, fromNode.y);
        ctx.lineTo(toNode.x, toNode.y);
        ctx.stroke();
      }
    });

    // 绘制节点
    nodes.forEach(node => {
      // 绘制节点圆圈
      ctx.beginPath();
      ctx.arc(node.x, node.y, node.radius, 0, 2 * Math.PI);
      ctx.fillStyle = node.color;
      ctx.fill();
      ctx.strokeStyle = '#FFFFFF';
      ctx.lineWidth = 2;
      ctx.stroke();

      // 绘制标签
      ctx.fillStyle = '#FFFFFF';
      ctx.font = '12px Arial';
      ctx.textAlign = 'center';
      ctx.fillText(node.label, node.x, node.y + node.radius + 15);
    });

    ctx.restore();
  };

  // 动画循环
  useEffect(() => {
    if (!isActive) return;

    const animate = () => {
      // 简单的节点动画 - 轻微的浮动效果
      setNodes(prevNodes => 
        prevNodes.map(node => ({
          ...node,
          y: node.y + Math.sin(animationFrame * 0.02 + parseInt(node.id)) * 0.5
        }))
      );

      drawGraph();
      setAnimationFrame(prev => prev + 1);
      requestAnimationFrame(animate);
    };

    const animationId = requestAnimationFrame(animate);
    return () => cancelAnimationFrame(animationId);
  }, [isActive, nodes, edges, zoomLevel, animationFrame]);

  // 初始绘制
  useEffect(() => {
    drawGraph();
  }, [nodes, edges, zoomLevel]);

  return (
    <div className="w-full h-full flex items-center justify-center bg-gray-900 rounded-lg">
      <canvas
        ref={canvasRef}
        width={600}
        height={400}
        className="border border-gray-700 rounded"
        style={{ 
          maxWidth: '100%', 
          maxHeight: '100%',
          background: 'linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%)'
        }}
      />
    </div>
  );
};

export default GraphVisualization;
