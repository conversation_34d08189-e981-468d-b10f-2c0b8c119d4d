// 路由配置

import { createBrowserRouter, Navigate } from 'react-router-dom';
import Layout from '../components/layout/Layout';
import { DashboardView, ExplorerView, UserQueryView } from '../components/views';

export const router = createBrowserRouter([
  {
    path: '/',
    element: <Layout><Navigate to="/dashboard" replace /></Layout>,
  },
  {
    path: '/dashboard',
    element: (
      <Layout>
        <DashboardView />
      </Layout>
    ),
  },
  {
    path: '/explorer',
    element: (
      <Layout>
        <ExplorerView />
      </Layout>
    ),
  },
  {
    path: '/user-query',
    element: (
      <Layout>
        <UserQueryView />
      </Layout>
    ),
  },
  {
    path: '*',
    element: <Layout><Navigate to="/dashboard" replace /></Layout>,
  },
]);

export default router;
