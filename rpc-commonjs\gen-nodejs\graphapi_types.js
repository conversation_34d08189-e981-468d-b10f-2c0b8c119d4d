//
// Autogenerated by Thrift Compiler (0.12.0)
//
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
//
"use strict";

var thrift = require('thrift');
var Thrift = thrift.Thrift;
var Q = thrift.Q;


var ttypes = module.exports = {};
ttypes.Operation = {
  'ADD' : 1,
  'SUBTRACT' : 2,
  'MULTIPLY' : 3,
  'DIVIDE' : 4
};
ttypes.DataTypeEnum = {
  'INT_TYPE' : 1,
  'DOUBLE_TYPE' : 2,
  'STRING_Type' : 3
};
var Work = module.exports.Work = function(args) {
  this.num1 = 0;
  this.num2 = null;
  this.op = null;
  this.comment = null;
  if (args) {
    if (args.num1 !== undefined && args.num1 !== null) {
      this.num1 = args.num1;
    }
    if (args.num2 !== undefined && args.num2 !== null) {
      this.num2 = args.num2;
    }
    if (args.op !== undefined && args.op !== null) {
      this.op = args.op;
    }
    if (args.comment !== undefined && args.comment !== null) {
      this.comment = args.comment;
    }
  }
};
Work.prototype = {};
Work.prototype.read = function(input) {
  input.readStructBegin();
  while (true) {
    var ret = input.readFieldBegin();
    var ftype = ret.ftype;
    var fid = ret.fid;
    if (ftype == Thrift.Type.STOP) {
      break;
    }
    switch (fid) {
      case 1:
      if (ftype == Thrift.Type.I32) {
        this.num1 = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 2:
      if (ftype == Thrift.Type.I32) {
        this.num2 = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 3:
      if (ftype == Thrift.Type.I32) {
        this.op = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 4:
      if (ftype == Thrift.Type.STRING) {
        this.comment = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      default:
        input.skip(ftype);
    }
    input.readFieldEnd();
  }
  input.readStructEnd();
  return;
};

Work.prototype.write = function(output) {
  output.writeStructBegin('Work');
  if (this.num1 !== null && this.num1 !== undefined) {
    output.writeFieldBegin('num1', Thrift.Type.I32, 1);
    output.writeI32(this.num1);
    output.writeFieldEnd();
  }
  if (this.num2 !== null && this.num2 !== undefined) {
    output.writeFieldBegin('num2', Thrift.Type.I32, 2);
    output.writeI32(this.num2);
    output.writeFieldEnd();
  }
  if (this.op !== null && this.op !== undefined) {
    output.writeFieldBegin('op', Thrift.Type.I32, 3);
    output.writeI32(this.op);
    output.writeFieldEnd();
  }
  if (this.comment !== null && this.comment !== undefined) {
    output.writeFieldBegin('comment', Thrift.Type.STRING, 4);
    output.writeString(this.comment);
    output.writeFieldEnd();
  }
  output.writeFieldStop();
  output.writeStructEnd();
  return;
};

var EPair = module.exports.EPair = function(args) {
  this.eid = null;
  this.vid = null;
  if (args) {
    if (args.eid !== undefined && args.eid !== null) {
      this.eid = args.eid;
    }
    if (args.vid !== undefined && args.vid !== null) {
      this.vid = args.vid;
    }
  }
};
EPair.prototype = {};
EPair.prototype.read = function(input) {
  input.readStructBegin();
  while (true) {
    var ret = input.readFieldBegin();
    var ftype = ret.ftype;
    var fid = ret.fid;
    if (ftype == Thrift.Type.STOP) {
      break;
    }
    switch (fid) {
      case 1:
      if (ftype == Thrift.Type.I64) {
        this.eid = input.readI64();
      } else {
        input.skip(ftype);
      }
      break;
      case 2:
      if (ftype == Thrift.Type.I64) {
        this.vid = input.readI64();
      } else {
        input.skip(ftype);
      }
      break;
      default:
        input.skip(ftype);
    }
    input.readFieldEnd();
  }
  input.readStructEnd();
  return;
};

EPair.prototype.write = function(output) {
  output.writeStructBegin('EPair');
  if (this.eid !== null && this.eid !== undefined) {
    output.writeFieldBegin('eid', Thrift.Type.I64, 1);
    output.writeI64(this.eid);
    output.writeFieldEnd();
  }
  if (this.vid !== null && this.vid !== undefined) {
    output.writeFieldBegin('vid', Thrift.Type.I64, 2);
    output.writeI64(this.vid);
    output.writeFieldEnd();
  }
  output.writeFieldStop();
  output.writeStructEnd();
  return;
};

var VertexPk = module.exports.VertexPk = function(args) {
  this.id = null;
  this.id_l = null;
  this.id_s = null;
  this.vertexName = null;
  if (args) {
    if (args.id !== undefined && args.id !== null) {
      this.id = args.id;
    }
    if (args.id_l !== undefined && args.id_l !== null) {
      this.id_l = args.id_l;
    }
    if (args.id_s !== undefined && args.id_s !== null) {
      this.id_s = args.id_s;
    }
    if (args.vertexName !== undefined && args.vertexName !== null) {
      this.vertexName = args.vertexName;
    }
  }
};
VertexPk.prototype = {};
VertexPk.prototype.read = function(input) {
  input.readStructBegin();
  while (true) {
    var ret = input.readFieldBegin();
    var ftype = ret.ftype;
    var fid = ret.fid;
    if (ftype == Thrift.Type.STOP) {
      break;
    }
    switch (fid) {
      case 1:
      if (ftype == Thrift.Type.I64) {
        this.id = input.readI64();
      } else {
        input.skip(ftype);
      }
      break;
      case 2:
      if (ftype == Thrift.Type.I64) {
        this.id_l = input.readI64();
      } else {
        input.skip(ftype);
      }
      break;
      case 3:
      if (ftype == Thrift.Type.STRING) {
        this.id_s = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      case 4:
      if (ftype == Thrift.Type.STRING) {
        this.vertexName = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      default:
        input.skip(ftype);
    }
    input.readFieldEnd();
  }
  input.readStructEnd();
  return;
};

VertexPk.prototype.write = function(output) {
  output.writeStructBegin('VertexPk');
  if (this.id !== null && this.id !== undefined) {
    output.writeFieldBegin('id', Thrift.Type.I64, 1);
    output.writeI64(this.id);
    output.writeFieldEnd();
  }
  if (this.id_l !== null && this.id_l !== undefined) {
    output.writeFieldBegin('id_l', Thrift.Type.I64, 2);
    output.writeI64(this.id_l);
    output.writeFieldEnd();
  }
  if (this.id_s !== null && this.id_s !== undefined) {
    output.writeFieldBegin('id_s', Thrift.Type.STRING, 3);
    output.writeString(this.id_s);
    output.writeFieldEnd();
  }
  if (this.vertexName !== null && this.vertexName !== undefined) {
    output.writeFieldBegin('vertexName', Thrift.Type.STRING, 4);
    output.writeString(this.vertexName);
    output.writeFieldEnd();
  }
  output.writeFieldStop();
  output.writeStructEnd();
  return;
};

var EdgePk = module.exports.EdgePk = function(args) {
  this.id = null;
  this.edgeName = null;
  this.srcid = null;
  this.dstid = null;
  if (args) {
    if (args.id !== undefined && args.id !== null) {
      this.id = args.id;
    }
    if (args.edgeName !== undefined && args.edgeName !== null) {
      this.edgeName = args.edgeName;
    }
    if (args.srcid !== undefined && args.srcid !== null) {
      this.srcid = args.srcid;
    }
    if (args.dstid !== undefined && args.dstid !== null) {
      this.dstid = args.dstid;
    }
  }
};
EdgePk.prototype = {};
EdgePk.prototype.read = function(input) {
  input.readStructBegin();
  while (true) {
    var ret = input.readFieldBegin();
    var ftype = ret.ftype;
    var fid = ret.fid;
    if (ftype == Thrift.Type.STOP) {
      break;
    }
    switch (fid) {
      case 1:
      if (ftype == Thrift.Type.I64) {
        this.id = input.readI64();
      } else {
        input.skip(ftype);
      }
      break;
      case 2:
      if (ftype == Thrift.Type.STRING) {
        this.edgeName = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      case 3:
      if (ftype == Thrift.Type.I64) {
        this.srcid = input.readI64();
      } else {
        input.skip(ftype);
      }
      break;
      case 4:
      if (ftype == Thrift.Type.I64) {
        this.dstid = input.readI64();
      } else {
        input.skip(ftype);
      }
      break;
      default:
        input.skip(ftype);
    }
    input.readFieldEnd();
  }
  input.readStructEnd();
  return;
};

EdgePk.prototype.write = function(output) {
  output.writeStructBegin('EdgePk');
  if (this.id !== null && this.id !== undefined) {
    output.writeFieldBegin('id', Thrift.Type.I64, 1);
    output.writeI64(this.id);
    output.writeFieldEnd();
  }
  if (this.edgeName !== null && this.edgeName !== undefined) {
    output.writeFieldBegin('edgeName', Thrift.Type.STRING, 2);
    output.writeString(this.edgeName);
    output.writeFieldEnd();
  }
  if (this.srcid !== null && this.srcid !== undefined) {
    output.writeFieldBegin('srcid', Thrift.Type.I64, 3);
    output.writeI64(this.srcid);
    output.writeFieldEnd();
  }
  if (this.dstid !== null && this.dstid !== undefined) {
    output.writeFieldBegin('dstid', Thrift.Type.I64, 4);
    output.writeI64(this.dstid);
    output.writeFieldEnd();
  }
  output.writeFieldStop();
  output.writeStructEnd();
  return;
};

var VertexID = module.exports.VertexID = function(args) {
  this.id = null;
  this.name = null;
  if (args) {
    if (args.id !== undefined && args.id !== null) {
      this.id = args.id;
    }
    if (args.name !== undefined && args.name !== null) {
      this.name = args.name;
    }
  }
};
VertexID.prototype = {};
VertexID.prototype.read = function(input) {
  input.readStructBegin();
  while (true) {
    var ret = input.readFieldBegin();
    var ftype = ret.ftype;
    var fid = ret.fid;
    if (ftype == Thrift.Type.STOP) {
      break;
    }
    switch (fid) {
      case 1:
      if (ftype == Thrift.Type.I64) {
        this.id = input.readI64();
      } else {
        input.skip(ftype);
      }
      break;
      case 2:
      if (ftype == Thrift.Type.STRING) {
        this.name = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      default:
        input.skip(ftype);
    }
    input.readFieldEnd();
  }
  input.readStructEnd();
  return;
};

VertexID.prototype.write = function(output) {
  output.writeStructBegin('VertexID');
  if (this.id !== null && this.id !== undefined) {
    output.writeFieldBegin('id', Thrift.Type.I64, 1);
    output.writeI64(this.id);
    output.writeFieldEnd();
  }
  if (this.name !== null && this.name !== undefined) {
    output.writeFieldBegin('name', Thrift.Type.STRING, 2);
    output.writeString(this.name);
    output.writeFieldEnd();
  }
  output.writeFieldStop();
  output.writeStructEnd();
  return;
};

var IntervalPair = module.exports.IntervalPair = function(args) {
  this.left = null;
  this.right = null;
  this.name = null;
  if (args) {
    if (args.left !== undefined && args.left !== null) {
      this.left = args.left;
    }
    if (args.right !== undefined && args.right !== null) {
      this.right = args.right;
    }
    if (args.name !== undefined && args.name !== null) {
      this.name = args.name;
    }
  }
};
IntervalPair.prototype = {};
IntervalPair.prototype.read = function(input) {
  input.readStructBegin();
  while (true) {
    var ret = input.readFieldBegin();
    var ftype = ret.ftype;
    var fid = ret.fid;
    if (ftype == Thrift.Type.STOP) {
      break;
    }
    switch (fid) {
      case 1:
      if (ftype == Thrift.Type.I64) {
        this.left = input.readI64();
      } else {
        input.skip(ftype);
      }
      break;
      case 2:
      if (ftype == Thrift.Type.I64) {
        this.right = input.readI64();
      } else {
        input.skip(ftype);
      }
      break;
      case 3:
      if (ftype == Thrift.Type.STRING) {
        this.name = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      default:
        input.skip(ftype);
    }
    input.readFieldEnd();
  }
  input.readStructEnd();
  return;
};

IntervalPair.prototype.write = function(output) {
  output.writeStructBegin('IntervalPair');
  if (this.left !== null && this.left !== undefined) {
    output.writeFieldBegin('left', Thrift.Type.I64, 1);
    output.writeI64(this.left);
    output.writeFieldEnd();
  }
  if (this.right !== null && this.right !== undefined) {
    output.writeFieldBegin('right', Thrift.Type.I64, 2);
    output.writeI64(this.right);
    output.writeFieldEnd();
  }
  if (this.name !== null && this.name !== undefined) {
    output.writeFieldBegin('name', Thrift.Type.STRING, 3);
    output.writeString(this.name);
    output.writeFieldEnd();
  }
  output.writeFieldStop();
  output.writeStructEnd();
  return;
};

var InvalidOperation = module.exports.InvalidOperation = function(args) {
  Thrift.TException.call(this, "InvalidOperation");
  this.name = "InvalidOperation";
  this.whatOp = null;
  this.why = null;
  if (args) {
    if (args.whatOp !== undefined && args.whatOp !== null) {
      this.whatOp = args.whatOp;
    }
    if (args.why !== undefined && args.why !== null) {
      this.why = args.why;
    }
  }
};
Thrift.inherits(InvalidOperation, Thrift.TException);
InvalidOperation.prototype.name = 'InvalidOperation';
InvalidOperation.prototype.read = function(input) {
  input.readStructBegin();
  while (true) {
    var ret = input.readFieldBegin();
    var ftype = ret.ftype;
    var fid = ret.fid;
    if (ftype == Thrift.Type.STOP) {
      break;
    }
    switch (fid) {
      case 1:
      if (ftype == Thrift.Type.I32) {
        this.whatOp = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 2:
      if (ftype == Thrift.Type.STRING) {
        this.why = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      default:
        input.skip(ftype);
    }
    input.readFieldEnd();
  }
  input.readStructEnd();
  return;
};

InvalidOperation.prototype.write = function(output) {
  output.writeStructBegin('InvalidOperation');
  if (this.whatOp !== null && this.whatOp !== undefined) {
    output.writeFieldBegin('whatOp', Thrift.Type.I32, 1);
    output.writeI32(this.whatOp);
    output.writeFieldEnd();
  }
  if (this.why !== null && this.why !== undefined) {
    output.writeFieldBegin('why', Thrift.Type.STRING, 2);
    output.writeString(this.why);
    output.writeFieldEnd();
  }
  output.writeFieldStop();
  output.writeStructEnd();
  return;
};

var ReadNodeFilter_Req = module.exports.ReadNodeFilter_Req = function(args) {
  this.filterType = -1;
  this.nodeName = null;
  this.attrName = null;
  this.upper = null;
  this.lower = null;
  if (args) {
    if (args.filterType !== undefined && args.filterType !== null) {
      this.filterType = args.filterType;
    }
    if (args.nodeName !== undefined && args.nodeName !== null) {
      this.nodeName = args.nodeName;
    }
    if (args.attrName !== undefined && args.attrName !== null) {
      this.attrName = args.attrName;
    }
    if (args.upper !== undefined && args.upper !== null) {
      this.upper = args.upper;
    }
    if (args.lower !== undefined && args.lower !== null) {
      this.lower = args.lower;
    }
  }
};
ReadNodeFilter_Req.prototype = {};
ReadNodeFilter_Req.prototype.read = function(input) {
  input.readStructBegin();
  while (true) {
    var ret = input.readFieldBegin();
    var ftype = ret.ftype;
    var fid = ret.fid;
    if (ftype == Thrift.Type.STOP) {
      break;
    }
    switch (fid) {
      case 1:
      if (ftype == Thrift.Type.I32) {
        this.filterType = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 2:
      if (ftype == Thrift.Type.STRING) {
        this.nodeName = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      case 3:
      if (ftype == Thrift.Type.STRING) {
        this.attrName = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      case 4:
      if (ftype == Thrift.Type.STRING) {
        this.upper = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      case 5:
      if (ftype == Thrift.Type.STRING) {
        this.lower = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      default:
        input.skip(ftype);
    }
    input.readFieldEnd();
  }
  input.readStructEnd();
  return;
};

ReadNodeFilter_Req.prototype.write = function(output) {
  output.writeStructBegin('ReadNodeFilter_Req');
  if (this.filterType !== null && this.filterType !== undefined) {
    output.writeFieldBegin('filterType', Thrift.Type.I32, 1);
    output.writeI32(this.filterType);
    output.writeFieldEnd();
  }
  if (this.nodeName !== null && this.nodeName !== undefined) {
    output.writeFieldBegin('nodeName', Thrift.Type.STRING, 2);
    output.writeString(this.nodeName);
    output.writeFieldEnd();
  }
  if (this.attrName !== null && this.attrName !== undefined) {
    output.writeFieldBegin('attrName', Thrift.Type.STRING, 3);
    output.writeString(this.attrName);
    output.writeFieldEnd();
  }
  if (this.upper !== null && this.upper !== undefined) {
    output.writeFieldBegin('upper', Thrift.Type.STRING, 4);
    output.writeString(this.upper);
    output.writeFieldEnd();
  }
  if (this.lower !== null && this.lower !== undefined) {
    output.writeFieldBegin('lower', Thrift.Type.STRING, 5);
    output.writeString(this.lower);
    output.writeFieldEnd();
  }
  output.writeFieldStop();
  output.writeStructEnd();
  return;
};

var ReadFilter_Req = module.exports.ReadFilter_Req = function(args) {
  this.filterType = -1;
  this.nodeName = null;
  this.attrName = null;
  this.upper = null;
  this.lower = null;
  this.targetFilter = null;
  if (args) {
    if (args.filterType !== undefined && args.filterType !== null) {
      this.filterType = args.filterType;
    }
    if (args.nodeName !== undefined && args.nodeName !== null) {
      this.nodeName = args.nodeName;
    }
    if (args.attrName !== undefined && args.attrName !== null) {
      this.attrName = args.attrName;
    }
    if (args.upper !== undefined && args.upper !== null) {
      this.upper = args.upper;
    }
    if (args.lower !== undefined && args.lower !== null) {
      this.lower = args.lower;
    }
    if (args.targetFilter !== undefined && args.targetFilter !== null) {
      this.targetFilter = new ttypes.ReadNodeFilter_Req(args.targetFilter);
    }
  }
};
ReadFilter_Req.prototype = {};
ReadFilter_Req.prototype.read = function(input) {
  input.readStructBegin();
  while (true) {
    var ret = input.readFieldBegin();
    var ftype = ret.ftype;
    var fid = ret.fid;
    if (ftype == Thrift.Type.STOP) {
      break;
    }
    switch (fid) {
      case 1:
      if (ftype == Thrift.Type.I32) {
        this.filterType = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 2:
      if (ftype == Thrift.Type.STRING) {
        this.nodeName = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      case 3:
      if (ftype == Thrift.Type.STRING) {
        this.attrName = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      case 4:
      if (ftype == Thrift.Type.STRING) {
        this.upper = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      case 5:
      if (ftype == Thrift.Type.STRING) {
        this.lower = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      case 6:
      if (ftype == Thrift.Type.STRUCT) {
        this.targetFilter = new ttypes.ReadNodeFilter_Req();
        this.targetFilter.read(input);
      } else {
        input.skip(ftype);
      }
      break;
      default:
        input.skip(ftype);
    }
    input.readFieldEnd();
  }
  input.readStructEnd();
  return;
};

ReadFilter_Req.prototype.write = function(output) {
  output.writeStructBegin('ReadFilter_Req');
  if (this.filterType !== null && this.filterType !== undefined) {
    output.writeFieldBegin('filterType', Thrift.Type.I32, 1);
    output.writeI32(this.filterType);
    output.writeFieldEnd();
  }
  if (this.nodeName !== null && this.nodeName !== undefined) {
    output.writeFieldBegin('nodeName', Thrift.Type.STRING, 2);
    output.writeString(this.nodeName);
    output.writeFieldEnd();
  }
  if (this.attrName !== null && this.attrName !== undefined) {
    output.writeFieldBegin('attrName', Thrift.Type.STRING, 3);
    output.writeString(this.attrName);
    output.writeFieldEnd();
  }
  if (this.upper !== null && this.upper !== undefined) {
    output.writeFieldBegin('upper', Thrift.Type.STRING, 4);
    output.writeString(this.upper);
    output.writeFieldEnd();
  }
  if (this.lower !== null && this.lower !== undefined) {
    output.writeFieldBegin('lower', Thrift.Type.STRING, 5);
    output.writeString(this.lower);
    output.writeFieldEnd();
  }
  if (this.targetFilter !== null && this.targetFilter !== undefined) {
    output.writeFieldBegin('targetFilter', Thrift.Type.STRUCT, 6);
    this.targetFilter.write(output);
    output.writeFieldEnd();
  }
  output.writeFieldStop();
  output.writeStructEnd();
  return;
};

var ReadFilterSet_Req = module.exports.ReadFilterSet_Req = function(args) {
  this.filters = null;
  this.relationType = null;
  if (args) {
    if (args.filters !== undefined && args.filters !== null) {
      this.filters = Thrift.copyList(args.filters, [ttypes.ReadFilter_Req]);
    }
    if (args.relationType !== undefined && args.relationType !== null) {
      this.relationType = args.relationType;
    }
  }
};
ReadFilterSet_Req.prototype = {};
ReadFilterSet_Req.prototype.read = function(input) {
  input.readStructBegin();
  while (true) {
    var ret = input.readFieldBegin();
    var ftype = ret.ftype;
    var fid = ret.fid;
    if (ftype == Thrift.Type.STOP) {
      break;
    }
    switch (fid) {
      case 1:
      if (ftype == Thrift.Type.LIST) {
        this.filters = [];
        var _rtmp31 = input.readListBegin();
        var _size0 = _rtmp31.size || 0;
        for (var _i2 = 0; _i2 < _size0; ++_i2) {
          var elem3 = null;
          elem3 = new ttypes.ReadFilter_Req();
          elem3.read(input);
          this.filters.push(elem3);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 2:
      if (ftype == Thrift.Type.I32) {
        this.relationType = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      default:
        input.skip(ftype);
    }
    input.readFieldEnd();
  }
  input.readStructEnd();
  return;
};

ReadFilterSet_Req.prototype.write = function(output) {
  output.writeStructBegin('ReadFilterSet_Req');
  if (this.filters !== null && this.filters !== undefined) {
    output.writeFieldBegin('filters', Thrift.Type.LIST, 1);
    output.writeListBegin(Thrift.Type.STRUCT, this.filters.length);
    for (var iter4 in this.filters) {
      if (this.filters.hasOwnProperty(iter4)) {
        iter4 = this.filters[iter4];
        iter4.write(output);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.relationType !== null && this.relationType !== undefined) {
    output.writeFieldBegin('relationType', Thrift.Type.I32, 2);
    output.writeI32(this.relationType);
    output.writeFieldEnd();
  }
  output.writeFieldStop();
  output.writeStructEnd();
  return;
};

var ReadFilterQuery_Req = module.exports.ReadFilterQuery_Req = function(args) {
  this.graph = null;
  this.startVertex = null;
  this.startIds = null;
  this.throughEdges = null;
  this.targetEdgeNodeName = null;
  this.isReturnEdge = 1;
  this.count = 100;
  this.stepLayerLimit = 0;
  this.filterMap = null;
  this.targetFilter = null;
  if (args) {
    if (args.graph !== undefined && args.graph !== null) {
      this.graph = args.graph;
    }
    if (args.startVertex !== undefined && args.startVertex !== null) {
      this.startVertex = args.startVertex;
    }
    if (args.startIds !== undefined && args.startIds !== null) {
      this.startIds = Thrift.copyList(args.startIds, [null]);
    }
    if (args.throughEdges !== undefined && args.throughEdges !== null) {
      this.throughEdges = Thrift.copyList(args.throughEdges, [null]);
    }
    if (args.targetEdgeNodeName !== undefined && args.targetEdgeNodeName !== null) {
      this.targetEdgeNodeName = args.targetEdgeNodeName;
    }
    if (args.isReturnEdge !== undefined && args.isReturnEdge !== null) {
      this.isReturnEdge = args.isReturnEdge;
    }
    if (args.count !== undefined && args.count !== null) {
      this.count = args.count;
    }
    if (args.stepLayerLimit !== undefined && args.stepLayerLimit !== null) {
      this.stepLayerLimit = args.stepLayerLimit;
    }
    if (args.filterMap !== undefined && args.filterMap !== null) {
      this.filterMap = Thrift.copyMap(args.filterMap, [ttypes.ReadFilterSet_Req]);
    }
    if (args.targetFilter !== undefined && args.targetFilter !== null) {
      this.targetFilter = new ttypes.ReadFilterSet_Req(args.targetFilter);
    }
  }
};
ReadFilterQuery_Req.prototype = {};
ReadFilterQuery_Req.prototype.read = function(input) {
  input.readStructBegin();
  while (true) {
    var ret = input.readFieldBegin();
    var ftype = ret.ftype;
    var fid = ret.fid;
    if (ftype == Thrift.Type.STOP) {
      break;
    }
    switch (fid) {
      case 1:
      if (ftype == Thrift.Type.STRING) {
        this.graph = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      case 2:
      if (ftype == Thrift.Type.STRING) {
        this.startVertex = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      case 3:
      if (ftype == Thrift.Type.LIST) {
        this.startIds = [];
        var _rtmp36 = input.readListBegin();
        var _size5 = _rtmp36.size || 0;
        for (var _i7 = 0; _i7 < _size5; ++_i7) {
          var elem8 = null;
          elem8 = input.readI64();
          this.startIds.push(elem8);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 4:
      if (ftype == Thrift.Type.LIST) {
        this.throughEdges = [];
        var _rtmp310 = input.readListBegin();
        var _size9 = _rtmp310.size || 0;
        for (var _i11 = 0; _i11 < _size9; ++_i11) {
          var elem12 = null;
          elem12 = input.readString();
          this.throughEdges.push(elem12);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 5:
      if (ftype == Thrift.Type.STRING) {
        this.targetEdgeNodeName = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      case 6:
      if (ftype == Thrift.Type.I32) {
        this.isReturnEdge = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 7:
      if (ftype == Thrift.Type.I32) {
        this.count = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 8:
      if (ftype == Thrift.Type.I32) {
        this.stepLayerLimit = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 9:
      if (ftype == Thrift.Type.MAP) {
        this.filterMap = {};
        var _rtmp314 = input.readMapBegin();
        var _size13 = _rtmp314.size || 0;
        for (var _i15 = 0; _i15 < _size13; ++_i15) {
          var key16 = null;
          var val17 = null;
          key16 = input.readString();
          val17 = new ttypes.ReadFilterSet_Req();
          val17.read(input);
          this.filterMap[key16] = val17;
        }
        input.readMapEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 10:
      if (ftype == Thrift.Type.STRUCT) {
        this.targetFilter = new ttypes.ReadFilterSet_Req();
        this.targetFilter.read(input);
      } else {
        input.skip(ftype);
      }
      break;
      default:
        input.skip(ftype);
    }
    input.readFieldEnd();
  }
  input.readStructEnd();
  return;
};

ReadFilterQuery_Req.prototype.write = function(output) {
  output.writeStructBegin('ReadFilterQuery_Req');
  if (this.graph !== null && this.graph !== undefined) {
    output.writeFieldBegin('graph', Thrift.Type.STRING, 1);
    output.writeString(this.graph);
    output.writeFieldEnd();
  }
  if (this.startVertex !== null && this.startVertex !== undefined) {
    output.writeFieldBegin('startVertex', Thrift.Type.STRING, 2);
    output.writeString(this.startVertex);
    output.writeFieldEnd();
  }
  if (this.startIds !== null && this.startIds !== undefined) {
    output.writeFieldBegin('startIds', Thrift.Type.LIST, 3);
    output.writeListBegin(Thrift.Type.I64, this.startIds.length);
    for (var iter18 in this.startIds) {
      if (this.startIds.hasOwnProperty(iter18)) {
        iter18 = this.startIds[iter18];
        output.writeI64(iter18);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.throughEdges !== null && this.throughEdges !== undefined) {
    output.writeFieldBegin('throughEdges', Thrift.Type.LIST, 4);
    output.writeListBegin(Thrift.Type.STRING, this.throughEdges.length);
    for (var iter19 in this.throughEdges) {
      if (this.throughEdges.hasOwnProperty(iter19)) {
        iter19 = this.throughEdges[iter19];
        output.writeString(iter19);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.targetEdgeNodeName !== null && this.targetEdgeNodeName !== undefined) {
    output.writeFieldBegin('targetEdgeNodeName', Thrift.Type.STRING, 5);
    output.writeString(this.targetEdgeNodeName);
    output.writeFieldEnd();
  }
  if (this.isReturnEdge !== null && this.isReturnEdge !== undefined) {
    output.writeFieldBegin('isReturnEdge', Thrift.Type.I32, 6);
    output.writeI32(this.isReturnEdge);
    output.writeFieldEnd();
  }
  if (this.count !== null && this.count !== undefined) {
    output.writeFieldBegin('count', Thrift.Type.I32, 7);
    output.writeI32(this.count);
    output.writeFieldEnd();
  }
  if (this.stepLayerLimit !== null && this.stepLayerLimit !== undefined) {
    output.writeFieldBegin('stepLayerLimit', Thrift.Type.I32, 8);
    output.writeI32(this.stepLayerLimit);
    output.writeFieldEnd();
  }
  if (this.filterMap !== null && this.filterMap !== undefined) {
    output.writeFieldBegin('filterMap', Thrift.Type.MAP, 9);
    output.writeMapBegin(Thrift.Type.STRING, Thrift.Type.STRUCT, Thrift.objectLength(this.filterMap));
    for (var kiter20 in this.filterMap) {
      if (this.filterMap.hasOwnProperty(kiter20)) {
        var viter21 = this.filterMap[kiter20];
        output.writeString(kiter20);
        viter21.write(output);
      }
    }
    output.writeMapEnd();
    output.writeFieldEnd();
  }
  if (this.targetFilter !== null && this.targetFilter !== undefined) {
    output.writeFieldBegin('targetFilter', Thrift.Type.STRUCT, 10);
    this.targetFilter.write(output);
    output.writeFieldEnd();
  }
  output.writeFieldStop();
  output.writeStructEnd();
  return;
};

var ReadRegulerQuery_Req = module.exports.ReadRegulerQuery_Req = function(args) {
  this.graph = null;
  this.startVertex = null;
  this.startIds = null;
  this.throughEdges = null;
  this.targetEdgeNodeName = null;
  this.isReturnEdge = 1;
  this.count = 100;
  this.filterVector = null;
  if (args) {
    if (args.graph !== undefined && args.graph !== null) {
      this.graph = args.graph;
    }
    if (args.startVertex !== undefined && args.startVertex !== null) {
      this.startVertex = args.startVertex;
    }
    if (args.startIds !== undefined && args.startIds !== null) {
      this.startIds = Thrift.copyList(args.startIds, [null]);
    }
    if (args.throughEdges !== undefined && args.throughEdges !== null) {
      this.throughEdges = Thrift.copyList(args.throughEdges, [null]);
    }
    if (args.targetEdgeNodeName !== undefined && args.targetEdgeNodeName !== null) {
      this.targetEdgeNodeName = args.targetEdgeNodeName;
    }
    if (args.isReturnEdge !== undefined && args.isReturnEdge !== null) {
      this.isReturnEdge = args.isReturnEdge;
    }
    if (args.count !== undefined && args.count !== null) {
      this.count = args.count;
    }
    if (args.filterVector !== undefined && args.filterVector !== null) {
      this.filterVector = Thrift.copyList(args.filterVector, [ttypes.ReadFilterSet_Req]);
    }
  }
};
ReadRegulerQuery_Req.prototype = {};
ReadRegulerQuery_Req.prototype.read = function(input) {
  input.readStructBegin();
  while (true) {
    var ret = input.readFieldBegin();
    var ftype = ret.ftype;
    var fid = ret.fid;
    if (ftype == Thrift.Type.STOP) {
      break;
    }
    switch (fid) {
      case 1:
      if (ftype == Thrift.Type.STRING) {
        this.graph = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      case 2:
      if (ftype == Thrift.Type.STRING) {
        this.startVertex = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      case 3:
      if (ftype == Thrift.Type.LIST) {
        this.startIds = [];
        var _rtmp323 = input.readListBegin();
        var _size22 = _rtmp323.size || 0;
        for (var _i24 = 0; _i24 < _size22; ++_i24) {
          var elem25 = null;
          elem25 = input.readI64();
          this.startIds.push(elem25);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 4:
      if (ftype == Thrift.Type.LIST) {
        this.throughEdges = [];
        var _rtmp327 = input.readListBegin();
        var _size26 = _rtmp327.size || 0;
        for (var _i28 = 0; _i28 < _size26; ++_i28) {
          var elem29 = null;
          elem29 = input.readString();
          this.throughEdges.push(elem29);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 5:
      if (ftype == Thrift.Type.STRING) {
        this.targetEdgeNodeName = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      case 6:
      if (ftype == Thrift.Type.I32) {
        this.isReturnEdge = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 7:
      if (ftype == Thrift.Type.I32) {
        this.count = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 8:
      if (ftype == Thrift.Type.LIST) {
        this.filterVector = [];
        var _rtmp331 = input.readListBegin();
        var _size30 = _rtmp331.size || 0;
        for (var _i32 = 0; _i32 < _size30; ++_i32) {
          var elem33 = null;
          elem33 = new ttypes.ReadFilterSet_Req();
          elem33.read(input);
          this.filterVector.push(elem33);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      default:
        input.skip(ftype);
    }
    input.readFieldEnd();
  }
  input.readStructEnd();
  return;
};

ReadRegulerQuery_Req.prototype.write = function(output) {
  output.writeStructBegin('ReadRegulerQuery_Req');
  if (this.graph !== null && this.graph !== undefined) {
    output.writeFieldBegin('graph', Thrift.Type.STRING, 1);
    output.writeString(this.graph);
    output.writeFieldEnd();
  }
  if (this.startVertex !== null && this.startVertex !== undefined) {
    output.writeFieldBegin('startVertex', Thrift.Type.STRING, 2);
    output.writeString(this.startVertex);
    output.writeFieldEnd();
  }
  if (this.startIds !== null && this.startIds !== undefined) {
    output.writeFieldBegin('startIds', Thrift.Type.LIST, 3);
    output.writeListBegin(Thrift.Type.I64, this.startIds.length);
    for (var iter34 in this.startIds) {
      if (this.startIds.hasOwnProperty(iter34)) {
        iter34 = this.startIds[iter34];
        output.writeI64(iter34);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.throughEdges !== null && this.throughEdges !== undefined) {
    output.writeFieldBegin('throughEdges', Thrift.Type.LIST, 4);
    output.writeListBegin(Thrift.Type.STRING, this.throughEdges.length);
    for (var iter35 in this.throughEdges) {
      if (this.throughEdges.hasOwnProperty(iter35)) {
        iter35 = this.throughEdges[iter35];
        output.writeString(iter35);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.targetEdgeNodeName !== null && this.targetEdgeNodeName !== undefined) {
    output.writeFieldBegin('targetEdgeNodeName', Thrift.Type.STRING, 5);
    output.writeString(this.targetEdgeNodeName);
    output.writeFieldEnd();
  }
  if (this.isReturnEdge !== null && this.isReturnEdge !== undefined) {
    output.writeFieldBegin('isReturnEdge', Thrift.Type.I32, 6);
    output.writeI32(this.isReturnEdge);
    output.writeFieldEnd();
  }
  if (this.count !== null && this.count !== undefined) {
    output.writeFieldBegin('count', Thrift.Type.I32, 7);
    output.writeI32(this.count);
    output.writeFieldEnd();
  }
  if (this.filterVector !== null && this.filterVector !== undefined) {
    output.writeFieldBegin('filterVector', Thrift.Type.LIST, 8);
    output.writeListBegin(Thrift.Type.STRUCT, this.filterVector.length);
    for (var iter36 in this.filterVector) {
      if (this.filterVector.hasOwnProperty(iter36)) {
        iter36 = this.filterVector[iter36];
        iter36.write(output);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  output.writeFieldStop();
  output.writeStructEnd();
  return;
};

var ReadRegulerQuerySort_Req = module.exports.ReadRegulerQuerySort_Req = function(args) {
  this.graph = null;
  this.startVertex = null;
  this.startIds = null;
  this.throughEdges = null;
  this.targetEdgeNodeName = null;
  this.isReturnEdge = 1;
  this.count = 100;
  this.filterVector = null;
  this.sortAttrs = null;
  if (args) {
    if (args.graph !== undefined && args.graph !== null) {
      this.graph = args.graph;
    }
    if (args.startVertex !== undefined && args.startVertex !== null) {
      this.startVertex = args.startVertex;
    }
    if (args.startIds !== undefined && args.startIds !== null) {
      this.startIds = Thrift.copyList(args.startIds, [null]);
    }
    if (args.throughEdges !== undefined && args.throughEdges !== null) {
      this.throughEdges = Thrift.copyList(args.throughEdges, [null]);
    }
    if (args.targetEdgeNodeName !== undefined && args.targetEdgeNodeName !== null) {
      this.targetEdgeNodeName = args.targetEdgeNodeName;
    }
    if (args.isReturnEdge !== undefined && args.isReturnEdge !== null) {
      this.isReturnEdge = args.isReturnEdge;
    }
    if (args.count !== undefined && args.count !== null) {
      this.count = args.count;
    }
    if (args.filterVector !== undefined && args.filterVector !== null) {
      this.filterVector = Thrift.copyList(args.filterVector, [ttypes.ReadFilterSet_Req]);
    }
    if (args.sortAttrs !== undefined && args.sortAttrs !== null) {
      this.sortAttrs = Thrift.copyList(args.sortAttrs, [null]);
    }
  }
};
ReadRegulerQuerySort_Req.prototype = {};
ReadRegulerQuerySort_Req.prototype.read = function(input) {
  input.readStructBegin();
  while (true) {
    var ret = input.readFieldBegin();
    var ftype = ret.ftype;
    var fid = ret.fid;
    if (ftype == Thrift.Type.STOP) {
      break;
    }
    switch (fid) {
      case 1:
      if (ftype == Thrift.Type.STRING) {
        this.graph = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      case 2:
      if (ftype == Thrift.Type.STRING) {
        this.startVertex = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      case 3:
      if (ftype == Thrift.Type.LIST) {
        this.startIds = [];
        var _rtmp338 = input.readListBegin();
        var _size37 = _rtmp338.size || 0;
        for (var _i39 = 0; _i39 < _size37; ++_i39) {
          var elem40 = null;
          elem40 = input.readI64();
          this.startIds.push(elem40);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 4:
      if (ftype == Thrift.Type.LIST) {
        this.throughEdges = [];
        var _rtmp342 = input.readListBegin();
        var _size41 = _rtmp342.size || 0;
        for (var _i43 = 0; _i43 < _size41; ++_i43) {
          var elem44 = null;
          elem44 = input.readString();
          this.throughEdges.push(elem44);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 5:
      if (ftype == Thrift.Type.STRING) {
        this.targetEdgeNodeName = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      case 6:
      if (ftype == Thrift.Type.I32) {
        this.isReturnEdge = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 7:
      if (ftype == Thrift.Type.I32) {
        this.count = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 8:
      if (ftype == Thrift.Type.LIST) {
        this.filterVector = [];
        var _rtmp346 = input.readListBegin();
        var _size45 = _rtmp346.size || 0;
        for (var _i47 = 0; _i47 < _size45; ++_i47) {
          var elem48 = null;
          elem48 = new ttypes.ReadFilterSet_Req();
          elem48.read(input);
          this.filterVector.push(elem48);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 9:
      if (ftype == Thrift.Type.LIST) {
        this.sortAttrs = [];
        var _rtmp350 = input.readListBegin();
        var _size49 = _rtmp350.size || 0;
        for (var _i51 = 0; _i51 < _size49; ++_i51) {
          var elem52 = null;
          elem52 = input.readString();
          this.sortAttrs.push(elem52);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      default:
        input.skip(ftype);
    }
    input.readFieldEnd();
  }
  input.readStructEnd();
  return;
};

ReadRegulerQuerySort_Req.prototype.write = function(output) {
  output.writeStructBegin('ReadRegulerQuerySort_Req');
  if (this.graph !== null && this.graph !== undefined) {
    output.writeFieldBegin('graph', Thrift.Type.STRING, 1);
    output.writeString(this.graph);
    output.writeFieldEnd();
  }
  if (this.startVertex !== null && this.startVertex !== undefined) {
    output.writeFieldBegin('startVertex', Thrift.Type.STRING, 2);
    output.writeString(this.startVertex);
    output.writeFieldEnd();
  }
  if (this.startIds !== null && this.startIds !== undefined) {
    output.writeFieldBegin('startIds', Thrift.Type.LIST, 3);
    output.writeListBegin(Thrift.Type.I64, this.startIds.length);
    for (var iter53 in this.startIds) {
      if (this.startIds.hasOwnProperty(iter53)) {
        iter53 = this.startIds[iter53];
        output.writeI64(iter53);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.throughEdges !== null && this.throughEdges !== undefined) {
    output.writeFieldBegin('throughEdges', Thrift.Type.LIST, 4);
    output.writeListBegin(Thrift.Type.STRING, this.throughEdges.length);
    for (var iter54 in this.throughEdges) {
      if (this.throughEdges.hasOwnProperty(iter54)) {
        iter54 = this.throughEdges[iter54];
        output.writeString(iter54);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.targetEdgeNodeName !== null && this.targetEdgeNodeName !== undefined) {
    output.writeFieldBegin('targetEdgeNodeName', Thrift.Type.STRING, 5);
    output.writeString(this.targetEdgeNodeName);
    output.writeFieldEnd();
  }
  if (this.isReturnEdge !== null && this.isReturnEdge !== undefined) {
    output.writeFieldBegin('isReturnEdge', Thrift.Type.I32, 6);
    output.writeI32(this.isReturnEdge);
    output.writeFieldEnd();
  }
  if (this.count !== null && this.count !== undefined) {
    output.writeFieldBegin('count', Thrift.Type.I32, 7);
    output.writeI32(this.count);
    output.writeFieldEnd();
  }
  if (this.filterVector !== null && this.filterVector !== undefined) {
    output.writeFieldBegin('filterVector', Thrift.Type.LIST, 8);
    output.writeListBegin(Thrift.Type.STRUCT, this.filterVector.length);
    for (var iter55 in this.filterVector) {
      if (this.filterVector.hasOwnProperty(iter55)) {
        iter55 = this.filterVector[iter55];
        iter55.write(output);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.sortAttrs !== null && this.sortAttrs !== undefined) {
    output.writeFieldBegin('sortAttrs', Thrift.Type.LIST, 9);
    output.writeListBegin(Thrift.Type.STRING, this.sortAttrs.length);
    for (var iter56 in this.sortAttrs) {
      if (this.sortAttrs.hasOwnProperty(iter56)) {
        iter56 = this.sortAttrs[iter56];
        output.writeString(iter56);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  output.writeFieldStop();
  output.writeStructEnd();
  return;
};

var StepGroupQuery_Req = module.exports.StepGroupQuery_Req = function(args) {
  this.graph = null;
  this.startVertex = null;
  this.startIds = null;
  this.throughEdges = null;
  this.filterVector = null;
  this.edgeAttrGroup = null;
  this.vertexAttrGroup = null;
  this.sortFlag = 0;
  this.sortTopN = 100;
  if (args) {
    if (args.graph !== undefined && args.graph !== null) {
      this.graph = args.graph;
    }
    if (args.startVertex !== undefined && args.startVertex !== null) {
      this.startVertex = args.startVertex;
    }
    if (args.startIds !== undefined && args.startIds !== null) {
      this.startIds = Thrift.copyList(args.startIds, [null]);
    }
    if (args.throughEdges !== undefined && args.throughEdges !== null) {
      this.throughEdges = Thrift.copyList(args.throughEdges, [null]);
    }
    if (args.filterVector !== undefined && args.filterVector !== null) {
      this.filterVector = Thrift.copyList(args.filterVector, [ttypes.ReadFilterSet_Req]);
    }
    if (args.edgeAttrGroup !== undefined && args.edgeAttrGroup !== null) {
      this.edgeAttrGroup = Thrift.copyMap(args.edgeAttrGroup, [Thrift.copyList, null]);
    }
    if (args.vertexAttrGroup !== undefined && args.vertexAttrGroup !== null) {
      this.vertexAttrGroup = Thrift.copyMap(args.vertexAttrGroup, [Thrift.copyList, null]);
    }
    if (args.sortFlag !== undefined && args.sortFlag !== null) {
      this.sortFlag = args.sortFlag;
    }
    if (args.sortTopN !== undefined && args.sortTopN !== null) {
      this.sortTopN = args.sortTopN;
    }
  }
};
StepGroupQuery_Req.prototype = {};
StepGroupQuery_Req.prototype.read = function(input) {
  input.readStructBegin();
  while (true) {
    var ret = input.readFieldBegin();
    var ftype = ret.ftype;
    var fid = ret.fid;
    if (ftype == Thrift.Type.STOP) {
      break;
    }
    switch (fid) {
      case 1:
      if (ftype == Thrift.Type.STRING) {
        this.graph = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      case 2:
      if (ftype == Thrift.Type.STRING) {
        this.startVertex = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      case 3:
      if (ftype == Thrift.Type.LIST) {
        this.startIds = [];
        var _rtmp358 = input.readListBegin();
        var _size57 = _rtmp358.size || 0;
        for (var _i59 = 0; _i59 < _size57; ++_i59) {
          var elem60 = null;
          elem60 = input.readI64();
          this.startIds.push(elem60);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 4:
      if (ftype == Thrift.Type.LIST) {
        this.throughEdges = [];
        var _rtmp362 = input.readListBegin();
        var _size61 = _rtmp362.size || 0;
        for (var _i63 = 0; _i63 < _size61; ++_i63) {
          var elem64 = null;
          elem64 = input.readString();
          this.throughEdges.push(elem64);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 5:
      if (ftype == Thrift.Type.LIST) {
        this.filterVector = [];
        var _rtmp366 = input.readListBegin();
        var _size65 = _rtmp366.size || 0;
        for (var _i67 = 0; _i67 < _size65; ++_i67) {
          var elem68 = null;
          elem68 = new ttypes.ReadFilterSet_Req();
          elem68.read(input);
          this.filterVector.push(elem68);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 6:
      if (ftype == Thrift.Type.MAP) {
        this.edgeAttrGroup = {};
        var _rtmp370 = input.readMapBegin();
        var _size69 = _rtmp370.size || 0;
        for (var _i71 = 0; _i71 < _size69; ++_i71) {
          var key72 = null;
          var val73 = null;
          key72 = input.readI32();
          val73 = [];
          var _rtmp375 = input.readListBegin();
          var _size74 = _rtmp375.size || 0;
          for (var _i76 = 0; _i76 < _size74; ++_i76) {
            var elem77 = null;
            elem77 = input.readString();
            val73.push(elem77);
          }
          input.readListEnd();
          this.edgeAttrGroup[key72] = val73;
        }
        input.readMapEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 7:
      if (ftype == Thrift.Type.MAP) {
        this.vertexAttrGroup = {};
        var _rtmp379 = input.readMapBegin();
        var _size78 = _rtmp379.size || 0;
        for (var _i80 = 0; _i80 < _size78; ++_i80) {
          var key81 = null;
          var val82 = null;
          key81 = input.readI32();
          val82 = [];
          var _rtmp384 = input.readListBegin();
          var _size83 = _rtmp384.size || 0;
          for (var _i85 = 0; _i85 < _size83; ++_i85) {
            var elem86 = null;
            elem86 = input.readString();
            val82.push(elem86);
          }
          input.readListEnd();
          this.vertexAttrGroup[key81] = val82;
        }
        input.readMapEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 8:
      if (ftype == Thrift.Type.I32) {
        this.sortFlag = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 9:
      if (ftype == Thrift.Type.I32) {
        this.sortTopN = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      default:
        input.skip(ftype);
    }
    input.readFieldEnd();
  }
  input.readStructEnd();
  return;
};

StepGroupQuery_Req.prototype.write = function(output) {
  output.writeStructBegin('StepGroupQuery_Req');
  if (this.graph !== null && this.graph !== undefined) {
    output.writeFieldBegin('graph', Thrift.Type.STRING, 1);
    output.writeString(this.graph);
    output.writeFieldEnd();
  }
  if (this.startVertex !== null && this.startVertex !== undefined) {
    output.writeFieldBegin('startVertex', Thrift.Type.STRING, 2);
    output.writeString(this.startVertex);
    output.writeFieldEnd();
  }
  if (this.startIds !== null && this.startIds !== undefined) {
    output.writeFieldBegin('startIds', Thrift.Type.LIST, 3);
    output.writeListBegin(Thrift.Type.I64, this.startIds.length);
    for (var iter87 in this.startIds) {
      if (this.startIds.hasOwnProperty(iter87)) {
        iter87 = this.startIds[iter87];
        output.writeI64(iter87);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.throughEdges !== null && this.throughEdges !== undefined) {
    output.writeFieldBegin('throughEdges', Thrift.Type.LIST, 4);
    output.writeListBegin(Thrift.Type.STRING, this.throughEdges.length);
    for (var iter88 in this.throughEdges) {
      if (this.throughEdges.hasOwnProperty(iter88)) {
        iter88 = this.throughEdges[iter88];
        output.writeString(iter88);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.filterVector !== null && this.filterVector !== undefined) {
    output.writeFieldBegin('filterVector', Thrift.Type.LIST, 5);
    output.writeListBegin(Thrift.Type.STRUCT, this.filterVector.length);
    for (var iter89 in this.filterVector) {
      if (this.filterVector.hasOwnProperty(iter89)) {
        iter89 = this.filterVector[iter89];
        iter89.write(output);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.edgeAttrGroup !== null && this.edgeAttrGroup !== undefined) {
    output.writeFieldBegin('edgeAttrGroup', Thrift.Type.MAP, 6);
    output.writeMapBegin(Thrift.Type.I32, Thrift.Type.LIST, Thrift.objectLength(this.edgeAttrGroup));
    for (var kiter90 in this.edgeAttrGroup) {
      if (this.edgeAttrGroup.hasOwnProperty(kiter90)) {
        var viter91 = this.edgeAttrGroup[kiter90];
        output.writeI32(kiter90);
        output.writeListBegin(Thrift.Type.STRING, viter91.length);
        for (var iter92 in viter91) {
          if (viter91.hasOwnProperty(iter92)) {
            iter92 = viter91[iter92];
            output.writeString(iter92);
          }
        }
        output.writeListEnd();
      }
    }
    output.writeMapEnd();
    output.writeFieldEnd();
  }
  if (this.vertexAttrGroup !== null && this.vertexAttrGroup !== undefined) {
    output.writeFieldBegin('vertexAttrGroup', Thrift.Type.MAP, 7);
    output.writeMapBegin(Thrift.Type.I32, Thrift.Type.LIST, Thrift.objectLength(this.vertexAttrGroup));
    for (var kiter93 in this.vertexAttrGroup) {
      if (this.vertexAttrGroup.hasOwnProperty(kiter93)) {
        var viter94 = this.vertexAttrGroup[kiter93];
        output.writeI32(kiter93);
        output.writeListBegin(Thrift.Type.STRING, viter94.length);
        for (var iter95 in viter94) {
          if (viter94.hasOwnProperty(iter95)) {
            iter95 = viter94[iter95];
            output.writeString(iter95);
          }
        }
        output.writeListEnd();
      }
    }
    output.writeMapEnd();
    output.writeFieldEnd();
  }
  if (this.sortFlag !== null && this.sortFlag !== undefined) {
    output.writeFieldBegin('sortFlag', Thrift.Type.I32, 8);
    output.writeI32(this.sortFlag);
    output.writeFieldEnd();
  }
  if (this.sortTopN !== null && this.sortTopN !== undefined) {
    output.writeFieldBegin('sortTopN', Thrift.Type.I32, 9);
    output.writeI32(this.sortTopN);
    output.writeFieldEnd();
  }
  output.writeFieldStop();
  output.writeStructEnd();
  return;
};

var SimilarFREdgeQuery_Req = module.exports.SimilarFREdgeQuery_Req = function(args) {
  this.graph = null;
  this.edge = null;
  this.startId = null;
  this.startDirect = null;
  this.scoreAttrName = null;
  this.topn = null;
  this.filterStep1 = null;
  this.filterStep2 = null;
  if (args) {
    if (args.graph !== undefined && args.graph !== null) {
      this.graph = args.graph;
    }
    if (args.edge !== undefined && args.edge !== null) {
      this.edge = args.edge;
    }
    if (args.startId !== undefined && args.startId !== null) {
      this.startId = args.startId;
    }
    if (args.startDirect !== undefined && args.startDirect !== null) {
      this.startDirect = args.startDirect;
    }
    if (args.scoreAttrName !== undefined && args.scoreAttrName !== null) {
      this.scoreAttrName = args.scoreAttrName;
    }
    if (args.topn !== undefined && args.topn !== null) {
      this.topn = args.topn;
    }
    if (args.filterStep1 !== undefined && args.filterStep1 !== null) {
      this.filterStep1 = new ttypes.ReadFilterSet_Req(args.filterStep1);
    }
    if (args.filterStep2 !== undefined && args.filterStep2 !== null) {
      this.filterStep2 = new ttypes.ReadFilterSet_Req(args.filterStep2);
    }
  }
};
SimilarFREdgeQuery_Req.prototype = {};
SimilarFREdgeQuery_Req.prototype.read = function(input) {
  input.readStructBegin();
  while (true) {
    var ret = input.readFieldBegin();
    var ftype = ret.ftype;
    var fid = ret.fid;
    if (ftype == Thrift.Type.STOP) {
      break;
    }
    switch (fid) {
      case 1:
      if (ftype == Thrift.Type.STRING) {
        this.graph = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      case 2:
      if (ftype == Thrift.Type.STRING) {
        this.edge = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      case 3:
      if (ftype == Thrift.Type.I64) {
        this.startId = input.readI64();
      } else {
        input.skip(ftype);
      }
      break;
      case 4:
      if (ftype == Thrift.Type.I32) {
        this.startDirect = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 5:
      if (ftype == Thrift.Type.STRING) {
        this.scoreAttrName = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      case 6:
      if (ftype == Thrift.Type.I32) {
        this.topn = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 7:
      if (ftype == Thrift.Type.STRUCT) {
        this.filterStep1 = new ttypes.ReadFilterSet_Req();
        this.filterStep1.read(input);
      } else {
        input.skip(ftype);
      }
      break;
      case 8:
      if (ftype == Thrift.Type.STRUCT) {
        this.filterStep2 = new ttypes.ReadFilterSet_Req();
        this.filterStep2.read(input);
      } else {
        input.skip(ftype);
      }
      break;
      default:
        input.skip(ftype);
    }
    input.readFieldEnd();
  }
  input.readStructEnd();
  return;
};

SimilarFREdgeQuery_Req.prototype.write = function(output) {
  output.writeStructBegin('SimilarFREdgeQuery_Req');
  if (this.graph !== null && this.graph !== undefined) {
    output.writeFieldBegin('graph', Thrift.Type.STRING, 1);
    output.writeString(this.graph);
    output.writeFieldEnd();
  }
  if (this.edge !== null && this.edge !== undefined) {
    output.writeFieldBegin('edge', Thrift.Type.STRING, 2);
    output.writeString(this.edge);
    output.writeFieldEnd();
  }
  if (this.startId !== null && this.startId !== undefined) {
    output.writeFieldBegin('startId', Thrift.Type.I64, 3);
    output.writeI64(this.startId);
    output.writeFieldEnd();
  }
  if (this.startDirect !== null && this.startDirect !== undefined) {
    output.writeFieldBegin('startDirect', Thrift.Type.I32, 4);
    output.writeI32(this.startDirect);
    output.writeFieldEnd();
  }
  if (this.scoreAttrName !== null && this.scoreAttrName !== undefined) {
    output.writeFieldBegin('scoreAttrName', Thrift.Type.STRING, 5);
    output.writeString(this.scoreAttrName);
    output.writeFieldEnd();
  }
  if (this.topn !== null && this.topn !== undefined) {
    output.writeFieldBegin('topn', Thrift.Type.I32, 6);
    output.writeI32(this.topn);
    output.writeFieldEnd();
  }
  if (this.filterStep1 !== null && this.filterStep1 !== undefined) {
    output.writeFieldBegin('filterStep1', Thrift.Type.STRUCT, 7);
    this.filterStep1.write(output);
    output.writeFieldEnd();
  }
  if (this.filterStep2 !== null && this.filterStep2 !== undefined) {
    output.writeFieldBegin('filterStep2', Thrift.Type.STRUCT, 8);
    this.filterStep2.write(output);
    output.writeFieldEnd();
  }
  output.writeFieldStop();
  output.writeStructEnd();
  return;
};

var SimilarMutualQuery_Req = module.exports.SimilarMutualQuery_Req = function(args) {
  this.graph = null;
  this.edge1 = null;
  this.edge2 = null;
  this.startId = null;
  this.topn = null;
  this.multiFlag = null;
  this.filterStep1 = null;
  this.filterStep2 = null;
  if (args) {
    if (args.graph !== undefined && args.graph !== null) {
      this.graph = args.graph;
    }
    if (args.edge1 !== undefined && args.edge1 !== null) {
      this.edge1 = args.edge1;
    }
    if (args.edge2 !== undefined && args.edge2 !== null) {
      this.edge2 = args.edge2;
    }
    if (args.startId !== undefined && args.startId !== null) {
      this.startId = args.startId;
    }
    if (args.topn !== undefined && args.topn !== null) {
      this.topn = args.topn;
    }
    if (args.multiFlag !== undefined && args.multiFlag !== null) {
      this.multiFlag = args.multiFlag;
    }
    if (args.filterStep1 !== undefined && args.filterStep1 !== null) {
      this.filterStep1 = new ttypes.ReadFilterSet_Req(args.filterStep1);
    }
    if (args.filterStep2 !== undefined && args.filterStep2 !== null) {
      this.filterStep2 = new ttypes.ReadFilterSet_Req(args.filterStep2);
    }
  }
};
SimilarMutualQuery_Req.prototype = {};
SimilarMutualQuery_Req.prototype.read = function(input) {
  input.readStructBegin();
  while (true) {
    var ret = input.readFieldBegin();
    var ftype = ret.ftype;
    var fid = ret.fid;
    if (ftype == Thrift.Type.STOP) {
      break;
    }
    switch (fid) {
      case 1:
      if (ftype == Thrift.Type.STRING) {
        this.graph = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      case 2:
      if (ftype == Thrift.Type.STRING) {
        this.edge1 = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      case 3:
      if (ftype == Thrift.Type.STRING) {
        this.edge2 = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      case 4:
      if (ftype == Thrift.Type.I64) {
        this.startId = input.readI64();
      } else {
        input.skip(ftype);
      }
      break;
      case 5:
      if (ftype == Thrift.Type.I32) {
        this.topn = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 6:
      if (ftype == Thrift.Type.I32) {
        this.multiFlag = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 7:
      if (ftype == Thrift.Type.STRUCT) {
        this.filterStep1 = new ttypes.ReadFilterSet_Req();
        this.filterStep1.read(input);
      } else {
        input.skip(ftype);
      }
      break;
      case 8:
      if (ftype == Thrift.Type.STRUCT) {
        this.filterStep2 = new ttypes.ReadFilterSet_Req();
        this.filterStep2.read(input);
      } else {
        input.skip(ftype);
      }
      break;
      default:
        input.skip(ftype);
    }
    input.readFieldEnd();
  }
  input.readStructEnd();
  return;
};

SimilarMutualQuery_Req.prototype.write = function(output) {
  output.writeStructBegin('SimilarMutualQuery_Req');
  if (this.graph !== null && this.graph !== undefined) {
    output.writeFieldBegin('graph', Thrift.Type.STRING, 1);
    output.writeString(this.graph);
    output.writeFieldEnd();
  }
  if (this.edge1 !== null && this.edge1 !== undefined) {
    output.writeFieldBegin('edge1', Thrift.Type.STRING, 2);
    output.writeString(this.edge1);
    output.writeFieldEnd();
  }
  if (this.edge2 !== null && this.edge2 !== undefined) {
    output.writeFieldBegin('edge2', Thrift.Type.STRING, 3);
    output.writeString(this.edge2);
    output.writeFieldEnd();
  }
  if (this.startId !== null && this.startId !== undefined) {
    output.writeFieldBegin('startId', Thrift.Type.I64, 4);
    output.writeI64(this.startId);
    output.writeFieldEnd();
  }
  if (this.topn !== null && this.topn !== undefined) {
    output.writeFieldBegin('topn', Thrift.Type.I32, 5);
    output.writeI32(this.topn);
    output.writeFieldEnd();
  }
  if (this.multiFlag !== null && this.multiFlag !== undefined) {
    output.writeFieldBegin('multiFlag', Thrift.Type.I32, 6);
    output.writeI32(this.multiFlag);
    output.writeFieldEnd();
  }
  if (this.filterStep1 !== null && this.filterStep1 !== undefined) {
    output.writeFieldBegin('filterStep1', Thrift.Type.STRUCT, 7);
    this.filterStep1.write(output);
    output.writeFieldEnd();
  }
  if (this.filterStep2 !== null && this.filterStep2 !== undefined) {
    output.writeFieldBegin('filterStep2', Thrift.Type.STRUCT, 8);
    this.filterStep2.write(output);
    output.writeFieldEnd();
  }
  output.writeFieldStop();
  output.writeStructEnd();
  return;
};

var RecommendQuery_Req = module.exports.RecommendQuery_Req = function(args) {
  this.graph = null;
  this.startId = null;
  this.throughEdges = null;
  this.filterVector = null;
  this.corelationFlag = null;
  this.multiFlag = null;
  this.minCount = null;
  this.minInDegree = null;
  this.topn = null;
  if (args) {
    if (args.graph !== undefined && args.graph !== null) {
      this.graph = args.graph;
    }
    if (args.startId !== undefined && args.startId !== null) {
      this.startId = args.startId;
    }
    if (args.throughEdges !== undefined && args.throughEdges !== null) {
      this.throughEdges = Thrift.copyList(args.throughEdges, [null]);
    }
    if (args.filterVector !== undefined && args.filterVector !== null) {
      this.filterVector = Thrift.copyList(args.filterVector, [ttypes.ReadFilterSet_Req]);
    }
    if (args.corelationFlag !== undefined && args.corelationFlag !== null) {
      this.corelationFlag = args.corelationFlag;
    }
    if (args.multiFlag !== undefined && args.multiFlag !== null) {
      this.multiFlag = args.multiFlag;
    }
    if (args.minCount !== undefined && args.minCount !== null) {
      this.minCount = args.minCount;
    }
    if (args.minInDegree !== undefined && args.minInDegree !== null) {
      this.minInDegree = args.minInDegree;
    }
    if (args.topn !== undefined && args.topn !== null) {
      this.topn = args.topn;
    }
  }
};
RecommendQuery_Req.prototype = {};
RecommendQuery_Req.prototype.read = function(input) {
  input.readStructBegin();
  while (true) {
    var ret = input.readFieldBegin();
    var ftype = ret.ftype;
    var fid = ret.fid;
    if (ftype == Thrift.Type.STOP) {
      break;
    }
    switch (fid) {
      case 1:
      if (ftype == Thrift.Type.STRING) {
        this.graph = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      case 2:
      if (ftype == Thrift.Type.I64) {
        this.startId = input.readI64();
      } else {
        input.skip(ftype);
      }
      break;
      case 3:
      if (ftype == Thrift.Type.LIST) {
        this.throughEdges = [];
        var _rtmp397 = input.readListBegin();
        var _size96 = _rtmp397.size || 0;
        for (var _i98 = 0; _i98 < _size96; ++_i98) {
          var elem99 = null;
          elem99 = input.readString();
          this.throughEdges.push(elem99);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 4:
      if (ftype == Thrift.Type.LIST) {
        this.filterVector = [];
        var _rtmp3101 = input.readListBegin();
        var _size100 = _rtmp3101.size || 0;
        for (var _i102 = 0; _i102 < _size100; ++_i102) {
          var elem103 = null;
          elem103 = new ttypes.ReadFilterSet_Req();
          elem103.read(input);
          this.filterVector.push(elem103);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 5:
      if (ftype == Thrift.Type.I32) {
        this.corelationFlag = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 6:
      if (ftype == Thrift.Type.I32) {
        this.multiFlag = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 7:
      if (ftype == Thrift.Type.I32) {
        this.minCount = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 8:
      if (ftype == Thrift.Type.I32) {
        this.minInDegree = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 9:
      if (ftype == Thrift.Type.I32) {
        this.topn = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      default:
        input.skip(ftype);
    }
    input.readFieldEnd();
  }
  input.readStructEnd();
  return;
};

RecommendQuery_Req.prototype.write = function(output) {
  output.writeStructBegin('RecommendQuery_Req');
  if (this.graph !== null && this.graph !== undefined) {
    output.writeFieldBegin('graph', Thrift.Type.STRING, 1);
    output.writeString(this.graph);
    output.writeFieldEnd();
  }
  if (this.startId !== null && this.startId !== undefined) {
    output.writeFieldBegin('startId', Thrift.Type.I64, 2);
    output.writeI64(this.startId);
    output.writeFieldEnd();
  }
  if (this.throughEdges !== null && this.throughEdges !== undefined) {
    output.writeFieldBegin('throughEdges', Thrift.Type.LIST, 3);
    output.writeListBegin(Thrift.Type.STRING, this.throughEdges.length);
    for (var iter104 in this.throughEdges) {
      if (this.throughEdges.hasOwnProperty(iter104)) {
        iter104 = this.throughEdges[iter104];
        output.writeString(iter104);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.filterVector !== null && this.filterVector !== undefined) {
    output.writeFieldBegin('filterVector', Thrift.Type.LIST, 4);
    output.writeListBegin(Thrift.Type.STRUCT, this.filterVector.length);
    for (var iter105 in this.filterVector) {
      if (this.filterVector.hasOwnProperty(iter105)) {
        iter105 = this.filterVector[iter105];
        iter105.write(output);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.corelationFlag !== null && this.corelationFlag !== undefined) {
    output.writeFieldBegin('corelationFlag', Thrift.Type.I32, 5);
    output.writeI32(this.corelationFlag);
    output.writeFieldEnd();
  }
  if (this.multiFlag !== null && this.multiFlag !== undefined) {
    output.writeFieldBegin('multiFlag', Thrift.Type.I32, 6);
    output.writeI32(this.multiFlag);
    output.writeFieldEnd();
  }
  if (this.minCount !== null && this.minCount !== undefined) {
    output.writeFieldBegin('minCount', Thrift.Type.I32, 7);
    output.writeI32(this.minCount);
    output.writeFieldEnd();
  }
  if (this.minInDegree !== null && this.minInDegree !== undefined) {
    output.writeFieldBegin('minInDegree', Thrift.Type.I32, 8);
    output.writeI32(this.minInDegree);
    output.writeFieldEnd();
  }
  if (this.topn !== null && this.topn !== undefined) {
    output.writeFieldBegin('topn', Thrift.Type.I32, 9);
    output.writeI32(this.topn);
    output.writeFieldEnd();
  }
  output.writeFieldStop();
  output.writeStructEnd();
  return;
};

var PathQuery_Req = module.exports.PathQuery_Req = function(args) {
  this.graph = null;
  this.startId = null;
  this.destId = null;
  this.throughEdges = null;
  this.filterVector = null;
  this.topn = null;
  if (args) {
    if (args.graph !== undefined && args.graph !== null) {
      this.graph = args.graph;
    }
    if (args.startId !== undefined && args.startId !== null) {
      this.startId = args.startId;
    }
    if (args.destId !== undefined && args.destId !== null) {
      this.destId = args.destId;
    }
    if (args.throughEdges !== undefined && args.throughEdges !== null) {
      this.throughEdges = Thrift.copyList(args.throughEdges, [null]);
    }
    if (args.filterVector !== undefined && args.filterVector !== null) {
      this.filterVector = Thrift.copyList(args.filterVector, [ttypes.ReadFilterSet_Req]);
    }
    if (args.topn !== undefined && args.topn !== null) {
      this.topn = args.topn;
    }
  }
};
PathQuery_Req.prototype = {};
PathQuery_Req.prototype.read = function(input) {
  input.readStructBegin();
  while (true) {
    var ret = input.readFieldBegin();
    var ftype = ret.ftype;
    var fid = ret.fid;
    if (ftype == Thrift.Type.STOP) {
      break;
    }
    switch (fid) {
      case 1:
      if (ftype == Thrift.Type.STRING) {
        this.graph = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      case 2:
      if (ftype == Thrift.Type.I64) {
        this.startId = input.readI64();
      } else {
        input.skip(ftype);
      }
      break;
      case 3:
      if (ftype == Thrift.Type.I64) {
        this.destId = input.readI64();
      } else {
        input.skip(ftype);
      }
      break;
      case 4:
      if (ftype == Thrift.Type.LIST) {
        this.throughEdges = [];
        var _rtmp3107 = input.readListBegin();
        var _size106 = _rtmp3107.size || 0;
        for (var _i108 = 0; _i108 < _size106; ++_i108) {
          var elem109 = null;
          elem109 = input.readString();
          this.throughEdges.push(elem109);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 5:
      if (ftype == Thrift.Type.LIST) {
        this.filterVector = [];
        var _rtmp3111 = input.readListBegin();
        var _size110 = _rtmp3111.size || 0;
        for (var _i112 = 0; _i112 < _size110; ++_i112) {
          var elem113 = null;
          elem113 = new ttypes.ReadFilterSet_Req();
          elem113.read(input);
          this.filterVector.push(elem113);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 6:
      if (ftype == Thrift.Type.I32) {
        this.topn = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      default:
        input.skip(ftype);
    }
    input.readFieldEnd();
  }
  input.readStructEnd();
  return;
};

PathQuery_Req.prototype.write = function(output) {
  output.writeStructBegin('PathQuery_Req');
  if (this.graph !== null && this.graph !== undefined) {
    output.writeFieldBegin('graph', Thrift.Type.STRING, 1);
    output.writeString(this.graph);
    output.writeFieldEnd();
  }
  if (this.startId !== null && this.startId !== undefined) {
    output.writeFieldBegin('startId', Thrift.Type.I64, 2);
    output.writeI64(this.startId);
    output.writeFieldEnd();
  }
  if (this.destId !== null && this.destId !== undefined) {
    output.writeFieldBegin('destId', Thrift.Type.I64, 3);
    output.writeI64(this.destId);
    output.writeFieldEnd();
  }
  if (this.throughEdges !== null && this.throughEdges !== undefined) {
    output.writeFieldBegin('throughEdges', Thrift.Type.LIST, 4);
    output.writeListBegin(Thrift.Type.STRING, this.throughEdges.length);
    for (var iter114 in this.throughEdges) {
      if (this.throughEdges.hasOwnProperty(iter114)) {
        iter114 = this.throughEdges[iter114];
        output.writeString(iter114);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.filterVector !== null && this.filterVector !== undefined) {
    output.writeFieldBegin('filterVector', Thrift.Type.LIST, 5);
    output.writeListBegin(Thrift.Type.STRUCT, this.filterVector.length);
    for (var iter115 in this.filterVector) {
      if (this.filterVector.hasOwnProperty(iter115)) {
        iter115 = this.filterVector[iter115];
        iter115.write(output);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.topn !== null && this.topn !== undefined) {
    output.writeFieldBegin('topn', Thrift.Type.I32, 6);
    output.writeI32(this.topn);
    output.writeFieldEnd();
  }
  output.writeFieldStop();
  output.writeStructEnd();
  return;
};

var SimplePageQuery_Req = module.exports.SimplePageQuery_Req = function(args) {
  this.graph = null;
  this.veName = null;
  this.vertexFlag = null;
  this.filter = null;
  this.pageSize = null;
  this.scrollId = -1;
  if (args) {
    if (args.graph !== undefined && args.graph !== null) {
      this.graph = args.graph;
    }
    if (args.veName !== undefined && args.veName !== null) {
      this.veName = args.veName;
    }
    if (args.vertexFlag !== undefined && args.vertexFlag !== null) {
      this.vertexFlag = args.vertexFlag;
    }
    if (args.filter !== undefined && args.filter !== null) {
      this.filter = new ttypes.ReadFilterSet_Req(args.filter);
    }
    if (args.pageSize !== undefined && args.pageSize !== null) {
      this.pageSize = args.pageSize;
    }
    if (args.scrollId !== undefined && args.scrollId !== null) {
      this.scrollId = args.scrollId;
    }
  }
};
SimplePageQuery_Req.prototype = {};
SimplePageQuery_Req.prototype.read = function(input) {
  input.readStructBegin();
  while (true) {
    var ret = input.readFieldBegin();
    var ftype = ret.ftype;
    var fid = ret.fid;
    if (ftype == Thrift.Type.STOP) {
      break;
    }
    switch (fid) {
      case 1:
      if (ftype == Thrift.Type.STRING) {
        this.graph = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      case 2:
      if (ftype == Thrift.Type.STRING) {
        this.veName = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      case 3:
      if (ftype == Thrift.Type.BOOL) {
        this.vertexFlag = input.readBool();
      } else {
        input.skip(ftype);
      }
      break;
      case 4:
      if (ftype == Thrift.Type.STRUCT) {
        this.filter = new ttypes.ReadFilterSet_Req();
        this.filter.read(input);
      } else {
        input.skip(ftype);
      }
      break;
      case 5:
      if (ftype == Thrift.Type.I32) {
        this.pageSize = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 6:
      if (ftype == Thrift.Type.I64) {
        this.scrollId = input.readI64();
      } else {
        input.skip(ftype);
      }
      break;
      default:
        input.skip(ftype);
    }
    input.readFieldEnd();
  }
  input.readStructEnd();
  return;
};

SimplePageQuery_Req.prototype.write = function(output) {
  output.writeStructBegin('SimplePageQuery_Req');
  if (this.graph !== null && this.graph !== undefined) {
    output.writeFieldBegin('graph', Thrift.Type.STRING, 1);
    output.writeString(this.graph);
    output.writeFieldEnd();
  }
  if (this.veName !== null && this.veName !== undefined) {
    output.writeFieldBegin('veName', Thrift.Type.STRING, 2);
    output.writeString(this.veName);
    output.writeFieldEnd();
  }
  if (this.vertexFlag !== null && this.vertexFlag !== undefined) {
    output.writeFieldBegin('vertexFlag', Thrift.Type.BOOL, 3);
    output.writeBool(this.vertexFlag);
    output.writeFieldEnd();
  }
  if (this.filter !== null && this.filter !== undefined) {
    output.writeFieldBegin('filter', Thrift.Type.STRUCT, 4);
    this.filter.write(output);
    output.writeFieldEnd();
  }
  if (this.pageSize !== null && this.pageSize !== undefined) {
    output.writeFieldBegin('pageSize', Thrift.Type.I32, 5);
    output.writeI32(this.pageSize);
    output.writeFieldEnd();
  }
  if (this.scrollId !== null && this.scrollId !== undefined) {
    output.writeFieldBegin('scrollId', Thrift.Type.I64, 6);
    output.writeI64(this.scrollId);
    output.writeFieldEnd();
  }
  output.writeFieldStop();
  output.writeStructEnd();
  return;
};

var SimplePageQuerySort_Req = module.exports.SimplePageQuerySort_Req = function(args) {
  this.graph = null;
  this.veName = null;
  this.vertexFlag = null;
  this.filter = null;
  this.pageSize = null;
  this.scrollId = -1;
  this.sortAttrs = null;
  if (args) {
    if (args.graph !== undefined && args.graph !== null) {
      this.graph = args.graph;
    }
    if (args.veName !== undefined && args.veName !== null) {
      this.veName = args.veName;
    }
    if (args.vertexFlag !== undefined && args.vertexFlag !== null) {
      this.vertexFlag = args.vertexFlag;
    }
    if (args.filter !== undefined && args.filter !== null) {
      this.filter = new ttypes.ReadFilterSet_Req(args.filter);
    }
    if (args.pageSize !== undefined && args.pageSize !== null) {
      this.pageSize = args.pageSize;
    }
    if (args.scrollId !== undefined && args.scrollId !== null) {
      this.scrollId = args.scrollId;
    }
    if (args.sortAttrs !== undefined && args.sortAttrs !== null) {
      this.sortAttrs = Thrift.copyList(args.sortAttrs, [null]);
    }
  }
};
SimplePageQuerySort_Req.prototype = {};
SimplePageQuerySort_Req.prototype.read = function(input) {
  input.readStructBegin();
  while (true) {
    var ret = input.readFieldBegin();
    var ftype = ret.ftype;
    var fid = ret.fid;
    if (ftype == Thrift.Type.STOP) {
      break;
    }
    switch (fid) {
      case 1:
      if (ftype == Thrift.Type.STRING) {
        this.graph = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      case 2:
      if (ftype == Thrift.Type.STRING) {
        this.veName = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      case 3:
      if (ftype == Thrift.Type.BOOL) {
        this.vertexFlag = input.readBool();
      } else {
        input.skip(ftype);
      }
      break;
      case 4:
      if (ftype == Thrift.Type.STRUCT) {
        this.filter = new ttypes.ReadFilterSet_Req();
        this.filter.read(input);
      } else {
        input.skip(ftype);
      }
      break;
      case 5:
      if (ftype == Thrift.Type.I32) {
        this.pageSize = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 6:
      if (ftype == Thrift.Type.I64) {
        this.scrollId = input.readI64();
      } else {
        input.skip(ftype);
      }
      break;
      case 7:
      if (ftype == Thrift.Type.LIST) {
        this.sortAttrs = [];
        var _rtmp3117 = input.readListBegin();
        var _size116 = _rtmp3117.size || 0;
        for (var _i118 = 0; _i118 < _size116; ++_i118) {
          var elem119 = null;
          elem119 = input.readString();
          this.sortAttrs.push(elem119);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      default:
        input.skip(ftype);
    }
    input.readFieldEnd();
  }
  input.readStructEnd();
  return;
};

SimplePageQuerySort_Req.prototype.write = function(output) {
  output.writeStructBegin('SimplePageQuerySort_Req');
  if (this.graph !== null && this.graph !== undefined) {
    output.writeFieldBegin('graph', Thrift.Type.STRING, 1);
    output.writeString(this.graph);
    output.writeFieldEnd();
  }
  if (this.veName !== null && this.veName !== undefined) {
    output.writeFieldBegin('veName', Thrift.Type.STRING, 2);
    output.writeString(this.veName);
    output.writeFieldEnd();
  }
  if (this.vertexFlag !== null && this.vertexFlag !== undefined) {
    output.writeFieldBegin('vertexFlag', Thrift.Type.BOOL, 3);
    output.writeBool(this.vertexFlag);
    output.writeFieldEnd();
  }
  if (this.filter !== null && this.filter !== undefined) {
    output.writeFieldBegin('filter', Thrift.Type.STRUCT, 4);
    this.filter.write(output);
    output.writeFieldEnd();
  }
  if (this.pageSize !== null && this.pageSize !== undefined) {
    output.writeFieldBegin('pageSize', Thrift.Type.I32, 5);
    output.writeI32(this.pageSize);
    output.writeFieldEnd();
  }
  if (this.scrollId !== null && this.scrollId !== undefined) {
    output.writeFieldBegin('scrollId', Thrift.Type.I64, 6);
    output.writeI64(this.scrollId);
    output.writeFieldEnd();
  }
  if (this.sortAttrs !== null && this.sortAttrs !== undefined) {
    output.writeFieldBegin('sortAttrs', Thrift.Type.LIST, 7);
    output.writeListBegin(Thrift.Type.STRING, this.sortAttrs.length);
    for (var iter120 in this.sortAttrs) {
      if (this.sortAttrs.hasOwnProperty(iter120)) {
        iter120 = this.sortAttrs[iter120];
        output.writeString(iter120);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  output.writeFieldStop();
  output.writeStructEnd();
  return;
};

var SimpleGroupQuery_Req = module.exports.SimpleGroupQuery_Req = function(args) {
  this.graph = null;
  this.veName = null;
  this.vertexFlag = null;
  this.filter = null;
  this.attrGroup = null;
  this.sortFlag = 0;
  this.sortTopN = 100;
  if (args) {
    if (args.graph !== undefined && args.graph !== null) {
      this.graph = args.graph;
    }
    if (args.veName !== undefined && args.veName !== null) {
      this.veName = args.veName;
    }
    if (args.vertexFlag !== undefined && args.vertexFlag !== null) {
      this.vertexFlag = args.vertexFlag;
    }
    if (args.filter !== undefined && args.filter !== null) {
      this.filter = new ttypes.ReadFilterSet_Req(args.filter);
    }
    if (args.attrGroup !== undefined && args.attrGroup !== null) {
      this.attrGroup = Thrift.copyList(args.attrGroup, [null]);
    }
    if (args.sortFlag !== undefined && args.sortFlag !== null) {
      this.sortFlag = args.sortFlag;
    }
    if (args.sortTopN !== undefined && args.sortTopN !== null) {
      this.sortTopN = args.sortTopN;
    }
  }
};
SimpleGroupQuery_Req.prototype = {};
SimpleGroupQuery_Req.prototype.read = function(input) {
  input.readStructBegin();
  while (true) {
    var ret = input.readFieldBegin();
    var ftype = ret.ftype;
    var fid = ret.fid;
    if (ftype == Thrift.Type.STOP) {
      break;
    }
    switch (fid) {
      case 1:
      if (ftype == Thrift.Type.STRING) {
        this.graph = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      case 2:
      if (ftype == Thrift.Type.STRING) {
        this.veName = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      case 3:
      if (ftype == Thrift.Type.BOOL) {
        this.vertexFlag = input.readBool();
      } else {
        input.skip(ftype);
      }
      break;
      case 4:
      if (ftype == Thrift.Type.STRUCT) {
        this.filter = new ttypes.ReadFilterSet_Req();
        this.filter.read(input);
      } else {
        input.skip(ftype);
      }
      break;
      case 5:
      if (ftype == Thrift.Type.LIST) {
        this.attrGroup = [];
        var _rtmp3122 = input.readListBegin();
        var _size121 = _rtmp3122.size || 0;
        for (var _i123 = 0; _i123 < _size121; ++_i123) {
          var elem124 = null;
          elem124 = input.readString();
          this.attrGroup.push(elem124);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 6:
      if (ftype == Thrift.Type.I32) {
        this.sortFlag = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 7:
      if (ftype == Thrift.Type.I32) {
        this.sortTopN = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      default:
        input.skip(ftype);
    }
    input.readFieldEnd();
  }
  input.readStructEnd();
  return;
};

SimpleGroupQuery_Req.prototype.write = function(output) {
  output.writeStructBegin('SimpleGroupQuery_Req');
  if (this.graph !== null && this.graph !== undefined) {
    output.writeFieldBegin('graph', Thrift.Type.STRING, 1);
    output.writeString(this.graph);
    output.writeFieldEnd();
  }
  if (this.veName !== null && this.veName !== undefined) {
    output.writeFieldBegin('veName', Thrift.Type.STRING, 2);
    output.writeString(this.veName);
    output.writeFieldEnd();
  }
  if (this.vertexFlag !== null && this.vertexFlag !== undefined) {
    output.writeFieldBegin('vertexFlag', Thrift.Type.BOOL, 3);
    output.writeBool(this.vertexFlag);
    output.writeFieldEnd();
  }
  if (this.filter !== null && this.filter !== undefined) {
    output.writeFieldBegin('filter', Thrift.Type.STRUCT, 4);
    this.filter.write(output);
    output.writeFieldEnd();
  }
  if (this.attrGroup !== null && this.attrGroup !== undefined) {
    output.writeFieldBegin('attrGroup', Thrift.Type.LIST, 5);
    output.writeListBegin(Thrift.Type.STRING, this.attrGroup.length);
    for (var iter125 in this.attrGroup) {
      if (this.attrGroup.hasOwnProperty(iter125)) {
        iter125 = this.attrGroup[iter125];
        output.writeString(iter125);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.sortFlag !== null && this.sortFlag !== undefined) {
    output.writeFieldBegin('sortFlag', Thrift.Type.I32, 6);
    output.writeI32(this.sortFlag);
    output.writeFieldEnd();
  }
  if (this.sortTopN !== null && this.sortTopN !== undefined) {
    output.writeFieldBegin('sortTopN', Thrift.Type.I32, 7);
    output.writeI32(this.sortTopN);
    output.writeFieldEnd();
  }
  output.writeFieldStop();
  output.writeStructEnd();
  return;
};

var CompondStepQuery_Req = module.exports.CompondStepQuery_Req = function(args) {
  this.startQuery = null;
  this.stepQuery = null;
  if (args) {
    if (args.startQuery !== undefined && args.startQuery !== null) {
      this.startQuery = new ttypes.SimplePageQuery_Req(args.startQuery);
    }
    if (args.stepQuery !== undefined && args.stepQuery !== null) {
      this.stepQuery = new ttypes.ReadRegulerQuery_Req(args.stepQuery);
    }
  }
};
CompondStepQuery_Req.prototype = {};
CompondStepQuery_Req.prototype.read = function(input) {
  input.readStructBegin();
  while (true) {
    var ret = input.readFieldBegin();
    var ftype = ret.ftype;
    var fid = ret.fid;
    if (ftype == Thrift.Type.STOP) {
      break;
    }
    switch (fid) {
      case 1:
      if (ftype == Thrift.Type.STRUCT) {
        this.startQuery = new ttypes.SimplePageQuery_Req();
        this.startQuery.read(input);
      } else {
        input.skip(ftype);
      }
      break;
      case 2:
      if (ftype == Thrift.Type.STRUCT) {
        this.stepQuery = new ttypes.ReadRegulerQuery_Req();
        this.stepQuery.read(input);
      } else {
        input.skip(ftype);
      }
      break;
      default:
        input.skip(ftype);
    }
    input.readFieldEnd();
  }
  input.readStructEnd();
  return;
};

CompondStepQuery_Req.prototype.write = function(output) {
  output.writeStructBegin('CompondStepQuery_Req');
  if (this.startQuery !== null && this.startQuery !== undefined) {
    output.writeFieldBegin('startQuery', Thrift.Type.STRUCT, 1);
    this.startQuery.write(output);
    output.writeFieldEnd();
  }
  if (this.stepQuery !== null && this.stepQuery !== undefined) {
    output.writeFieldBegin('stepQuery', Thrift.Type.STRUCT, 2);
    this.stepQuery.write(output);
    output.writeFieldEnd();
  }
  output.writeFieldStop();
  output.writeStructEnd();
  return;
};

var CompondGroupQuery_Req = module.exports.CompondGroupQuery_Req = function(args) {
  this.startQuery = null;
  this.stepQuery = null;
  if (args) {
    if (args.startQuery !== undefined && args.startQuery !== null) {
      this.startQuery = new ttypes.SimpleGroupQuery_Req(args.startQuery);
    }
    if (args.stepQuery !== undefined && args.stepQuery !== null) {
      this.stepQuery = new ttypes.StepGroupQuery_Req(args.stepQuery);
    }
  }
};
CompondGroupQuery_Req.prototype = {};
CompondGroupQuery_Req.prototype.read = function(input) {
  input.readStructBegin();
  while (true) {
    var ret = input.readFieldBegin();
    var ftype = ret.ftype;
    var fid = ret.fid;
    if (ftype == Thrift.Type.STOP) {
      break;
    }
    switch (fid) {
      case 1:
      if (ftype == Thrift.Type.STRUCT) {
        this.startQuery = new ttypes.SimpleGroupQuery_Req();
        this.startQuery.read(input);
      } else {
        input.skip(ftype);
      }
      break;
      case 2:
      if (ftype == Thrift.Type.STRUCT) {
        this.stepQuery = new ttypes.StepGroupQuery_Req();
        this.stepQuery.read(input);
      } else {
        input.skip(ftype);
      }
      break;
      default:
        input.skip(ftype);
    }
    input.readFieldEnd();
  }
  input.readStructEnd();
  return;
};

CompondGroupQuery_Req.prototype.write = function(output) {
  output.writeStructBegin('CompondGroupQuery_Req');
  if (this.startQuery !== null && this.startQuery !== undefined) {
    output.writeFieldBegin('startQuery', Thrift.Type.STRUCT, 1);
    this.startQuery.write(output);
    output.writeFieldEnd();
  }
  if (this.stepQuery !== null && this.stepQuery !== undefined) {
    output.writeFieldBegin('stepQuery', Thrift.Type.STRUCT, 2);
    this.stepQuery.write(output);
    output.writeFieldEnd();
  }
  output.writeFieldStop();
  output.writeStructEnd();
  return;
};

var TagJob4WanderWorkRestrain = module.exports.TagJob4WanderWorkRestrain = function(args) {
  this.restrains = null;
  this.restrainLogic = null;
  if (args) {
    if (args.restrains !== undefined && args.restrains !== null) {
      this.restrains = Thrift.copyList(args.restrains, [ttypes.ReadRegulerQuery_Req]);
    }
    if (args.restrainLogic !== undefined && args.restrainLogic !== null) {
      this.restrainLogic = args.restrainLogic;
    }
  }
};
TagJob4WanderWorkRestrain.prototype = {};
TagJob4WanderWorkRestrain.prototype.read = function(input) {
  input.readStructBegin();
  while (true) {
    var ret = input.readFieldBegin();
    var ftype = ret.ftype;
    var fid = ret.fid;
    if (ftype == Thrift.Type.STOP) {
      break;
    }
    switch (fid) {
      case 1:
      if (ftype == Thrift.Type.LIST) {
        this.restrains = [];
        var _rtmp3127 = input.readListBegin();
        var _size126 = _rtmp3127.size || 0;
        for (var _i128 = 0; _i128 < _size126; ++_i128) {
          var elem129 = null;
          elem129 = new ttypes.ReadRegulerQuery_Req();
          elem129.read(input);
          this.restrains.push(elem129);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 2:
      if (ftype == Thrift.Type.I32) {
        this.restrainLogic = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      default:
        input.skip(ftype);
    }
    input.readFieldEnd();
  }
  input.readStructEnd();
  return;
};

TagJob4WanderWorkRestrain.prototype.write = function(output) {
  output.writeStructBegin('TagJob4WanderWorkRestrain');
  if (this.restrains !== null && this.restrains !== undefined) {
    output.writeFieldBegin('restrains', Thrift.Type.LIST, 1);
    output.writeListBegin(Thrift.Type.STRUCT, this.restrains.length);
    for (var iter130 in this.restrains) {
      if (this.restrains.hasOwnProperty(iter130)) {
        iter130 = this.restrains[iter130];
        iter130.write(output);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.restrainLogic !== null && this.restrainLogic !== undefined) {
    output.writeFieldBegin('restrainLogic', Thrift.Type.I32, 2);
    output.writeI32(this.restrainLogic);
    output.writeFieldEnd();
  }
  output.writeFieldStop();
  output.writeStructEnd();
  return;
};

var TagJob4WanderWork_Req = module.exports.TagJob4WanderWork_Req = function(args) {
  this.paths = null;
  this.jobid = null;
  this.resultEdge = null;
  this.logic = null;
  this.factEdge = null;
  this.restrainMap = null;
  this.ratio = null;
  this.single = null;
  this.threshold = null;
  this.brandId = null;
  this.userBrandEdgeName = null;
  if (args) {
    if (args.paths !== undefined && args.paths !== null) {
      this.paths = Thrift.copyList(args.paths, [ttypes.RecommendQuery_Req]);
    }
    if (args.jobid !== undefined && args.jobid !== null) {
      this.jobid = args.jobid;
    }
    if (args.resultEdge !== undefined && args.resultEdge !== null) {
      this.resultEdge = args.resultEdge;
    }
    if (args.logic !== undefined && args.logic !== null) {
      this.logic = args.logic;
    }
    if (args.factEdge !== undefined && args.factEdge !== null) {
      this.factEdge = args.factEdge;
    }
    if (args.restrainMap !== undefined && args.restrainMap !== null) {
      this.restrainMap = Thrift.copyMap(args.restrainMap, [ttypes.TagJob4WanderWorkRestrain]);
    }
    if (args.ratio !== undefined && args.ratio !== null) {
      this.ratio = args.ratio;
    }
    if (args.single !== undefined && args.single !== null) {
      this.single = args.single;
    }
    if (args.threshold !== undefined && args.threshold !== null) {
      this.threshold = args.threshold;
    }
    if (args.brandId !== undefined && args.brandId !== null) {
      this.brandId = args.brandId;
    }
    if (args.userBrandEdgeName !== undefined && args.userBrandEdgeName !== null) {
      this.userBrandEdgeName = args.userBrandEdgeName;
    }
  }
};
TagJob4WanderWork_Req.prototype = {};
TagJob4WanderWork_Req.prototype.read = function(input) {
  input.readStructBegin();
  while (true) {
    var ret = input.readFieldBegin();
    var ftype = ret.ftype;
    var fid = ret.fid;
    if (ftype == Thrift.Type.STOP) {
      break;
    }
    switch (fid) {
      case 1:
      if (ftype == Thrift.Type.LIST) {
        this.paths = [];
        var _rtmp3132 = input.readListBegin();
        var _size131 = _rtmp3132.size || 0;
        for (var _i133 = 0; _i133 < _size131; ++_i133) {
          var elem134 = null;
          elem134 = new ttypes.RecommendQuery_Req();
          elem134.read(input);
          this.paths.push(elem134);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 2:
      if (ftype == Thrift.Type.I64) {
        this.jobid = input.readI64();
      } else {
        input.skip(ftype);
      }
      break;
      case 3:
      if (ftype == Thrift.Type.STRING) {
        this.resultEdge = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      case 4:
      if (ftype == Thrift.Type.I32) {
        this.logic = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 5:
      if (ftype == Thrift.Type.STRING) {
        this.factEdge = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      case 6:
      if (ftype == Thrift.Type.MAP) {
        this.restrainMap = {};
        var _rtmp3136 = input.readMapBegin();
        var _size135 = _rtmp3136.size || 0;
        for (var _i137 = 0; _i137 < _size135; ++_i137) {
          var key138 = null;
          var val139 = null;
          key138 = input.readI64();
          val139 = new ttypes.TagJob4WanderWorkRestrain();
          val139.read(input);
          this.restrainMap[key138] = val139;
        }
        input.readMapEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 7:
      if (ftype == Thrift.Type.DOUBLE) {
        this.ratio = input.readDouble();
      } else {
        input.skip(ftype);
      }
      break;
      case 8:
      if (ftype == Thrift.Type.I32) {
        this.single = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 9:
      if (ftype == Thrift.Type.DOUBLE) {
        this.threshold = input.readDouble();
      } else {
        input.skip(ftype);
      }
      break;
      case 10:
      if (ftype == Thrift.Type.I32) {
        this.brandId = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 11:
      if (ftype == Thrift.Type.STRING) {
        this.userBrandEdgeName = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      default:
        input.skip(ftype);
    }
    input.readFieldEnd();
  }
  input.readStructEnd();
  return;
};

TagJob4WanderWork_Req.prototype.write = function(output) {
  output.writeStructBegin('TagJob4WanderWork_Req');
  if (this.paths !== null && this.paths !== undefined) {
    output.writeFieldBegin('paths', Thrift.Type.LIST, 1);
    output.writeListBegin(Thrift.Type.STRUCT, this.paths.length);
    for (var iter140 in this.paths) {
      if (this.paths.hasOwnProperty(iter140)) {
        iter140 = this.paths[iter140];
        iter140.write(output);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.jobid !== null && this.jobid !== undefined) {
    output.writeFieldBegin('jobid', Thrift.Type.I64, 2);
    output.writeI64(this.jobid);
    output.writeFieldEnd();
  }
  if (this.resultEdge !== null && this.resultEdge !== undefined) {
    output.writeFieldBegin('resultEdge', Thrift.Type.STRING, 3);
    output.writeString(this.resultEdge);
    output.writeFieldEnd();
  }
  if (this.logic !== null && this.logic !== undefined) {
    output.writeFieldBegin('logic', Thrift.Type.I32, 4);
    output.writeI32(this.logic);
    output.writeFieldEnd();
  }
  if (this.factEdge !== null && this.factEdge !== undefined) {
    output.writeFieldBegin('factEdge', Thrift.Type.STRING, 5);
    output.writeString(this.factEdge);
    output.writeFieldEnd();
  }
  if (this.restrainMap !== null && this.restrainMap !== undefined) {
    output.writeFieldBegin('restrainMap', Thrift.Type.MAP, 6);
    output.writeMapBegin(Thrift.Type.I64, Thrift.Type.STRUCT, Thrift.objectLength(this.restrainMap));
    for (var kiter141 in this.restrainMap) {
      if (this.restrainMap.hasOwnProperty(kiter141)) {
        var viter142 = this.restrainMap[kiter141];
        output.writeI64(kiter141);
        viter142.write(output);
      }
    }
    output.writeMapEnd();
    output.writeFieldEnd();
  }
  if (this.ratio !== null && this.ratio !== undefined) {
    output.writeFieldBegin('ratio', Thrift.Type.DOUBLE, 7);
    output.writeDouble(this.ratio);
    output.writeFieldEnd();
  }
  if (this.single !== null && this.single !== undefined) {
    output.writeFieldBegin('single', Thrift.Type.I32, 8);
    output.writeI32(this.single);
    output.writeFieldEnd();
  }
  if (this.threshold !== null && this.threshold !== undefined) {
    output.writeFieldBegin('threshold', Thrift.Type.DOUBLE, 9);
    output.writeDouble(this.threshold);
    output.writeFieldEnd();
  }
  if (this.brandId !== null && this.brandId !== undefined) {
    output.writeFieldBegin('brandId', Thrift.Type.I32, 10);
    output.writeI32(this.brandId);
    output.writeFieldEnd();
  }
  if (this.userBrandEdgeName !== null && this.userBrandEdgeName !== undefined) {
    output.writeFieldBegin('userBrandEdgeName', Thrift.Type.STRING, 11);
    output.writeString(this.userBrandEdgeName);
    output.writeFieldEnd();
  }
  output.writeFieldStop();
  output.writeStructEnd();
  return;
};

var TagJob4Custom1_Req = module.exports.TagJob4Custom1_Req = function(args) {
  this.paths = null;
  this.jobid = null;
  this.resultEdge = null;
  this.brandMap = null;
  this.brandAttrName = null;
  this.ratio = null;
  this.single = null;
  this.brandId = null;
  this.userBrandEdgeName = null;
  this.brandEdgeFlag = 1;
  this.brandStep = 1;
  if (args) {
    if (args.paths !== undefined && args.paths !== null) {
      this.paths = Thrift.copyList(args.paths, [ttypes.RecommendQuery_Req]);
    }
    if (args.jobid !== undefined && args.jobid !== null) {
      this.jobid = args.jobid;
    }
    if (args.resultEdge !== undefined && args.resultEdge !== null) {
      this.resultEdge = args.resultEdge;
    }
    if (args.brandMap !== undefined && args.brandMap !== null) {
      this.brandMap = Thrift.copyMap(args.brandMap, [null]);
    }
    if (args.brandAttrName !== undefined && args.brandAttrName !== null) {
      this.brandAttrName = args.brandAttrName;
    }
    if (args.ratio !== undefined && args.ratio !== null) {
      this.ratio = args.ratio;
    }
    if (args.single !== undefined && args.single !== null) {
      this.single = args.single;
    }
    if (args.brandId !== undefined && args.brandId !== null) {
      this.brandId = args.brandId;
    }
    if (args.userBrandEdgeName !== undefined && args.userBrandEdgeName !== null) {
      this.userBrandEdgeName = args.userBrandEdgeName;
    }
    if (args.brandEdgeFlag !== undefined && args.brandEdgeFlag !== null) {
      this.brandEdgeFlag = args.brandEdgeFlag;
    }
    if (args.brandStep !== undefined && args.brandStep !== null) {
      this.brandStep = args.brandStep;
    }
  }
};
TagJob4Custom1_Req.prototype = {};
TagJob4Custom1_Req.prototype.read = function(input) {
  input.readStructBegin();
  while (true) {
    var ret = input.readFieldBegin();
    var ftype = ret.ftype;
    var fid = ret.fid;
    if (ftype == Thrift.Type.STOP) {
      break;
    }
    switch (fid) {
      case 1:
      if (ftype == Thrift.Type.LIST) {
        this.paths = [];
        var _rtmp3144 = input.readListBegin();
        var _size143 = _rtmp3144.size || 0;
        for (var _i145 = 0; _i145 < _size143; ++_i145) {
          var elem146 = null;
          elem146 = new ttypes.RecommendQuery_Req();
          elem146.read(input);
          this.paths.push(elem146);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 2:
      if (ftype == Thrift.Type.I64) {
        this.jobid = input.readI64();
      } else {
        input.skip(ftype);
      }
      break;
      case 3:
      if (ftype == Thrift.Type.STRING) {
        this.resultEdge = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      case 4:
      if (ftype == Thrift.Type.MAP) {
        this.brandMap = {};
        var _rtmp3148 = input.readMapBegin();
        var _size147 = _rtmp3148.size || 0;
        for (var _i149 = 0; _i149 < _size147; ++_i149) {
          var key150 = null;
          var val151 = null;
          key150 = input.readI64();
          val151 = input.readI32();
          this.brandMap[key150] = val151;
        }
        input.readMapEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 5:
      if (ftype == Thrift.Type.STRING) {
        this.brandAttrName = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      case 6:
      if (ftype == Thrift.Type.DOUBLE) {
        this.ratio = input.readDouble();
      } else {
        input.skip(ftype);
      }
      break;
      case 7:
      if (ftype == Thrift.Type.I32) {
        this.single = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 8:
      if (ftype == Thrift.Type.I32) {
        this.brandId = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 9:
      if (ftype == Thrift.Type.STRING) {
        this.userBrandEdgeName = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      case 10:
      if (ftype == Thrift.Type.I32) {
        this.brandEdgeFlag = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 11:
      if (ftype == Thrift.Type.I32) {
        this.brandStep = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      default:
        input.skip(ftype);
    }
    input.readFieldEnd();
  }
  input.readStructEnd();
  return;
};

TagJob4Custom1_Req.prototype.write = function(output) {
  output.writeStructBegin('TagJob4Custom1_Req');
  if (this.paths !== null && this.paths !== undefined) {
    output.writeFieldBegin('paths', Thrift.Type.LIST, 1);
    output.writeListBegin(Thrift.Type.STRUCT, this.paths.length);
    for (var iter152 in this.paths) {
      if (this.paths.hasOwnProperty(iter152)) {
        iter152 = this.paths[iter152];
        iter152.write(output);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.jobid !== null && this.jobid !== undefined) {
    output.writeFieldBegin('jobid', Thrift.Type.I64, 2);
    output.writeI64(this.jobid);
    output.writeFieldEnd();
  }
  if (this.resultEdge !== null && this.resultEdge !== undefined) {
    output.writeFieldBegin('resultEdge', Thrift.Type.STRING, 3);
    output.writeString(this.resultEdge);
    output.writeFieldEnd();
  }
  if (this.brandMap !== null && this.brandMap !== undefined) {
    output.writeFieldBegin('brandMap', Thrift.Type.MAP, 4);
    output.writeMapBegin(Thrift.Type.I64, Thrift.Type.I32, Thrift.objectLength(this.brandMap));
    for (var kiter153 in this.brandMap) {
      if (this.brandMap.hasOwnProperty(kiter153)) {
        var viter154 = this.brandMap[kiter153];
        output.writeI64(kiter153);
        output.writeI32(viter154);
      }
    }
    output.writeMapEnd();
    output.writeFieldEnd();
  }
  if (this.brandAttrName !== null && this.brandAttrName !== undefined) {
    output.writeFieldBegin('brandAttrName', Thrift.Type.STRING, 5);
    output.writeString(this.brandAttrName);
    output.writeFieldEnd();
  }
  if (this.ratio !== null && this.ratio !== undefined) {
    output.writeFieldBegin('ratio', Thrift.Type.DOUBLE, 6);
    output.writeDouble(this.ratio);
    output.writeFieldEnd();
  }
  if (this.single !== null && this.single !== undefined) {
    output.writeFieldBegin('single', Thrift.Type.I32, 7);
    output.writeI32(this.single);
    output.writeFieldEnd();
  }
  if (this.brandId !== null && this.brandId !== undefined) {
    output.writeFieldBegin('brandId', Thrift.Type.I32, 8);
    output.writeI32(this.brandId);
    output.writeFieldEnd();
  }
  if (this.userBrandEdgeName !== null && this.userBrandEdgeName !== undefined) {
    output.writeFieldBegin('userBrandEdgeName', Thrift.Type.STRING, 9);
    output.writeString(this.userBrandEdgeName);
    output.writeFieldEnd();
  }
  if (this.brandEdgeFlag !== null && this.brandEdgeFlag !== undefined) {
    output.writeFieldBegin('brandEdgeFlag', Thrift.Type.I32, 10);
    output.writeI32(this.brandEdgeFlag);
    output.writeFieldEnd();
  }
  if (this.brandStep !== null && this.brandStep !== undefined) {
    output.writeFieldBegin('brandStep', Thrift.Type.I32, 11);
    output.writeI32(this.brandStep);
    output.writeFieldEnd();
  }
  output.writeFieldStop();
  output.writeStructEnd();
  return;
};

var ReadRegulerJobQuery_Req = module.exports.ReadRegulerJobQuery_Req = function(args) {
  this.graph = null;
  this.startIds = null;
  this.throughEdges = null;
  this.edgeAttrGroup = null;
  this.vertexAttrGroup = null;
  this.filterVector = null;
  this.expVertexAttrGroup = null;
  this.jobid = null;
  if (args) {
    if (args.graph !== undefined && args.graph !== null) {
      this.graph = args.graph;
    }
    if (args.startIds !== undefined && args.startIds !== null) {
      this.startIds = Thrift.copyList(args.startIds, [null]);
    }
    if (args.throughEdges !== undefined && args.throughEdges !== null) {
      this.throughEdges = Thrift.copyList(args.throughEdges, [null]);
    }
    if (args.edgeAttrGroup !== undefined && args.edgeAttrGroup !== null) {
      this.edgeAttrGroup = Thrift.copyMap(args.edgeAttrGroup, [Thrift.copyList, null]);
    }
    if (args.vertexAttrGroup !== undefined && args.vertexAttrGroup !== null) {
      this.vertexAttrGroup = Thrift.copyMap(args.vertexAttrGroup, [Thrift.copyList, null]);
    }
    if (args.filterVector !== undefined && args.filterVector !== null) {
      this.filterVector = Thrift.copyList(args.filterVector, [ttypes.ReadFilterSet_Req]);
    }
    if (args.expVertexAttrGroup !== undefined && args.expVertexAttrGroup !== null) {
      this.expVertexAttrGroup = Thrift.copyMap(args.expVertexAttrGroup, [Thrift.copyList, null]);
    }
    if (args.jobid !== undefined && args.jobid !== null) {
      this.jobid = args.jobid;
    }
  }
};
ReadRegulerJobQuery_Req.prototype = {};
ReadRegulerJobQuery_Req.prototype.read = function(input) {
  input.readStructBegin();
  while (true) {
    var ret = input.readFieldBegin();
    var ftype = ret.ftype;
    var fid = ret.fid;
    if (ftype == Thrift.Type.STOP) {
      break;
    }
    switch (fid) {
      case 1:
      if (ftype == Thrift.Type.STRING) {
        this.graph = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      case 2:
      if (ftype == Thrift.Type.LIST) {
        this.startIds = [];
        var _rtmp3156 = input.readListBegin();
        var _size155 = _rtmp3156.size || 0;
        for (var _i157 = 0; _i157 < _size155; ++_i157) {
          var elem158 = null;
          elem158 = input.readI64();
          this.startIds.push(elem158);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 3:
      if (ftype == Thrift.Type.LIST) {
        this.throughEdges = [];
        var _rtmp3160 = input.readListBegin();
        var _size159 = _rtmp3160.size || 0;
        for (var _i161 = 0; _i161 < _size159; ++_i161) {
          var elem162 = null;
          elem162 = input.readString();
          this.throughEdges.push(elem162);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 4:
      if (ftype == Thrift.Type.MAP) {
        this.edgeAttrGroup = {};
        var _rtmp3164 = input.readMapBegin();
        var _size163 = _rtmp3164.size || 0;
        for (var _i165 = 0; _i165 < _size163; ++_i165) {
          var key166 = null;
          var val167 = null;
          key166 = input.readI32();
          val167 = [];
          var _rtmp3169 = input.readListBegin();
          var _size168 = _rtmp3169.size || 0;
          for (var _i170 = 0; _i170 < _size168; ++_i170) {
            var elem171 = null;
            elem171 = input.readString();
            val167.push(elem171);
          }
          input.readListEnd();
          this.edgeAttrGroup[key166] = val167;
        }
        input.readMapEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 5:
      if (ftype == Thrift.Type.MAP) {
        this.vertexAttrGroup = {};
        var _rtmp3173 = input.readMapBegin();
        var _size172 = _rtmp3173.size || 0;
        for (var _i174 = 0; _i174 < _size172; ++_i174) {
          var key175 = null;
          var val176 = null;
          key175 = input.readI32();
          val176 = [];
          var _rtmp3178 = input.readListBegin();
          var _size177 = _rtmp3178.size || 0;
          for (var _i179 = 0; _i179 < _size177; ++_i179) {
            var elem180 = null;
            elem180 = input.readString();
            val176.push(elem180);
          }
          input.readListEnd();
          this.vertexAttrGroup[key175] = val176;
        }
        input.readMapEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 6:
      if (ftype == Thrift.Type.LIST) {
        this.filterVector = [];
        var _rtmp3182 = input.readListBegin();
        var _size181 = _rtmp3182.size || 0;
        for (var _i183 = 0; _i183 < _size181; ++_i183) {
          var elem184 = null;
          elem184 = new ttypes.ReadFilterSet_Req();
          elem184.read(input);
          this.filterVector.push(elem184);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 7:
      if (ftype == Thrift.Type.MAP) {
        this.expVertexAttrGroup = {};
        var _rtmp3186 = input.readMapBegin();
        var _size185 = _rtmp3186.size || 0;
        for (var _i187 = 0; _i187 < _size185; ++_i187) {
          var key188 = null;
          var val189 = null;
          key188 = input.readString();
          val189 = [];
          var _rtmp3191 = input.readListBegin();
          var _size190 = _rtmp3191.size || 0;
          for (var _i192 = 0; _i192 < _size190; ++_i192) {
            var elem193 = null;
            elem193 = input.readString();
            val189.push(elem193);
          }
          input.readListEnd();
          this.expVertexAttrGroup[key188] = val189;
        }
        input.readMapEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 8:
      if (ftype == Thrift.Type.I64) {
        this.jobid = input.readI64();
      } else {
        input.skip(ftype);
      }
      break;
      default:
        input.skip(ftype);
    }
    input.readFieldEnd();
  }
  input.readStructEnd();
  return;
};

ReadRegulerJobQuery_Req.prototype.write = function(output) {
  output.writeStructBegin('ReadRegulerJobQuery_Req');
  if (this.graph !== null && this.graph !== undefined) {
    output.writeFieldBegin('graph', Thrift.Type.STRING, 1);
    output.writeString(this.graph);
    output.writeFieldEnd();
  }
  if (this.startIds !== null && this.startIds !== undefined) {
    output.writeFieldBegin('startIds', Thrift.Type.LIST, 2);
    output.writeListBegin(Thrift.Type.I64, this.startIds.length);
    for (var iter194 in this.startIds) {
      if (this.startIds.hasOwnProperty(iter194)) {
        iter194 = this.startIds[iter194];
        output.writeI64(iter194);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.throughEdges !== null && this.throughEdges !== undefined) {
    output.writeFieldBegin('throughEdges', Thrift.Type.LIST, 3);
    output.writeListBegin(Thrift.Type.STRING, this.throughEdges.length);
    for (var iter195 in this.throughEdges) {
      if (this.throughEdges.hasOwnProperty(iter195)) {
        iter195 = this.throughEdges[iter195];
        output.writeString(iter195);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.edgeAttrGroup !== null && this.edgeAttrGroup !== undefined) {
    output.writeFieldBegin('edgeAttrGroup', Thrift.Type.MAP, 4);
    output.writeMapBegin(Thrift.Type.I32, Thrift.Type.LIST, Thrift.objectLength(this.edgeAttrGroup));
    for (var kiter196 in this.edgeAttrGroup) {
      if (this.edgeAttrGroup.hasOwnProperty(kiter196)) {
        var viter197 = this.edgeAttrGroup[kiter196];
        output.writeI32(kiter196);
        output.writeListBegin(Thrift.Type.STRING, viter197.length);
        for (var iter198 in viter197) {
          if (viter197.hasOwnProperty(iter198)) {
            iter198 = viter197[iter198];
            output.writeString(iter198);
          }
        }
        output.writeListEnd();
      }
    }
    output.writeMapEnd();
    output.writeFieldEnd();
  }
  if (this.vertexAttrGroup !== null && this.vertexAttrGroup !== undefined) {
    output.writeFieldBegin('vertexAttrGroup', Thrift.Type.MAP, 5);
    output.writeMapBegin(Thrift.Type.I32, Thrift.Type.LIST, Thrift.objectLength(this.vertexAttrGroup));
    for (var kiter199 in this.vertexAttrGroup) {
      if (this.vertexAttrGroup.hasOwnProperty(kiter199)) {
        var viter200 = this.vertexAttrGroup[kiter199];
        output.writeI32(kiter199);
        output.writeListBegin(Thrift.Type.STRING, viter200.length);
        for (var iter201 in viter200) {
          if (viter200.hasOwnProperty(iter201)) {
            iter201 = viter200[iter201];
            output.writeString(iter201);
          }
        }
        output.writeListEnd();
      }
    }
    output.writeMapEnd();
    output.writeFieldEnd();
  }
  if (this.filterVector !== null && this.filterVector !== undefined) {
    output.writeFieldBegin('filterVector', Thrift.Type.LIST, 6);
    output.writeListBegin(Thrift.Type.STRUCT, this.filterVector.length);
    for (var iter202 in this.filterVector) {
      if (this.filterVector.hasOwnProperty(iter202)) {
        iter202 = this.filterVector[iter202];
        iter202.write(output);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.expVertexAttrGroup !== null && this.expVertexAttrGroup !== undefined) {
    output.writeFieldBegin('expVertexAttrGroup', Thrift.Type.MAP, 7);
    output.writeMapBegin(Thrift.Type.STRING, Thrift.Type.LIST, Thrift.objectLength(this.expVertexAttrGroup));
    for (var kiter203 in this.expVertexAttrGroup) {
      if (this.expVertexAttrGroup.hasOwnProperty(kiter203)) {
        var viter204 = this.expVertexAttrGroup[kiter203];
        output.writeString(kiter203);
        output.writeListBegin(Thrift.Type.STRING, viter204.length);
        for (var iter205 in viter204) {
          if (viter204.hasOwnProperty(iter205)) {
            iter205 = viter204[iter205];
            output.writeString(iter205);
          }
        }
        output.writeListEnd();
      }
    }
    output.writeMapEnd();
    output.writeFieldEnd();
  }
  if (this.jobid !== null && this.jobid !== undefined) {
    output.writeFieldBegin('jobid', Thrift.Type.I64, 8);
    output.writeI64(this.jobid);
    output.writeFieldEnd();
  }
  output.writeFieldStop();
  output.writeStructEnd();
  return;
};

var ReadRegulerJob_BranchEdge_Req = module.exports.ReadRegulerJob_BranchEdge_Req = function(args) {
  this.edgename = null;
  this.filter = null;
  this.edgeAttrs = null;
  this.vertexAttrs = null;
  if (args) {
    if (args.edgename !== undefined && args.edgename !== null) {
      this.edgename = args.edgename;
    }
    if (args.filter !== undefined && args.filter !== null) {
      this.filter = new ttypes.ReadFilterSet_Req(args.filter);
    }
    if (args.edgeAttrs !== undefined && args.edgeAttrs !== null) {
      this.edgeAttrs = Thrift.copyList(args.edgeAttrs, [null]);
    }
    if (args.vertexAttrs !== undefined && args.vertexAttrs !== null) {
      this.vertexAttrs = Thrift.copyList(args.vertexAttrs, [null]);
    }
  }
};
ReadRegulerJob_BranchEdge_Req.prototype = {};
ReadRegulerJob_BranchEdge_Req.prototype.read = function(input) {
  input.readStructBegin();
  while (true) {
    var ret = input.readFieldBegin();
    var ftype = ret.ftype;
    var fid = ret.fid;
    if (ftype == Thrift.Type.STOP) {
      break;
    }
    switch (fid) {
      case 1:
      if (ftype == Thrift.Type.STRING) {
        this.edgename = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      case 2:
      if (ftype == Thrift.Type.STRUCT) {
        this.filter = new ttypes.ReadFilterSet_Req();
        this.filter.read(input);
      } else {
        input.skip(ftype);
      }
      break;
      case 3:
      if (ftype == Thrift.Type.LIST) {
        this.edgeAttrs = [];
        var _rtmp3207 = input.readListBegin();
        var _size206 = _rtmp3207.size || 0;
        for (var _i208 = 0; _i208 < _size206; ++_i208) {
          var elem209 = null;
          elem209 = input.readString();
          this.edgeAttrs.push(elem209);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 4:
      if (ftype == Thrift.Type.LIST) {
        this.vertexAttrs = [];
        var _rtmp3211 = input.readListBegin();
        var _size210 = _rtmp3211.size || 0;
        for (var _i212 = 0; _i212 < _size210; ++_i212) {
          var elem213 = null;
          elem213 = input.readString();
          this.vertexAttrs.push(elem213);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      default:
        input.skip(ftype);
    }
    input.readFieldEnd();
  }
  input.readStructEnd();
  return;
};

ReadRegulerJob_BranchEdge_Req.prototype.write = function(output) {
  output.writeStructBegin('ReadRegulerJob_BranchEdge_Req');
  if (this.edgename !== null && this.edgename !== undefined) {
    output.writeFieldBegin('edgename', Thrift.Type.STRING, 1);
    output.writeString(this.edgename);
    output.writeFieldEnd();
  }
  if (this.filter !== null && this.filter !== undefined) {
    output.writeFieldBegin('filter', Thrift.Type.STRUCT, 2);
    this.filter.write(output);
    output.writeFieldEnd();
  }
  if (this.edgeAttrs !== null && this.edgeAttrs !== undefined) {
    output.writeFieldBegin('edgeAttrs', Thrift.Type.LIST, 3);
    output.writeListBegin(Thrift.Type.STRING, this.edgeAttrs.length);
    for (var iter214 in this.edgeAttrs) {
      if (this.edgeAttrs.hasOwnProperty(iter214)) {
        iter214 = this.edgeAttrs[iter214];
        output.writeString(iter214);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.vertexAttrs !== null && this.vertexAttrs !== undefined) {
    output.writeFieldBegin('vertexAttrs', Thrift.Type.LIST, 4);
    output.writeListBegin(Thrift.Type.STRING, this.vertexAttrs.length);
    for (var iter215 in this.vertexAttrs) {
      if (this.vertexAttrs.hasOwnProperty(iter215)) {
        iter215 = this.vertexAttrs[iter215];
        output.writeString(iter215);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  output.writeFieldStop();
  output.writeStructEnd();
  return;
};

var ReadRegulerJob_BranchPath_Req = module.exports.ReadRegulerJob_BranchPath_Req = function(args) {
  this.path = null;
  this.type = null;
  if (args) {
    if (args.path !== undefined && args.path !== null) {
      this.path = Thrift.copyList(args.path, [ttypes.ReadRegulerJob_BranchEdge_Req]);
    }
    if (args.type !== undefined && args.type !== null) {
      this.type = args.type;
    }
  }
};
ReadRegulerJob_BranchPath_Req.prototype = {};
ReadRegulerJob_BranchPath_Req.prototype.read = function(input) {
  input.readStructBegin();
  while (true) {
    var ret = input.readFieldBegin();
    var ftype = ret.ftype;
    var fid = ret.fid;
    if (ftype == Thrift.Type.STOP) {
      break;
    }
    switch (fid) {
      case 1:
      if (ftype == Thrift.Type.LIST) {
        this.path = [];
        var _rtmp3217 = input.readListBegin();
        var _size216 = _rtmp3217.size || 0;
        for (var _i218 = 0; _i218 < _size216; ++_i218) {
          var elem219 = null;
          elem219 = new ttypes.ReadRegulerJob_BranchEdge_Req();
          elem219.read(input);
          this.path.push(elem219);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 2:
      if (ftype == Thrift.Type.I32) {
        this.type = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      default:
        input.skip(ftype);
    }
    input.readFieldEnd();
  }
  input.readStructEnd();
  return;
};

ReadRegulerJob_BranchPath_Req.prototype.write = function(output) {
  output.writeStructBegin('ReadRegulerJob_BranchPath_Req');
  if (this.path !== null && this.path !== undefined) {
    output.writeFieldBegin('path', Thrift.Type.LIST, 1);
    output.writeListBegin(Thrift.Type.STRUCT, this.path.length);
    for (var iter220 in this.path) {
      if (this.path.hasOwnProperty(iter220)) {
        iter220 = this.path[iter220];
        iter220.write(output);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.type !== null && this.type !== undefined) {
    output.writeFieldBegin('type', Thrift.Type.I32, 2);
    output.writeI32(this.type);
    output.writeFieldEnd();
  }
  output.writeFieldStop();
  output.writeStructEnd();
  return;
};

var ReadRegulerJob_MainStep_Req = module.exports.ReadRegulerJob_MainStep_Req = function(args) {
  this.mainStep = null;
  this.branchPaths = null;
  if (args) {
    if (args.mainStep !== undefined && args.mainStep !== null) {
      this.mainStep = new ttypes.ReadRegulerJob_BranchEdge_Req(args.mainStep);
    }
    if (args.branchPaths !== undefined && args.branchPaths !== null) {
      this.branchPaths = Thrift.copyList(args.branchPaths, [ttypes.ReadRegulerJob_BranchPath_Req]);
    }
  }
};
ReadRegulerJob_MainStep_Req.prototype = {};
ReadRegulerJob_MainStep_Req.prototype.read = function(input) {
  input.readStructBegin();
  while (true) {
    var ret = input.readFieldBegin();
    var ftype = ret.ftype;
    var fid = ret.fid;
    if (ftype == Thrift.Type.STOP) {
      break;
    }
    switch (fid) {
      case 1:
      if (ftype == Thrift.Type.STRUCT) {
        this.mainStep = new ttypes.ReadRegulerJob_BranchEdge_Req();
        this.mainStep.read(input);
      } else {
        input.skip(ftype);
      }
      break;
      case 2:
      if (ftype == Thrift.Type.LIST) {
        this.branchPaths = [];
        var _rtmp3222 = input.readListBegin();
        var _size221 = _rtmp3222.size || 0;
        for (var _i223 = 0; _i223 < _size221; ++_i223) {
          var elem224 = null;
          elem224 = new ttypes.ReadRegulerJob_BranchPath_Req();
          elem224.read(input);
          this.branchPaths.push(elem224);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      default:
        input.skip(ftype);
    }
    input.readFieldEnd();
  }
  input.readStructEnd();
  return;
};

ReadRegulerJob_MainStep_Req.prototype.write = function(output) {
  output.writeStructBegin('ReadRegulerJob_MainStep_Req');
  if (this.mainStep !== null && this.mainStep !== undefined) {
    output.writeFieldBegin('mainStep', Thrift.Type.STRUCT, 1);
    this.mainStep.write(output);
    output.writeFieldEnd();
  }
  if (this.branchPaths !== null && this.branchPaths !== undefined) {
    output.writeFieldBegin('branchPaths', Thrift.Type.LIST, 2);
    output.writeListBegin(Thrift.Type.STRUCT, this.branchPaths.length);
    for (var iter225 in this.branchPaths) {
      if (this.branchPaths.hasOwnProperty(iter225)) {
        iter225 = this.branchPaths[iter225];
        iter225.write(output);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  output.writeFieldStop();
  output.writeStructEnd();
  return;
};

var ReadRegulerJobQueryV2_Req = module.exports.ReadRegulerJobQueryV2_Req = function(args) {
  this.graph = null;
  this.startIds = null;
  this.jobid = null;
  this.paths = null;
  this.vertexAttrs = null;
  if (args) {
    if (args.graph !== undefined && args.graph !== null) {
      this.graph = args.graph;
    }
    if (args.startIds !== undefined && args.startIds !== null) {
      this.startIds = Thrift.copyList(args.startIds, [null]);
    }
    if (args.jobid !== undefined && args.jobid !== null) {
      this.jobid = args.jobid;
    }
    if (args.paths !== undefined && args.paths !== null) {
      this.paths = Thrift.copyList(args.paths, [ttypes.ReadRegulerJob_MainStep_Req]);
    }
    if (args.vertexAttrs !== undefined && args.vertexAttrs !== null) {
      this.vertexAttrs = Thrift.copyList(args.vertexAttrs, [null]);
    }
  }
};
ReadRegulerJobQueryV2_Req.prototype = {};
ReadRegulerJobQueryV2_Req.prototype.read = function(input) {
  input.readStructBegin();
  while (true) {
    var ret = input.readFieldBegin();
    var ftype = ret.ftype;
    var fid = ret.fid;
    if (ftype == Thrift.Type.STOP) {
      break;
    }
    switch (fid) {
      case 1:
      if (ftype == Thrift.Type.STRING) {
        this.graph = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      case 2:
      if (ftype == Thrift.Type.LIST) {
        this.startIds = [];
        var _rtmp3227 = input.readListBegin();
        var _size226 = _rtmp3227.size || 0;
        for (var _i228 = 0; _i228 < _size226; ++_i228) {
          var elem229 = null;
          elem229 = input.readI64();
          this.startIds.push(elem229);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 3:
      if (ftype == Thrift.Type.I64) {
        this.jobid = input.readI64();
      } else {
        input.skip(ftype);
      }
      break;
      case 4:
      if (ftype == Thrift.Type.LIST) {
        this.paths = [];
        var _rtmp3231 = input.readListBegin();
        var _size230 = _rtmp3231.size || 0;
        for (var _i232 = 0; _i232 < _size230; ++_i232) {
          var elem233 = null;
          elem233 = new ttypes.ReadRegulerJob_MainStep_Req();
          elem233.read(input);
          this.paths.push(elem233);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 5:
      if (ftype == Thrift.Type.LIST) {
        this.vertexAttrs = [];
        var _rtmp3235 = input.readListBegin();
        var _size234 = _rtmp3235.size || 0;
        for (var _i236 = 0; _i236 < _size234; ++_i236) {
          var elem237 = null;
          elem237 = input.readString();
          this.vertexAttrs.push(elem237);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      default:
        input.skip(ftype);
    }
    input.readFieldEnd();
  }
  input.readStructEnd();
  return;
};

ReadRegulerJobQueryV2_Req.prototype.write = function(output) {
  output.writeStructBegin('ReadRegulerJobQueryV2_Req');
  if (this.graph !== null && this.graph !== undefined) {
    output.writeFieldBegin('graph', Thrift.Type.STRING, 1);
    output.writeString(this.graph);
    output.writeFieldEnd();
  }
  if (this.startIds !== null && this.startIds !== undefined) {
    output.writeFieldBegin('startIds', Thrift.Type.LIST, 2);
    output.writeListBegin(Thrift.Type.I64, this.startIds.length);
    for (var iter238 in this.startIds) {
      if (this.startIds.hasOwnProperty(iter238)) {
        iter238 = this.startIds[iter238];
        output.writeI64(iter238);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.jobid !== null && this.jobid !== undefined) {
    output.writeFieldBegin('jobid', Thrift.Type.I64, 3);
    output.writeI64(this.jobid);
    output.writeFieldEnd();
  }
  if (this.paths !== null && this.paths !== undefined) {
    output.writeFieldBegin('paths', Thrift.Type.LIST, 4);
    output.writeListBegin(Thrift.Type.STRUCT, this.paths.length);
    for (var iter239 in this.paths) {
      if (this.paths.hasOwnProperty(iter239)) {
        iter239 = this.paths[iter239];
        iter239.write(output);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.vertexAttrs !== null && this.vertexAttrs !== undefined) {
    output.writeFieldBegin('vertexAttrs', Thrift.Type.LIST, 5);
    output.writeListBegin(Thrift.Type.STRING, this.vertexAttrs.length);
    for (var iter240 in this.vertexAttrs) {
      if (this.vertexAttrs.hasOwnProperty(iter240)) {
        iter240 = this.vertexAttrs[iter240];
        output.writeString(iter240);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  output.writeFieldStop();
  output.writeStructEnd();
  return;
};

var TagJob4Custom2PathThreshold_Req = module.exports.TagJob4Custom2PathThreshold_Req = function(args) {
  this.filterType = -1;
  this.upper = null;
  this.lower = null;
  if (args) {
    if (args.filterType !== undefined && args.filterType !== null) {
      this.filterType = args.filterType;
    }
    if (args.upper !== undefined && args.upper !== null) {
      this.upper = args.upper;
    }
    if (args.lower !== undefined && args.lower !== null) {
      this.lower = args.lower;
    }
  }
};
TagJob4Custom2PathThreshold_Req.prototype = {};
TagJob4Custom2PathThreshold_Req.prototype.read = function(input) {
  input.readStructBegin();
  while (true) {
    var ret = input.readFieldBegin();
    var ftype = ret.ftype;
    var fid = ret.fid;
    if (ftype == Thrift.Type.STOP) {
      break;
    }
    switch (fid) {
      case 1:
      if (ftype == Thrift.Type.I32) {
        this.filterType = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 2:
      if (ftype == Thrift.Type.DOUBLE) {
        this.upper = input.readDouble();
      } else {
        input.skip(ftype);
      }
      break;
      case 3:
      if (ftype == Thrift.Type.DOUBLE) {
        this.lower = input.readDouble();
      } else {
        input.skip(ftype);
      }
      break;
      default:
        input.skip(ftype);
    }
    input.readFieldEnd();
  }
  input.readStructEnd();
  return;
};

TagJob4Custom2PathThreshold_Req.prototype.write = function(output) {
  output.writeStructBegin('TagJob4Custom2PathThreshold_Req');
  if (this.filterType !== null && this.filterType !== undefined) {
    output.writeFieldBegin('filterType', Thrift.Type.I32, 1);
    output.writeI32(this.filterType);
    output.writeFieldEnd();
  }
  if (this.upper !== null && this.upper !== undefined) {
    output.writeFieldBegin('upper', Thrift.Type.DOUBLE, 2);
    output.writeDouble(this.upper);
    output.writeFieldEnd();
  }
  if (this.lower !== null && this.lower !== undefined) {
    output.writeFieldBegin('lower', Thrift.Type.DOUBLE, 3);
    output.writeDouble(this.lower);
    output.writeFieldEnd();
  }
  output.writeFieldStop();
  output.writeStructEnd();
  return;
};

var TagJob4Custom2PathThresholdSet_Req = module.exports.TagJob4Custom2PathThresholdSet_Req = function(args) {
  this.thresholds = null;
  this.relationType = null;
  if (args) {
    if (args.thresholds !== undefined && args.thresholds !== null) {
      this.thresholds = Thrift.copyList(args.thresholds, [ttypes.TagJob4Custom2PathThreshold_Req]);
    }
    if (args.relationType !== undefined && args.relationType !== null) {
      this.relationType = args.relationType;
    }
  }
};
TagJob4Custom2PathThresholdSet_Req.prototype = {};
TagJob4Custom2PathThresholdSet_Req.prototype.read = function(input) {
  input.readStructBegin();
  while (true) {
    var ret = input.readFieldBegin();
    var ftype = ret.ftype;
    var fid = ret.fid;
    if (ftype == Thrift.Type.STOP) {
      break;
    }
    switch (fid) {
      case 1:
      if (ftype == Thrift.Type.LIST) {
        this.thresholds = [];
        var _rtmp3242 = input.readListBegin();
        var _size241 = _rtmp3242.size || 0;
        for (var _i243 = 0; _i243 < _size241; ++_i243) {
          var elem244 = null;
          elem244 = new ttypes.TagJob4Custom2PathThreshold_Req();
          elem244.read(input);
          this.thresholds.push(elem244);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 2:
      if (ftype == Thrift.Type.I32) {
        this.relationType = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      default:
        input.skip(ftype);
    }
    input.readFieldEnd();
  }
  input.readStructEnd();
  return;
};

TagJob4Custom2PathThresholdSet_Req.prototype.write = function(output) {
  output.writeStructBegin('TagJob4Custom2PathThresholdSet_Req');
  if (this.thresholds !== null && this.thresholds !== undefined) {
    output.writeFieldBegin('thresholds', Thrift.Type.LIST, 1);
    output.writeListBegin(Thrift.Type.STRUCT, this.thresholds.length);
    for (var iter245 in this.thresholds) {
      if (this.thresholds.hasOwnProperty(iter245)) {
        iter245 = this.thresholds[iter245];
        iter245.write(output);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.relationType !== null && this.relationType !== undefined) {
    output.writeFieldBegin('relationType', Thrift.Type.I32, 2);
    output.writeI32(this.relationType);
    output.writeFieldEnd();
  }
  output.writeFieldStop();
  output.writeStructEnd();
  return;
};

var TagJob4Custom2PathFilter_Req = module.exports.TagJob4Custom2PathFilter_Req = function(args) {
  this.filterType = -1;
  this.fillType = 0;
  this.attrName = null;
  this.upper = null;
  this.lower = null;
  if (args) {
    if (args.filterType !== undefined && args.filterType !== null) {
      this.filterType = args.filterType;
    }
    if (args.fillType !== undefined && args.fillType !== null) {
      this.fillType = args.fillType;
    }
    if (args.attrName !== undefined && args.attrName !== null) {
      this.attrName = args.attrName;
    }
    if (args.upper !== undefined && args.upper !== null) {
      this.upper = args.upper;
    }
    if (args.lower !== undefined && args.lower !== null) {
      this.lower = args.lower;
    }
  }
};
TagJob4Custom2PathFilter_Req.prototype = {};
TagJob4Custom2PathFilter_Req.prototype.read = function(input) {
  input.readStructBegin();
  while (true) {
    var ret = input.readFieldBegin();
    var ftype = ret.ftype;
    var fid = ret.fid;
    if (ftype == Thrift.Type.STOP) {
      break;
    }
    switch (fid) {
      case 1:
      if (ftype == Thrift.Type.I32) {
        this.filterType = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 2:
      if (ftype == Thrift.Type.I32) {
        this.fillType = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 3:
      if (ftype == Thrift.Type.STRING) {
        this.attrName = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      case 4:
      if (ftype == Thrift.Type.STRING) {
        this.upper = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      case 5:
      if (ftype == Thrift.Type.STRING) {
        this.lower = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      default:
        input.skip(ftype);
    }
    input.readFieldEnd();
  }
  input.readStructEnd();
  return;
};

TagJob4Custom2PathFilter_Req.prototype.write = function(output) {
  output.writeStructBegin('TagJob4Custom2PathFilter_Req');
  if (this.filterType !== null && this.filterType !== undefined) {
    output.writeFieldBegin('filterType', Thrift.Type.I32, 1);
    output.writeI32(this.filterType);
    output.writeFieldEnd();
  }
  if (this.fillType !== null && this.fillType !== undefined) {
    output.writeFieldBegin('fillType', Thrift.Type.I32, 2);
    output.writeI32(this.fillType);
    output.writeFieldEnd();
  }
  if (this.attrName !== null && this.attrName !== undefined) {
    output.writeFieldBegin('attrName', Thrift.Type.STRING, 3);
    output.writeString(this.attrName);
    output.writeFieldEnd();
  }
  if (this.upper !== null && this.upper !== undefined) {
    output.writeFieldBegin('upper', Thrift.Type.STRING, 4);
    output.writeString(this.upper);
    output.writeFieldEnd();
  }
  if (this.lower !== null && this.lower !== undefined) {
    output.writeFieldBegin('lower', Thrift.Type.STRING, 5);
    output.writeString(this.lower);
    output.writeFieldEnd();
  }
  output.writeFieldStop();
  output.writeStructEnd();
  return;
};

var TagJob4Custom2PathFilterSet_Req = module.exports.TagJob4Custom2PathFilterSet_Req = function(args) {
  this.filters = null;
  this.relationType = null;
  if (args) {
    if (args.filters !== undefined && args.filters !== null) {
      this.filters = Thrift.copyList(args.filters, [ttypes.TagJob4Custom2PathFilter_Req]);
    }
    if (args.relationType !== undefined && args.relationType !== null) {
      this.relationType = args.relationType;
    }
  }
};
TagJob4Custom2PathFilterSet_Req.prototype = {};
TagJob4Custom2PathFilterSet_Req.prototype.read = function(input) {
  input.readStructBegin();
  while (true) {
    var ret = input.readFieldBegin();
    var ftype = ret.ftype;
    var fid = ret.fid;
    if (ftype == Thrift.Type.STOP) {
      break;
    }
    switch (fid) {
      case 1:
      if (ftype == Thrift.Type.LIST) {
        this.filters = [];
        var _rtmp3247 = input.readListBegin();
        var _size246 = _rtmp3247.size || 0;
        for (var _i248 = 0; _i248 < _size246; ++_i248) {
          var elem249 = null;
          elem249 = new ttypes.TagJob4Custom2PathFilter_Req();
          elem249.read(input);
          this.filters.push(elem249);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 2:
      if (ftype == Thrift.Type.I32) {
        this.relationType = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      default:
        input.skip(ftype);
    }
    input.readFieldEnd();
  }
  input.readStructEnd();
  return;
};

TagJob4Custom2PathFilterSet_Req.prototype.write = function(output) {
  output.writeStructBegin('TagJob4Custom2PathFilterSet_Req');
  if (this.filters !== null && this.filters !== undefined) {
    output.writeFieldBegin('filters', Thrift.Type.LIST, 1);
    output.writeListBegin(Thrift.Type.STRUCT, this.filters.length);
    for (var iter250 in this.filters) {
      if (this.filters.hasOwnProperty(iter250)) {
        iter250 = this.filters[iter250];
        iter250.write(output);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.relationType !== null && this.relationType !== undefined) {
    output.writeFieldBegin('relationType', Thrift.Type.I32, 2);
    output.writeI32(this.relationType);
    output.writeFieldEnd();
  }
  output.writeFieldStop();
  output.writeStructEnd();
  return;
};

var TagJob4Custom2Path_Req = module.exports.TagJob4Custom2Path_Req = function(args) {
  this.throughEdges = null;
  this.filters_up = null;
  this.filters_down = null;
  this.filterLogic_up = null;
  this.filterLogic_down = null;
  this.calcType = null;
  this.statisType_up = null;
  this.statisType_down = null;
  this.threshold = null;
  this.filters_spec_edge_up = null;
  this.filters_spec_edge_down = null;
  this.filters_spec_node_up = null;
  this.filters_spec_node_down = null;
  if (args) {
    if (args.throughEdges !== undefined && args.throughEdges !== null) {
      this.throughEdges = Thrift.copyList(args.throughEdges, [null]);
    }
    if (args.filters_up !== undefined && args.filters_up !== null) {
      this.filters_up = Thrift.copyList(args.filters_up, [ttypes.ReadFilterSet_Req]);
    }
    if (args.filters_down !== undefined && args.filters_down !== null) {
      this.filters_down = Thrift.copyList(args.filters_down, [ttypes.ReadFilterSet_Req]);
    }
    if (args.filterLogic_up !== undefined && args.filterLogic_up !== null) {
      this.filterLogic_up = args.filterLogic_up;
    }
    if (args.filterLogic_down !== undefined && args.filterLogic_down !== null) {
      this.filterLogic_down = args.filterLogic_down;
    }
    if (args.calcType !== undefined && args.calcType !== null) {
      this.calcType = args.calcType;
    }
    if (args.statisType_up !== undefined && args.statisType_up !== null) {
      this.statisType_up = args.statisType_up;
    }
    if (args.statisType_down !== undefined && args.statisType_down !== null) {
      this.statisType_down = args.statisType_down;
    }
    if (args.threshold !== undefined && args.threshold !== null) {
      this.threshold = new ttypes.TagJob4Custom2PathThreshold_Req(args.threshold);
    }
    if (args.filters_spec_edge_up !== undefined && args.filters_spec_edge_up !== null) {
      this.filters_spec_edge_up = Thrift.copyList(args.filters_spec_edge_up, [ttypes.TagJob4Custom2PathFilterSet_Req]);
    }
    if (args.filters_spec_edge_down !== undefined && args.filters_spec_edge_down !== null) {
      this.filters_spec_edge_down = Thrift.copyList(args.filters_spec_edge_down, [ttypes.TagJob4Custom2PathFilterSet_Req]);
    }
    if (args.filters_spec_node_up !== undefined && args.filters_spec_node_up !== null) {
      this.filters_spec_node_up = Thrift.copyList(args.filters_spec_node_up, [ttypes.TagJob4Custom2PathFilterSet_Req]);
    }
    if (args.filters_spec_node_down !== undefined && args.filters_spec_node_down !== null) {
      this.filters_spec_node_down = Thrift.copyList(args.filters_spec_node_down, [ttypes.TagJob4Custom2PathFilterSet_Req]);
    }
  }
};
TagJob4Custom2Path_Req.prototype = {};
TagJob4Custom2Path_Req.prototype.read = function(input) {
  input.readStructBegin();
  while (true) {
    var ret = input.readFieldBegin();
    var ftype = ret.ftype;
    var fid = ret.fid;
    if (ftype == Thrift.Type.STOP) {
      break;
    }
    switch (fid) {
      case 1:
      if (ftype == Thrift.Type.LIST) {
        this.throughEdges = [];
        var _rtmp3252 = input.readListBegin();
        var _size251 = _rtmp3252.size || 0;
        for (var _i253 = 0; _i253 < _size251; ++_i253) {
          var elem254 = null;
          elem254 = input.readString();
          this.throughEdges.push(elem254);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 2:
      if (ftype == Thrift.Type.LIST) {
        this.filters_up = [];
        var _rtmp3256 = input.readListBegin();
        var _size255 = _rtmp3256.size || 0;
        for (var _i257 = 0; _i257 < _size255; ++_i257) {
          var elem258 = null;
          elem258 = new ttypes.ReadFilterSet_Req();
          elem258.read(input);
          this.filters_up.push(elem258);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 3:
      if (ftype == Thrift.Type.LIST) {
        this.filters_down = [];
        var _rtmp3260 = input.readListBegin();
        var _size259 = _rtmp3260.size || 0;
        for (var _i261 = 0; _i261 < _size259; ++_i261) {
          var elem262 = null;
          elem262 = new ttypes.ReadFilterSet_Req();
          elem262.read(input);
          this.filters_down.push(elem262);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 4:
      if (ftype == Thrift.Type.I32) {
        this.filterLogic_up = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 5:
      if (ftype == Thrift.Type.I32) {
        this.filterLogic_down = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 6:
      if (ftype == Thrift.Type.I32) {
        this.calcType = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 7:
      if (ftype == Thrift.Type.I32) {
        this.statisType_up = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 8:
      if (ftype == Thrift.Type.I32) {
        this.statisType_down = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 9:
      if (ftype == Thrift.Type.STRUCT) {
        this.threshold = new ttypes.TagJob4Custom2PathThreshold_Req();
        this.threshold.read(input);
      } else {
        input.skip(ftype);
      }
      break;
      case 10:
      if (ftype == Thrift.Type.LIST) {
        this.filters_spec_edge_up = [];
        var _rtmp3264 = input.readListBegin();
        var _size263 = _rtmp3264.size || 0;
        for (var _i265 = 0; _i265 < _size263; ++_i265) {
          var elem266 = null;
          elem266 = new ttypes.TagJob4Custom2PathFilterSet_Req();
          elem266.read(input);
          this.filters_spec_edge_up.push(elem266);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 11:
      if (ftype == Thrift.Type.LIST) {
        this.filters_spec_edge_down = [];
        var _rtmp3268 = input.readListBegin();
        var _size267 = _rtmp3268.size || 0;
        for (var _i269 = 0; _i269 < _size267; ++_i269) {
          var elem270 = null;
          elem270 = new ttypes.TagJob4Custom2PathFilterSet_Req();
          elem270.read(input);
          this.filters_spec_edge_down.push(elem270);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 12:
      if (ftype == Thrift.Type.LIST) {
        this.filters_spec_node_up = [];
        var _rtmp3272 = input.readListBegin();
        var _size271 = _rtmp3272.size || 0;
        for (var _i273 = 0; _i273 < _size271; ++_i273) {
          var elem274 = null;
          elem274 = new ttypes.TagJob4Custom2PathFilterSet_Req();
          elem274.read(input);
          this.filters_spec_node_up.push(elem274);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 13:
      if (ftype == Thrift.Type.LIST) {
        this.filters_spec_node_down = [];
        var _rtmp3276 = input.readListBegin();
        var _size275 = _rtmp3276.size || 0;
        for (var _i277 = 0; _i277 < _size275; ++_i277) {
          var elem278 = null;
          elem278 = new ttypes.TagJob4Custom2PathFilterSet_Req();
          elem278.read(input);
          this.filters_spec_node_down.push(elem278);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      default:
        input.skip(ftype);
    }
    input.readFieldEnd();
  }
  input.readStructEnd();
  return;
};

TagJob4Custom2Path_Req.prototype.write = function(output) {
  output.writeStructBegin('TagJob4Custom2Path_Req');
  if (this.throughEdges !== null && this.throughEdges !== undefined) {
    output.writeFieldBegin('throughEdges', Thrift.Type.LIST, 1);
    output.writeListBegin(Thrift.Type.STRING, this.throughEdges.length);
    for (var iter279 in this.throughEdges) {
      if (this.throughEdges.hasOwnProperty(iter279)) {
        iter279 = this.throughEdges[iter279];
        output.writeString(iter279);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.filters_up !== null && this.filters_up !== undefined) {
    output.writeFieldBegin('filters_up', Thrift.Type.LIST, 2);
    output.writeListBegin(Thrift.Type.STRUCT, this.filters_up.length);
    for (var iter280 in this.filters_up) {
      if (this.filters_up.hasOwnProperty(iter280)) {
        iter280 = this.filters_up[iter280];
        iter280.write(output);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.filters_down !== null && this.filters_down !== undefined) {
    output.writeFieldBegin('filters_down', Thrift.Type.LIST, 3);
    output.writeListBegin(Thrift.Type.STRUCT, this.filters_down.length);
    for (var iter281 in this.filters_down) {
      if (this.filters_down.hasOwnProperty(iter281)) {
        iter281 = this.filters_down[iter281];
        iter281.write(output);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.filterLogic_up !== null && this.filterLogic_up !== undefined) {
    output.writeFieldBegin('filterLogic_up', Thrift.Type.I32, 4);
    output.writeI32(this.filterLogic_up);
    output.writeFieldEnd();
  }
  if (this.filterLogic_down !== null && this.filterLogic_down !== undefined) {
    output.writeFieldBegin('filterLogic_down', Thrift.Type.I32, 5);
    output.writeI32(this.filterLogic_down);
    output.writeFieldEnd();
  }
  if (this.calcType !== null && this.calcType !== undefined) {
    output.writeFieldBegin('calcType', Thrift.Type.I32, 6);
    output.writeI32(this.calcType);
    output.writeFieldEnd();
  }
  if (this.statisType_up !== null && this.statisType_up !== undefined) {
    output.writeFieldBegin('statisType_up', Thrift.Type.I32, 7);
    output.writeI32(this.statisType_up);
    output.writeFieldEnd();
  }
  if (this.statisType_down !== null && this.statisType_down !== undefined) {
    output.writeFieldBegin('statisType_down', Thrift.Type.I32, 8);
    output.writeI32(this.statisType_down);
    output.writeFieldEnd();
  }
  if (this.threshold !== null && this.threshold !== undefined) {
    output.writeFieldBegin('threshold', Thrift.Type.STRUCT, 9);
    this.threshold.write(output);
    output.writeFieldEnd();
  }
  if (this.filters_spec_edge_up !== null && this.filters_spec_edge_up !== undefined) {
    output.writeFieldBegin('filters_spec_edge_up', Thrift.Type.LIST, 10);
    output.writeListBegin(Thrift.Type.STRUCT, this.filters_spec_edge_up.length);
    for (var iter282 in this.filters_spec_edge_up) {
      if (this.filters_spec_edge_up.hasOwnProperty(iter282)) {
        iter282 = this.filters_spec_edge_up[iter282];
        iter282.write(output);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.filters_spec_edge_down !== null && this.filters_spec_edge_down !== undefined) {
    output.writeFieldBegin('filters_spec_edge_down', Thrift.Type.LIST, 11);
    output.writeListBegin(Thrift.Type.STRUCT, this.filters_spec_edge_down.length);
    for (var iter283 in this.filters_spec_edge_down) {
      if (this.filters_spec_edge_down.hasOwnProperty(iter283)) {
        iter283 = this.filters_spec_edge_down[iter283];
        iter283.write(output);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.filters_spec_node_up !== null && this.filters_spec_node_up !== undefined) {
    output.writeFieldBegin('filters_spec_node_up', Thrift.Type.LIST, 12);
    output.writeListBegin(Thrift.Type.STRUCT, this.filters_spec_node_up.length);
    for (var iter284 in this.filters_spec_node_up) {
      if (this.filters_spec_node_up.hasOwnProperty(iter284)) {
        iter284 = this.filters_spec_node_up[iter284];
        iter284.write(output);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.filters_spec_node_down !== null && this.filters_spec_node_down !== undefined) {
    output.writeFieldBegin('filters_spec_node_down', Thrift.Type.LIST, 13);
    output.writeListBegin(Thrift.Type.STRUCT, this.filters_spec_node_down.length);
    for (var iter285 in this.filters_spec_node_down) {
      if (this.filters_spec_node_down.hasOwnProperty(iter285)) {
        iter285 = this.filters_spec_node_down[iter285];
        iter285.write(output);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  output.writeFieldStop();
  output.writeStructEnd();
  return;
};

var TagJob4Custom2_Req = module.exports.TagJob4Custom2_Req = function(args) {
  this.graph = null;
  this.ratio = null;
  this.single = null;
  this.paths = null;
  this.thresholdLogic = null;
  this.thresholdSets = null;
  this.jobid = null;
  this.threadCnt = null;
  this.singleTagIdList = null;
  this.resultEdge = null;
  this.brandId = null;
  this.userBrandEdgeName = null;
  if (args) {
    if (args.graph !== undefined && args.graph !== null) {
      this.graph = args.graph;
    }
    if (args.ratio !== undefined && args.ratio !== null) {
      this.ratio = args.ratio;
    }
    if (args.single !== undefined && args.single !== null) {
      this.single = args.single;
    }
    if (args.paths !== undefined && args.paths !== null) {
      this.paths = Thrift.copyList(args.paths, [ttypes.TagJob4Custom2Path_Req]);
    }
    if (args.thresholdLogic !== undefined && args.thresholdLogic !== null) {
      this.thresholdLogic = args.thresholdLogic;
    }
    if (args.thresholdSets !== undefined && args.thresholdSets !== null) {
      this.thresholdSets = Thrift.copyList(args.thresholdSets, [ttypes.TagJob4Custom2PathThresholdSet_Req]);
    }
    if (args.jobid !== undefined && args.jobid !== null) {
      this.jobid = args.jobid;
    }
    if (args.threadCnt !== undefined && args.threadCnt !== null) {
      this.threadCnt = args.threadCnt;
    }
    if (args.singleTagIdList !== undefined && args.singleTagIdList !== null) {
      this.singleTagIdList = Thrift.copyList(args.singleTagIdList, [null]);
    }
    if (args.resultEdge !== undefined && args.resultEdge !== null) {
      this.resultEdge = args.resultEdge;
    }
    if (args.brandId !== undefined && args.brandId !== null) {
      this.brandId = args.brandId;
    }
    if (args.userBrandEdgeName !== undefined && args.userBrandEdgeName !== null) {
      this.userBrandEdgeName = args.userBrandEdgeName;
    }
  }
};
TagJob4Custom2_Req.prototype = {};
TagJob4Custom2_Req.prototype.read = function(input) {
  input.readStructBegin();
  while (true) {
    var ret = input.readFieldBegin();
    var ftype = ret.ftype;
    var fid = ret.fid;
    if (ftype == Thrift.Type.STOP) {
      break;
    }
    switch (fid) {
      case 1:
      if (ftype == Thrift.Type.STRING) {
        this.graph = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      case 2:
      if (ftype == Thrift.Type.DOUBLE) {
        this.ratio = input.readDouble();
      } else {
        input.skip(ftype);
      }
      break;
      case 3:
      if (ftype == Thrift.Type.I32) {
        this.single = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 4:
      if (ftype == Thrift.Type.LIST) {
        this.paths = [];
        var _rtmp3287 = input.readListBegin();
        var _size286 = _rtmp3287.size || 0;
        for (var _i288 = 0; _i288 < _size286; ++_i288) {
          var elem289 = null;
          elem289 = new ttypes.TagJob4Custom2Path_Req();
          elem289.read(input);
          this.paths.push(elem289);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 5:
      if (ftype == Thrift.Type.I32) {
        this.thresholdLogic = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 6:
      if (ftype == Thrift.Type.LIST) {
        this.thresholdSets = [];
        var _rtmp3291 = input.readListBegin();
        var _size290 = _rtmp3291.size || 0;
        for (var _i292 = 0; _i292 < _size290; ++_i292) {
          var elem293 = null;
          elem293 = new ttypes.TagJob4Custom2PathThresholdSet_Req();
          elem293.read(input);
          this.thresholdSets.push(elem293);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 7:
      if (ftype == Thrift.Type.I64) {
        this.jobid = input.readI64();
      } else {
        input.skip(ftype);
      }
      break;
      case 8:
      if (ftype == Thrift.Type.I32) {
        this.threadCnt = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 9:
      if (ftype == Thrift.Type.LIST) {
        this.singleTagIdList = [];
        var _rtmp3295 = input.readListBegin();
        var _size294 = _rtmp3295.size || 0;
        for (var _i296 = 0; _i296 < _size294; ++_i296) {
          var elem297 = null;
          elem297 = input.readI64();
          this.singleTagIdList.push(elem297);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 10:
      if (ftype == Thrift.Type.STRING) {
        this.resultEdge = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      case 11:
      if (ftype == Thrift.Type.I32) {
        this.brandId = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 12:
      if (ftype == Thrift.Type.STRING) {
        this.userBrandEdgeName = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      default:
        input.skip(ftype);
    }
    input.readFieldEnd();
  }
  input.readStructEnd();
  return;
};

TagJob4Custom2_Req.prototype.write = function(output) {
  output.writeStructBegin('TagJob4Custom2_Req');
  if (this.graph !== null && this.graph !== undefined) {
    output.writeFieldBegin('graph', Thrift.Type.STRING, 1);
    output.writeString(this.graph);
    output.writeFieldEnd();
  }
  if (this.ratio !== null && this.ratio !== undefined) {
    output.writeFieldBegin('ratio', Thrift.Type.DOUBLE, 2);
    output.writeDouble(this.ratio);
    output.writeFieldEnd();
  }
  if (this.single !== null && this.single !== undefined) {
    output.writeFieldBegin('single', Thrift.Type.I32, 3);
    output.writeI32(this.single);
    output.writeFieldEnd();
  }
  if (this.paths !== null && this.paths !== undefined) {
    output.writeFieldBegin('paths', Thrift.Type.LIST, 4);
    output.writeListBegin(Thrift.Type.STRUCT, this.paths.length);
    for (var iter298 in this.paths) {
      if (this.paths.hasOwnProperty(iter298)) {
        iter298 = this.paths[iter298];
        iter298.write(output);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.thresholdLogic !== null && this.thresholdLogic !== undefined) {
    output.writeFieldBegin('thresholdLogic', Thrift.Type.I32, 5);
    output.writeI32(this.thresholdLogic);
    output.writeFieldEnd();
  }
  if (this.thresholdSets !== null && this.thresholdSets !== undefined) {
    output.writeFieldBegin('thresholdSets', Thrift.Type.LIST, 6);
    output.writeListBegin(Thrift.Type.STRUCT, this.thresholdSets.length);
    for (var iter299 in this.thresholdSets) {
      if (this.thresholdSets.hasOwnProperty(iter299)) {
        iter299 = this.thresholdSets[iter299];
        iter299.write(output);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.jobid !== null && this.jobid !== undefined) {
    output.writeFieldBegin('jobid', Thrift.Type.I64, 7);
    output.writeI64(this.jobid);
    output.writeFieldEnd();
  }
  if (this.threadCnt !== null && this.threadCnt !== undefined) {
    output.writeFieldBegin('threadCnt', Thrift.Type.I32, 8);
    output.writeI32(this.threadCnt);
    output.writeFieldEnd();
  }
  if (this.singleTagIdList !== null && this.singleTagIdList !== undefined) {
    output.writeFieldBegin('singleTagIdList', Thrift.Type.LIST, 9);
    output.writeListBegin(Thrift.Type.I64, this.singleTagIdList.length);
    for (var iter300 in this.singleTagIdList) {
      if (this.singleTagIdList.hasOwnProperty(iter300)) {
        iter300 = this.singleTagIdList[iter300];
        output.writeI64(iter300);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.resultEdge !== null && this.resultEdge !== undefined) {
    output.writeFieldBegin('resultEdge', Thrift.Type.STRING, 10);
    output.writeString(this.resultEdge);
    output.writeFieldEnd();
  }
  if (this.brandId !== null && this.brandId !== undefined) {
    output.writeFieldBegin('brandId', Thrift.Type.I32, 11);
    output.writeI32(this.brandId);
    output.writeFieldEnd();
  }
  if (this.userBrandEdgeName !== null && this.userBrandEdgeName !== undefined) {
    output.writeFieldBegin('userBrandEdgeName', Thrift.Type.STRING, 12);
    output.writeString(this.userBrandEdgeName);
    output.writeFieldEnd();
  }
  output.writeFieldStop();
  output.writeStructEnd();
  return;
};

var TagJob4Custom4Path_Req = module.exports.TagJob4Custom4Path_Req = function(args) {
  this.throughEdges = null;
  this.filters = null;
  this.up_step = null;
  this.up_isEdge = null;
  this.up_attr = null;
  this.down_step = null;
  this.down_isEdge = null;
  this.down_attr = null;
  if (args) {
    if (args.throughEdges !== undefined && args.throughEdges !== null) {
      this.throughEdges = Thrift.copyList(args.throughEdges, [null]);
    }
    if (args.filters !== undefined && args.filters !== null) {
      this.filters = Thrift.copyList(args.filters, [ttypes.ReadFilterSet_Req]);
    }
    if (args.up_step !== undefined && args.up_step !== null) {
      this.up_step = args.up_step;
    }
    if (args.up_isEdge !== undefined && args.up_isEdge !== null) {
      this.up_isEdge = args.up_isEdge;
    }
    if (args.up_attr !== undefined && args.up_attr !== null) {
      this.up_attr = args.up_attr;
    }
    if (args.down_step !== undefined && args.down_step !== null) {
      this.down_step = args.down_step;
    }
    if (args.down_isEdge !== undefined && args.down_isEdge !== null) {
      this.down_isEdge = args.down_isEdge;
    }
    if (args.down_attr !== undefined && args.down_attr !== null) {
      this.down_attr = args.down_attr;
    }
  }
};
TagJob4Custom4Path_Req.prototype = {};
TagJob4Custom4Path_Req.prototype.read = function(input) {
  input.readStructBegin();
  while (true) {
    var ret = input.readFieldBegin();
    var ftype = ret.ftype;
    var fid = ret.fid;
    if (ftype == Thrift.Type.STOP) {
      break;
    }
    switch (fid) {
      case 1:
      if (ftype == Thrift.Type.LIST) {
        this.throughEdges = [];
        var _rtmp3302 = input.readListBegin();
        var _size301 = _rtmp3302.size || 0;
        for (var _i303 = 0; _i303 < _size301; ++_i303) {
          var elem304 = null;
          elem304 = input.readString();
          this.throughEdges.push(elem304);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 2:
      if (ftype == Thrift.Type.LIST) {
        this.filters = [];
        var _rtmp3306 = input.readListBegin();
        var _size305 = _rtmp3306.size || 0;
        for (var _i307 = 0; _i307 < _size305; ++_i307) {
          var elem308 = null;
          elem308 = new ttypes.ReadFilterSet_Req();
          elem308.read(input);
          this.filters.push(elem308);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 3:
      if (ftype == Thrift.Type.I32) {
        this.up_step = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 4:
      if (ftype == Thrift.Type.I32) {
        this.up_isEdge = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 5:
      if (ftype == Thrift.Type.STRING) {
        this.up_attr = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      case 6:
      if (ftype == Thrift.Type.I32) {
        this.down_step = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 7:
      if (ftype == Thrift.Type.I32) {
        this.down_isEdge = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 8:
      if (ftype == Thrift.Type.STRING) {
        this.down_attr = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      default:
        input.skip(ftype);
    }
    input.readFieldEnd();
  }
  input.readStructEnd();
  return;
};

TagJob4Custom4Path_Req.prototype.write = function(output) {
  output.writeStructBegin('TagJob4Custom4Path_Req');
  if (this.throughEdges !== null && this.throughEdges !== undefined) {
    output.writeFieldBegin('throughEdges', Thrift.Type.LIST, 1);
    output.writeListBegin(Thrift.Type.STRING, this.throughEdges.length);
    for (var iter309 in this.throughEdges) {
      if (this.throughEdges.hasOwnProperty(iter309)) {
        iter309 = this.throughEdges[iter309];
        output.writeString(iter309);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.filters !== null && this.filters !== undefined) {
    output.writeFieldBegin('filters', Thrift.Type.LIST, 2);
    output.writeListBegin(Thrift.Type.STRUCT, this.filters.length);
    for (var iter310 in this.filters) {
      if (this.filters.hasOwnProperty(iter310)) {
        iter310 = this.filters[iter310];
        iter310.write(output);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.up_step !== null && this.up_step !== undefined) {
    output.writeFieldBegin('up_step', Thrift.Type.I32, 3);
    output.writeI32(this.up_step);
    output.writeFieldEnd();
  }
  if (this.up_isEdge !== null && this.up_isEdge !== undefined) {
    output.writeFieldBegin('up_isEdge', Thrift.Type.I32, 4);
    output.writeI32(this.up_isEdge);
    output.writeFieldEnd();
  }
  if (this.up_attr !== null && this.up_attr !== undefined) {
    output.writeFieldBegin('up_attr', Thrift.Type.STRING, 5);
    output.writeString(this.up_attr);
    output.writeFieldEnd();
  }
  if (this.down_step !== null && this.down_step !== undefined) {
    output.writeFieldBegin('down_step', Thrift.Type.I32, 6);
    output.writeI32(this.down_step);
    output.writeFieldEnd();
  }
  if (this.down_isEdge !== null && this.down_isEdge !== undefined) {
    output.writeFieldBegin('down_isEdge', Thrift.Type.I32, 7);
    output.writeI32(this.down_isEdge);
    output.writeFieldEnd();
  }
  if (this.down_attr !== null && this.down_attr !== undefined) {
    output.writeFieldBegin('down_attr', Thrift.Type.STRING, 8);
    output.writeString(this.down_attr);
    output.writeFieldEnd();
  }
  output.writeFieldStop();
  output.writeStructEnd();
  return;
};

var TagJob4Custom4_Req = module.exports.TagJob4Custom4_Req = function(args) {
  this.graph = null;
  this.ratio = null;
  this.paths = null;
  this.jobid = null;
  if (args) {
    if (args.graph !== undefined && args.graph !== null) {
      this.graph = args.graph;
    }
    if (args.ratio !== undefined && args.ratio !== null) {
      this.ratio = args.ratio;
    }
    if (args.paths !== undefined && args.paths !== null) {
      this.paths = Thrift.copyList(args.paths, [ttypes.TagJob4Custom4Path_Req]);
    }
    if (args.jobid !== undefined && args.jobid !== null) {
      this.jobid = args.jobid;
    }
  }
};
TagJob4Custom4_Req.prototype = {};
TagJob4Custom4_Req.prototype.read = function(input) {
  input.readStructBegin();
  while (true) {
    var ret = input.readFieldBegin();
    var ftype = ret.ftype;
    var fid = ret.fid;
    if (ftype == Thrift.Type.STOP) {
      break;
    }
    switch (fid) {
      case 1:
      if (ftype == Thrift.Type.STRING) {
        this.graph = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      case 2:
      if (ftype == Thrift.Type.DOUBLE) {
        this.ratio = input.readDouble();
      } else {
        input.skip(ftype);
      }
      break;
      case 3:
      if (ftype == Thrift.Type.LIST) {
        this.paths = [];
        var _rtmp3312 = input.readListBegin();
        var _size311 = _rtmp3312.size || 0;
        for (var _i313 = 0; _i313 < _size311; ++_i313) {
          var elem314 = null;
          elem314 = new ttypes.TagJob4Custom4Path_Req();
          elem314.read(input);
          this.paths.push(elem314);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 4:
      if (ftype == Thrift.Type.I64) {
        this.jobid = input.readI64();
      } else {
        input.skip(ftype);
      }
      break;
      default:
        input.skip(ftype);
    }
    input.readFieldEnd();
  }
  input.readStructEnd();
  return;
};

TagJob4Custom4_Req.prototype.write = function(output) {
  output.writeStructBegin('TagJob4Custom4_Req');
  if (this.graph !== null && this.graph !== undefined) {
    output.writeFieldBegin('graph', Thrift.Type.STRING, 1);
    output.writeString(this.graph);
    output.writeFieldEnd();
  }
  if (this.ratio !== null && this.ratio !== undefined) {
    output.writeFieldBegin('ratio', Thrift.Type.DOUBLE, 2);
    output.writeDouble(this.ratio);
    output.writeFieldEnd();
  }
  if (this.paths !== null && this.paths !== undefined) {
    output.writeFieldBegin('paths', Thrift.Type.LIST, 3);
    output.writeListBegin(Thrift.Type.STRUCT, this.paths.length);
    for (var iter315 in this.paths) {
      if (this.paths.hasOwnProperty(iter315)) {
        iter315 = this.paths[iter315];
        iter315.write(output);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.jobid !== null && this.jobid !== undefined) {
    output.writeFieldBegin('jobid', Thrift.Type.I64, 4);
    output.writeI64(this.jobid);
    output.writeFieldEnd();
  }
  output.writeFieldStop();
  output.writeStructEnd();
  return;
};

var TagJob4Custom3_Res = module.exports.TagJob4Custom3_Res = function(args) {
  this.pathidx = null;
  this.uid = null;
  this.tagid = null;
  this.up = null;
  this.down = null;
  this.score = null;
  if (args) {
    if (args.pathidx !== undefined && args.pathidx !== null) {
      this.pathidx = args.pathidx;
    }
    if (args.uid !== undefined && args.uid !== null) {
      this.uid = args.uid;
    }
    if (args.tagid !== undefined && args.tagid !== null) {
      this.tagid = args.tagid;
    }
    if (args.up !== undefined && args.up !== null) {
      this.up = args.up;
    }
    if (args.down !== undefined && args.down !== null) {
      this.down = args.down;
    }
    if (args.score !== undefined && args.score !== null) {
      this.score = args.score;
    }
  }
};
TagJob4Custom3_Res.prototype = {};
TagJob4Custom3_Res.prototype.read = function(input) {
  input.readStructBegin();
  while (true) {
    var ret = input.readFieldBegin();
    var ftype = ret.ftype;
    var fid = ret.fid;
    if (ftype == Thrift.Type.STOP) {
      break;
    }
    switch (fid) {
      case 1:
      if (ftype == Thrift.Type.I32) {
        this.pathidx = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 2:
      if (ftype == Thrift.Type.I64) {
        this.uid = input.readI64();
      } else {
        input.skip(ftype);
      }
      break;
      case 3:
      if (ftype == Thrift.Type.I64) {
        this.tagid = input.readI64();
      } else {
        input.skip(ftype);
      }
      break;
      case 4:
      if (ftype == Thrift.Type.I64) {
        this.up = input.readI64();
      } else {
        input.skip(ftype);
      }
      break;
      case 5:
      if (ftype == Thrift.Type.I64) {
        this.down = input.readI64();
      } else {
        input.skip(ftype);
      }
      break;
      case 6:
      if (ftype == Thrift.Type.DOUBLE) {
        this.score = input.readDouble();
      } else {
        input.skip(ftype);
      }
      break;
      default:
        input.skip(ftype);
    }
    input.readFieldEnd();
  }
  input.readStructEnd();
  return;
};

TagJob4Custom3_Res.prototype.write = function(output) {
  output.writeStructBegin('TagJob4Custom3_Res');
  if (this.pathidx !== null && this.pathidx !== undefined) {
    output.writeFieldBegin('pathidx', Thrift.Type.I32, 1);
    output.writeI32(this.pathidx);
    output.writeFieldEnd();
  }
  if (this.uid !== null && this.uid !== undefined) {
    output.writeFieldBegin('uid', Thrift.Type.I64, 2);
    output.writeI64(this.uid);
    output.writeFieldEnd();
  }
  if (this.tagid !== null && this.tagid !== undefined) {
    output.writeFieldBegin('tagid', Thrift.Type.I64, 3);
    output.writeI64(this.tagid);
    output.writeFieldEnd();
  }
  if (this.up !== null && this.up !== undefined) {
    output.writeFieldBegin('up', Thrift.Type.I64, 4);
    output.writeI64(this.up);
    output.writeFieldEnd();
  }
  if (this.down !== null && this.down !== undefined) {
    output.writeFieldBegin('down', Thrift.Type.I64, 5);
    output.writeI64(this.down);
    output.writeFieldEnd();
  }
  if (this.score !== null && this.score !== undefined) {
    output.writeFieldBegin('score', Thrift.Type.DOUBLE, 6);
    output.writeDouble(this.score);
    output.writeFieldEnd();
  }
  output.writeFieldStop();
  output.writeStructEnd();
  return;
};

var FreeBroadPath_Req = module.exports.FreeBroadPath_Req = function(args) {
  this.throughEdges = null;
  this.edgefilters = null;
  this.throughVertexes = null;
  this.vertexfilters = null;
  this.startVertex = null;
  this.startIds = null;
  this.steplimit = 100;
  this.resultlimit = 10000;
  this.graph = null;
  if (args) {
    if (args.throughEdges !== undefined && args.throughEdges !== null) {
      this.throughEdges = Thrift.copyList(args.throughEdges, [null]);
    }
    if (args.edgefilters !== undefined && args.edgefilters !== null) {
      this.edgefilters = Thrift.copyList(args.edgefilters, [ttypes.ReadFilterSet_Req]);
    }
    if (args.throughVertexes !== undefined && args.throughVertexes !== null) {
      this.throughVertexes = Thrift.copyList(args.throughVertexes, [null]);
    }
    if (args.vertexfilters !== undefined && args.vertexfilters !== null) {
      this.vertexfilters = Thrift.copyList(args.vertexfilters, [ttypes.ReadFilterSet_Req]);
    }
    if (args.startVertex !== undefined && args.startVertex !== null) {
      this.startVertex = args.startVertex;
    }
    if (args.startIds !== undefined && args.startIds !== null) {
      this.startIds = Thrift.copyList(args.startIds, [null]);
    }
    if (args.steplimit !== undefined && args.steplimit !== null) {
      this.steplimit = args.steplimit;
    }
    if (args.resultlimit !== undefined && args.resultlimit !== null) {
      this.resultlimit = args.resultlimit;
    }
    if (args.graph !== undefined && args.graph !== null) {
      this.graph = args.graph;
    }
  }
};
FreeBroadPath_Req.prototype = {};
FreeBroadPath_Req.prototype.read = function(input) {
  input.readStructBegin();
  while (true) {
    var ret = input.readFieldBegin();
    var ftype = ret.ftype;
    var fid = ret.fid;
    if (ftype == Thrift.Type.STOP) {
      break;
    }
    switch (fid) {
      case 1:
      if (ftype == Thrift.Type.LIST) {
        this.throughEdges = [];
        var _rtmp3317 = input.readListBegin();
        var _size316 = _rtmp3317.size || 0;
        for (var _i318 = 0; _i318 < _size316; ++_i318) {
          var elem319 = null;
          elem319 = input.readString();
          this.throughEdges.push(elem319);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 2:
      if (ftype == Thrift.Type.LIST) {
        this.edgefilters = [];
        var _rtmp3321 = input.readListBegin();
        var _size320 = _rtmp3321.size || 0;
        for (var _i322 = 0; _i322 < _size320; ++_i322) {
          var elem323 = null;
          elem323 = new ttypes.ReadFilterSet_Req();
          elem323.read(input);
          this.edgefilters.push(elem323);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 3:
      if (ftype == Thrift.Type.LIST) {
        this.throughVertexes = [];
        var _rtmp3325 = input.readListBegin();
        var _size324 = _rtmp3325.size || 0;
        for (var _i326 = 0; _i326 < _size324; ++_i326) {
          var elem327 = null;
          elem327 = input.readString();
          this.throughVertexes.push(elem327);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 4:
      if (ftype == Thrift.Type.LIST) {
        this.vertexfilters = [];
        var _rtmp3329 = input.readListBegin();
        var _size328 = _rtmp3329.size || 0;
        for (var _i330 = 0; _i330 < _size328; ++_i330) {
          var elem331 = null;
          elem331 = new ttypes.ReadFilterSet_Req();
          elem331.read(input);
          this.vertexfilters.push(elem331);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 5:
      if (ftype == Thrift.Type.STRING) {
        this.startVertex = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      case 6:
      if (ftype == Thrift.Type.LIST) {
        this.startIds = [];
        var _rtmp3333 = input.readListBegin();
        var _size332 = _rtmp3333.size || 0;
        for (var _i334 = 0; _i334 < _size332; ++_i334) {
          var elem335 = null;
          elem335 = input.readI64();
          this.startIds.push(elem335);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 7:
      if (ftype == Thrift.Type.I32) {
        this.steplimit = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 8:
      if (ftype == Thrift.Type.I32) {
        this.resultlimit = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 9:
      if (ftype == Thrift.Type.STRING) {
        this.graph = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      default:
        input.skip(ftype);
    }
    input.readFieldEnd();
  }
  input.readStructEnd();
  return;
};

FreeBroadPath_Req.prototype.write = function(output) {
  output.writeStructBegin('FreeBroadPath_Req');
  if (this.throughEdges !== null && this.throughEdges !== undefined) {
    output.writeFieldBegin('throughEdges', Thrift.Type.LIST, 1);
    output.writeListBegin(Thrift.Type.STRING, this.throughEdges.length);
    for (var iter336 in this.throughEdges) {
      if (this.throughEdges.hasOwnProperty(iter336)) {
        iter336 = this.throughEdges[iter336];
        output.writeString(iter336);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.edgefilters !== null && this.edgefilters !== undefined) {
    output.writeFieldBegin('edgefilters', Thrift.Type.LIST, 2);
    output.writeListBegin(Thrift.Type.STRUCT, this.edgefilters.length);
    for (var iter337 in this.edgefilters) {
      if (this.edgefilters.hasOwnProperty(iter337)) {
        iter337 = this.edgefilters[iter337];
        iter337.write(output);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.throughVertexes !== null && this.throughVertexes !== undefined) {
    output.writeFieldBegin('throughVertexes', Thrift.Type.LIST, 3);
    output.writeListBegin(Thrift.Type.STRING, this.throughVertexes.length);
    for (var iter338 in this.throughVertexes) {
      if (this.throughVertexes.hasOwnProperty(iter338)) {
        iter338 = this.throughVertexes[iter338];
        output.writeString(iter338);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.vertexfilters !== null && this.vertexfilters !== undefined) {
    output.writeFieldBegin('vertexfilters', Thrift.Type.LIST, 4);
    output.writeListBegin(Thrift.Type.STRUCT, this.vertexfilters.length);
    for (var iter339 in this.vertexfilters) {
      if (this.vertexfilters.hasOwnProperty(iter339)) {
        iter339 = this.vertexfilters[iter339];
        iter339.write(output);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.startVertex !== null && this.startVertex !== undefined) {
    output.writeFieldBegin('startVertex', Thrift.Type.STRING, 5);
    output.writeString(this.startVertex);
    output.writeFieldEnd();
  }
  if (this.startIds !== null && this.startIds !== undefined) {
    output.writeFieldBegin('startIds', Thrift.Type.LIST, 6);
    output.writeListBegin(Thrift.Type.I64, this.startIds.length);
    for (var iter340 in this.startIds) {
      if (this.startIds.hasOwnProperty(iter340)) {
        iter340 = this.startIds[iter340];
        output.writeI64(iter340);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.steplimit !== null && this.steplimit !== undefined) {
    output.writeFieldBegin('steplimit', Thrift.Type.I32, 7);
    output.writeI32(this.steplimit);
    output.writeFieldEnd();
  }
  if (this.resultlimit !== null && this.resultlimit !== undefined) {
    output.writeFieldBegin('resultlimit', Thrift.Type.I32, 8);
    output.writeI32(this.resultlimit);
    output.writeFieldEnd();
  }
  if (this.graph !== null && this.graph !== undefined) {
    output.writeFieldBegin('graph', Thrift.Type.STRING, 9);
    output.writeString(this.graph);
    output.writeFieldEnd();
  }
  output.writeFieldStop();
  output.writeStructEnd();
  return;
};

var VertexCommunicatePath_Req = module.exports.VertexCommunicatePath_Req = function(args) {
  this.throughEdges = null;
  this.edgefilters = null;
  this.throughVertexes = null;
  this.vertexfilters = null;
  this.srcid = null;
  this.dstid = null;
  this.graph = null;
  this.resultlimit = 10000;
  this.calclimit = 10000000;
  this.pathlimit = 100;
  this.stepDown = 0;
  this.stepUp = 100;
  this.shortFlag = 0;
  this.srcVertex = null;
  this.dstVertex = null;
  if (args) {
    if (args.throughEdges !== undefined && args.throughEdges !== null) {
      this.throughEdges = Thrift.copyList(args.throughEdges, [null]);
    }
    if (args.edgefilters !== undefined && args.edgefilters !== null) {
      this.edgefilters = Thrift.copyList(args.edgefilters, [ttypes.ReadFilterSet_Req]);
    }
    if (args.throughVertexes !== undefined && args.throughVertexes !== null) {
      this.throughVertexes = Thrift.copyList(args.throughVertexes, [null]);
    }
    if (args.vertexfilters !== undefined && args.vertexfilters !== null) {
      this.vertexfilters = Thrift.copyList(args.vertexfilters, [ttypes.ReadFilterSet_Req]);
    }
    if (args.srcid !== undefined && args.srcid !== null) {
      this.srcid = args.srcid;
    }
    if (args.dstid !== undefined && args.dstid !== null) {
      this.dstid = args.dstid;
    }
    if (args.graph !== undefined && args.graph !== null) {
      this.graph = args.graph;
    }
    if (args.resultlimit !== undefined && args.resultlimit !== null) {
      this.resultlimit = args.resultlimit;
    }
    if (args.calclimit !== undefined && args.calclimit !== null) {
      this.calclimit = args.calclimit;
    }
    if (args.pathlimit !== undefined && args.pathlimit !== null) {
      this.pathlimit = args.pathlimit;
    }
    if (args.stepDown !== undefined && args.stepDown !== null) {
      this.stepDown = args.stepDown;
    }
    if (args.stepUp !== undefined && args.stepUp !== null) {
      this.stepUp = args.stepUp;
    }
    if (args.shortFlag !== undefined && args.shortFlag !== null) {
      this.shortFlag = args.shortFlag;
    }
    if (args.srcVertex !== undefined && args.srcVertex !== null) {
      this.srcVertex = args.srcVertex;
    }
    if (args.dstVertex !== undefined && args.dstVertex !== null) {
      this.dstVertex = args.dstVertex;
    }
  }
};
VertexCommunicatePath_Req.prototype = {};
VertexCommunicatePath_Req.prototype.read = function(input) {
  input.readStructBegin();
  while (true) {
    var ret = input.readFieldBegin();
    var ftype = ret.ftype;
    var fid = ret.fid;
    if (ftype == Thrift.Type.STOP) {
      break;
    }
    switch (fid) {
      case 1:
      if (ftype == Thrift.Type.LIST) {
        this.throughEdges = [];
        var _rtmp3342 = input.readListBegin();
        var _size341 = _rtmp3342.size || 0;
        for (var _i343 = 0; _i343 < _size341; ++_i343) {
          var elem344 = null;
          elem344 = input.readString();
          this.throughEdges.push(elem344);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 2:
      if (ftype == Thrift.Type.LIST) {
        this.edgefilters = [];
        var _rtmp3346 = input.readListBegin();
        var _size345 = _rtmp3346.size || 0;
        for (var _i347 = 0; _i347 < _size345; ++_i347) {
          var elem348 = null;
          elem348 = new ttypes.ReadFilterSet_Req();
          elem348.read(input);
          this.edgefilters.push(elem348);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 3:
      if (ftype == Thrift.Type.LIST) {
        this.throughVertexes = [];
        var _rtmp3350 = input.readListBegin();
        var _size349 = _rtmp3350.size || 0;
        for (var _i351 = 0; _i351 < _size349; ++_i351) {
          var elem352 = null;
          elem352 = input.readString();
          this.throughVertexes.push(elem352);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 4:
      if (ftype == Thrift.Type.LIST) {
        this.vertexfilters = [];
        var _rtmp3354 = input.readListBegin();
        var _size353 = _rtmp3354.size || 0;
        for (var _i355 = 0; _i355 < _size353; ++_i355) {
          var elem356 = null;
          elem356 = new ttypes.ReadFilterSet_Req();
          elem356.read(input);
          this.vertexfilters.push(elem356);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 5:
      if (ftype == Thrift.Type.I64) {
        this.srcid = input.readI64();
      } else {
        input.skip(ftype);
      }
      break;
      case 6:
      if (ftype == Thrift.Type.I64) {
        this.dstid = input.readI64();
      } else {
        input.skip(ftype);
      }
      break;
      case 7:
      if (ftype == Thrift.Type.STRING) {
        this.graph = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      case 8:
      if (ftype == Thrift.Type.I32) {
        this.resultlimit = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 9:
      if (ftype == Thrift.Type.I32) {
        this.calclimit = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 10:
      if (ftype == Thrift.Type.I32) {
        this.pathlimit = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 11:
      if (ftype == Thrift.Type.I32) {
        this.stepDown = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 12:
      if (ftype == Thrift.Type.I32) {
        this.stepUp = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 13:
      if (ftype == Thrift.Type.I32) {
        this.shortFlag = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 14:
      if (ftype == Thrift.Type.STRING) {
        this.srcVertex = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      case 15:
      if (ftype == Thrift.Type.STRING) {
        this.dstVertex = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      default:
        input.skip(ftype);
    }
    input.readFieldEnd();
  }
  input.readStructEnd();
  return;
};

VertexCommunicatePath_Req.prototype.write = function(output) {
  output.writeStructBegin('VertexCommunicatePath_Req');
  if (this.throughEdges !== null && this.throughEdges !== undefined) {
    output.writeFieldBegin('throughEdges', Thrift.Type.LIST, 1);
    output.writeListBegin(Thrift.Type.STRING, this.throughEdges.length);
    for (var iter357 in this.throughEdges) {
      if (this.throughEdges.hasOwnProperty(iter357)) {
        iter357 = this.throughEdges[iter357];
        output.writeString(iter357);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.edgefilters !== null && this.edgefilters !== undefined) {
    output.writeFieldBegin('edgefilters', Thrift.Type.LIST, 2);
    output.writeListBegin(Thrift.Type.STRUCT, this.edgefilters.length);
    for (var iter358 in this.edgefilters) {
      if (this.edgefilters.hasOwnProperty(iter358)) {
        iter358 = this.edgefilters[iter358];
        iter358.write(output);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.throughVertexes !== null && this.throughVertexes !== undefined) {
    output.writeFieldBegin('throughVertexes', Thrift.Type.LIST, 3);
    output.writeListBegin(Thrift.Type.STRING, this.throughVertexes.length);
    for (var iter359 in this.throughVertexes) {
      if (this.throughVertexes.hasOwnProperty(iter359)) {
        iter359 = this.throughVertexes[iter359];
        output.writeString(iter359);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.vertexfilters !== null && this.vertexfilters !== undefined) {
    output.writeFieldBegin('vertexfilters', Thrift.Type.LIST, 4);
    output.writeListBegin(Thrift.Type.STRUCT, this.vertexfilters.length);
    for (var iter360 in this.vertexfilters) {
      if (this.vertexfilters.hasOwnProperty(iter360)) {
        iter360 = this.vertexfilters[iter360];
        iter360.write(output);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.srcid !== null && this.srcid !== undefined) {
    output.writeFieldBegin('srcid', Thrift.Type.I64, 5);
    output.writeI64(this.srcid);
    output.writeFieldEnd();
  }
  if (this.dstid !== null && this.dstid !== undefined) {
    output.writeFieldBegin('dstid', Thrift.Type.I64, 6);
    output.writeI64(this.dstid);
    output.writeFieldEnd();
  }
  if (this.graph !== null && this.graph !== undefined) {
    output.writeFieldBegin('graph', Thrift.Type.STRING, 7);
    output.writeString(this.graph);
    output.writeFieldEnd();
  }
  if (this.resultlimit !== null && this.resultlimit !== undefined) {
    output.writeFieldBegin('resultlimit', Thrift.Type.I32, 8);
    output.writeI32(this.resultlimit);
    output.writeFieldEnd();
  }
  if (this.calclimit !== null && this.calclimit !== undefined) {
    output.writeFieldBegin('calclimit', Thrift.Type.I32, 9);
    output.writeI32(this.calclimit);
    output.writeFieldEnd();
  }
  if (this.pathlimit !== null && this.pathlimit !== undefined) {
    output.writeFieldBegin('pathlimit', Thrift.Type.I32, 10);
    output.writeI32(this.pathlimit);
    output.writeFieldEnd();
  }
  if (this.stepDown !== null && this.stepDown !== undefined) {
    output.writeFieldBegin('stepDown', Thrift.Type.I32, 11);
    output.writeI32(this.stepDown);
    output.writeFieldEnd();
  }
  if (this.stepUp !== null && this.stepUp !== undefined) {
    output.writeFieldBegin('stepUp', Thrift.Type.I32, 12);
    output.writeI32(this.stepUp);
    output.writeFieldEnd();
  }
  if (this.shortFlag !== null && this.shortFlag !== undefined) {
    output.writeFieldBegin('shortFlag', Thrift.Type.I32, 13);
    output.writeI32(this.shortFlag);
    output.writeFieldEnd();
  }
  if (this.srcVertex !== null && this.srcVertex !== undefined) {
    output.writeFieldBegin('srcVertex', Thrift.Type.STRING, 14);
    output.writeString(this.srcVertex);
    output.writeFieldEnd();
  }
  if (this.dstVertex !== null && this.dstVertex !== undefined) {
    output.writeFieldBegin('dstVertex', Thrift.Type.STRING, 15);
    output.writeString(this.dstVertex);
    output.writeFieldEnd();
  }
  output.writeFieldStop();
  output.writeStructEnd();
  return;
};

var VertexConnectionPath_Req = module.exports.VertexConnectionPath_Req = function(args) {
  this.throughEdges = null;
  this.edgefilters = null;
  this.throughVertexes = null;
  this.vertexfilters = null;
  this.vids = null;
  this.graph = null;
  this.resultlimit = 10000;
  this.calclimit = 10000000;
  this.pathlimit = 100;
  if (args) {
    if (args.throughEdges !== undefined && args.throughEdges !== null) {
      this.throughEdges = Thrift.copyList(args.throughEdges, [null]);
    }
    if (args.edgefilters !== undefined && args.edgefilters !== null) {
      this.edgefilters = Thrift.copyList(args.edgefilters, [ttypes.ReadFilterSet_Req]);
    }
    if (args.throughVertexes !== undefined && args.throughVertexes !== null) {
      this.throughVertexes = Thrift.copyList(args.throughVertexes, [null]);
    }
    if (args.vertexfilters !== undefined && args.vertexfilters !== null) {
      this.vertexfilters = Thrift.copyList(args.vertexfilters, [ttypes.ReadFilterSet_Req]);
    }
    if (args.vids !== undefined && args.vids !== null) {
      this.vids = Thrift.copyList(args.vids, [ttypes.VertexID]);
    }
    if (args.graph !== undefined && args.graph !== null) {
      this.graph = args.graph;
    }
    if (args.resultlimit !== undefined && args.resultlimit !== null) {
      this.resultlimit = args.resultlimit;
    }
    if (args.calclimit !== undefined && args.calclimit !== null) {
      this.calclimit = args.calclimit;
    }
    if (args.pathlimit !== undefined && args.pathlimit !== null) {
      this.pathlimit = args.pathlimit;
    }
  }
};
VertexConnectionPath_Req.prototype = {};
VertexConnectionPath_Req.prototype.read = function(input) {
  input.readStructBegin();
  while (true) {
    var ret = input.readFieldBegin();
    var ftype = ret.ftype;
    var fid = ret.fid;
    if (ftype == Thrift.Type.STOP) {
      break;
    }
    switch (fid) {
      case 1:
      if (ftype == Thrift.Type.LIST) {
        this.throughEdges = [];
        var _rtmp3362 = input.readListBegin();
        var _size361 = _rtmp3362.size || 0;
        for (var _i363 = 0; _i363 < _size361; ++_i363) {
          var elem364 = null;
          elem364 = input.readString();
          this.throughEdges.push(elem364);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 2:
      if (ftype == Thrift.Type.LIST) {
        this.edgefilters = [];
        var _rtmp3366 = input.readListBegin();
        var _size365 = _rtmp3366.size || 0;
        for (var _i367 = 0; _i367 < _size365; ++_i367) {
          var elem368 = null;
          elem368 = new ttypes.ReadFilterSet_Req();
          elem368.read(input);
          this.edgefilters.push(elem368);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 3:
      if (ftype == Thrift.Type.LIST) {
        this.throughVertexes = [];
        var _rtmp3370 = input.readListBegin();
        var _size369 = _rtmp3370.size || 0;
        for (var _i371 = 0; _i371 < _size369; ++_i371) {
          var elem372 = null;
          elem372 = input.readString();
          this.throughVertexes.push(elem372);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 4:
      if (ftype == Thrift.Type.LIST) {
        this.vertexfilters = [];
        var _rtmp3374 = input.readListBegin();
        var _size373 = _rtmp3374.size || 0;
        for (var _i375 = 0; _i375 < _size373; ++_i375) {
          var elem376 = null;
          elem376 = new ttypes.ReadFilterSet_Req();
          elem376.read(input);
          this.vertexfilters.push(elem376);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 5:
      if (ftype == Thrift.Type.LIST) {
        this.vids = [];
        var _rtmp3378 = input.readListBegin();
        var _size377 = _rtmp3378.size || 0;
        for (var _i379 = 0; _i379 < _size377; ++_i379) {
          var elem380 = null;
          elem380 = new ttypes.VertexID();
          elem380.read(input);
          this.vids.push(elem380);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 6:
      if (ftype == Thrift.Type.STRING) {
        this.graph = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      case 7:
      if (ftype == Thrift.Type.I32) {
        this.resultlimit = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 8:
      if (ftype == Thrift.Type.I32) {
        this.calclimit = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 9:
      if (ftype == Thrift.Type.I32) {
        this.pathlimit = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      default:
        input.skip(ftype);
    }
    input.readFieldEnd();
  }
  input.readStructEnd();
  return;
};

VertexConnectionPath_Req.prototype.write = function(output) {
  output.writeStructBegin('VertexConnectionPath_Req');
  if (this.throughEdges !== null && this.throughEdges !== undefined) {
    output.writeFieldBegin('throughEdges', Thrift.Type.LIST, 1);
    output.writeListBegin(Thrift.Type.STRING, this.throughEdges.length);
    for (var iter381 in this.throughEdges) {
      if (this.throughEdges.hasOwnProperty(iter381)) {
        iter381 = this.throughEdges[iter381];
        output.writeString(iter381);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.edgefilters !== null && this.edgefilters !== undefined) {
    output.writeFieldBegin('edgefilters', Thrift.Type.LIST, 2);
    output.writeListBegin(Thrift.Type.STRUCT, this.edgefilters.length);
    for (var iter382 in this.edgefilters) {
      if (this.edgefilters.hasOwnProperty(iter382)) {
        iter382 = this.edgefilters[iter382];
        iter382.write(output);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.throughVertexes !== null && this.throughVertexes !== undefined) {
    output.writeFieldBegin('throughVertexes', Thrift.Type.LIST, 3);
    output.writeListBegin(Thrift.Type.STRING, this.throughVertexes.length);
    for (var iter383 in this.throughVertexes) {
      if (this.throughVertexes.hasOwnProperty(iter383)) {
        iter383 = this.throughVertexes[iter383];
        output.writeString(iter383);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.vertexfilters !== null && this.vertexfilters !== undefined) {
    output.writeFieldBegin('vertexfilters', Thrift.Type.LIST, 4);
    output.writeListBegin(Thrift.Type.STRUCT, this.vertexfilters.length);
    for (var iter384 in this.vertexfilters) {
      if (this.vertexfilters.hasOwnProperty(iter384)) {
        iter384 = this.vertexfilters[iter384];
        iter384.write(output);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.vids !== null && this.vids !== undefined) {
    output.writeFieldBegin('vids', Thrift.Type.LIST, 5);
    output.writeListBegin(Thrift.Type.STRUCT, this.vids.length);
    for (var iter385 in this.vids) {
      if (this.vids.hasOwnProperty(iter385)) {
        iter385 = this.vids[iter385];
        iter385.write(output);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.graph !== null && this.graph !== undefined) {
    output.writeFieldBegin('graph', Thrift.Type.STRING, 6);
    output.writeString(this.graph);
    output.writeFieldEnd();
  }
  if (this.resultlimit !== null && this.resultlimit !== undefined) {
    output.writeFieldBegin('resultlimit', Thrift.Type.I32, 7);
    output.writeI32(this.resultlimit);
    output.writeFieldEnd();
  }
  if (this.calclimit !== null && this.calclimit !== undefined) {
    output.writeFieldBegin('calclimit', Thrift.Type.I32, 8);
    output.writeI32(this.calclimit);
    output.writeFieldEnd();
  }
  if (this.pathlimit !== null && this.pathlimit !== undefined) {
    output.writeFieldBegin('pathlimit', Thrift.Type.I32, 9);
    output.writeI32(this.pathlimit);
    output.writeFieldEnd();
  }
  output.writeFieldStop();
  output.writeStructEnd();
  return;
};

var GraphEntityResult_Res = module.exports.GraphEntityResult_Res = function(args) {
  this.vids = null;
  this.eids = null;
  if (args) {
    if (args.vids !== undefined && args.vids !== null) {
      this.vids = Thrift.copyList(args.vids, [ttypes.VertexPk]);
    }
    if (args.eids !== undefined && args.eids !== null) {
      this.eids = Thrift.copyList(args.eids, [ttypes.EdgePk]);
    }
  }
};
GraphEntityResult_Res.prototype = {};
GraphEntityResult_Res.prototype.read = function(input) {
  input.readStructBegin();
  while (true) {
    var ret = input.readFieldBegin();
    var ftype = ret.ftype;
    var fid = ret.fid;
    if (ftype == Thrift.Type.STOP) {
      break;
    }
    switch (fid) {
      case 1:
      if (ftype == Thrift.Type.LIST) {
        this.vids = [];
        var _rtmp3387 = input.readListBegin();
        var _size386 = _rtmp3387.size || 0;
        for (var _i388 = 0; _i388 < _size386; ++_i388) {
          var elem389 = null;
          elem389 = new ttypes.VertexPk();
          elem389.read(input);
          this.vids.push(elem389);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 2:
      if (ftype == Thrift.Type.LIST) {
        this.eids = [];
        var _rtmp3391 = input.readListBegin();
        var _size390 = _rtmp3391.size || 0;
        for (var _i392 = 0; _i392 < _size390; ++_i392) {
          var elem393 = null;
          elem393 = new ttypes.EdgePk();
          elem393.read(input);
          this.eids.push(elem393);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      default:
        input.skip(ftype);
    }
    input.readFieldEnd();
  }
  input.readStructEnd();
  return;
};

GraphEntityResult_Res.prototype.write = function(output) {
  output.writeStructBegin('GraphEntityResult_Res');
  if (this.vids !== null && this.vids !== undefined) {
    output.writeFieldBegin('vids', Thrift.Type.LIST, 1);
    output.writeListBegin(Thrift.Type.STRUCT, this.vids.length);
    for (var iter394 in this.vids) {
      if (this.vids.hasOwnProperty(iter394)) {
        iter394 = this.vids[iter394];
        iter394.write(output);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.eids !== null && this.eids !== undefined) {
    output.writeFieldBegin('eids', Thrift.Type.LIST, 2);
    output.writeListBegin(Thrift.Type.STRUCT, this.eids.length);
    for (var iter395 in this.eids) {
      if (this.eids.hasOwnProperty(iter395)) {
        iter395 = this.eids[iter395];
        iter395.write(output);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  output.writeFieldStop();
  output.writeStructEnd();
  return;
};

var PathResultBean_Res = module.exports.PathResultBean_Res = function(args) {
  this.edge_name = null;
  this.dst_vname = null;
  this.eid = null;
  this.dst_vid = null;
  this.dst_vpk = null;
  if (args) {
    if (args.edge_name !== undefined && args.edge_name !== null) {
      this.edge_name = args.edge_name;
    }
    if (args.dst_vname !== undefined && args.dst_vname !== null) {
      this.dst_vname = args.dst_vname;
    }
    if (args.eid !== undefined && args.eid !== null) {
      this.eid = args.eid;
    }
    if (args.dst_vid !== undefined && args.dst_vid !== null) {
      this.dst_vid = args.dst_vid;
    }
    if (args.dst_vpk !== undefined && args.dst_vpk !== null) {
      this.dst_vpk = args.dst_vpk;
    }
  }
};
PathResultBean_Res.prototype = {};
PathResultBean_Res.prototype.read = function(input) {
  input.readStructBegin();
  while (true) {
    var ret = input.readFieldBegin();
    var ftype = ret.ftype;
    var fid = ret.fid;
    if (ftype == Thrift.Type.STOP) {
      break;
    }
    switch (fid) {
      case 1:
      if (ftype == Thrift.Type.STRING) {
        this.edge_name = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      case 2:
      if (ftype == Thrift.Type.STRING) {
        this.dst_vname = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      case 3:
      if (ftype == Thrift.Type.I64) {
        this.eid = input.readI64();
      } else {
        input.skip(ftype);
      }
      break;
      case 4:
      if (ftype == Thrift.Type.I64) {
        this.dst_vid = input.readI64();
      } else {
        input.skip(ftype);
      }
      break;
      case 5:
      if (ftype == Thrift.Type.STRING) {
        this.dst_vpk = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      default:
        input.skip(ftype);
    }
    input.readFieldEnd();
  }
  input.readStructEnd();
  return;
};

PathResultBean_Res.prototype.write = function(output) {
  output.writeStructBegin('PathResultBean_Res');
  if (this.edge_name !== null && this.edge_name !== undefined) {
    output.writeFieldBegin('edge_name', Thrift.Type.STRING, 1);
    output.writeString(this.edge_name);
    output.writeFieldEnd();
  }
  if (this.dst_vname !== null && this.dst_vname !== undefined) {
    output.writeFieldBegin('dst_vname', Thrift.Type.STRING, 2);
    output.writeString(this.dst_vname);
    output.writeFieldEnd();
  }
  if (this.eid !== null && this.eid !== undefined) {
    output.writeFieldBegin('eid', Thrift.Type.I64, 3);
    output.writeI64(this.eid);
    output.writeFieldEnd();
  }
  if (this.dst_vid !== null && this.dst_vid !== undefined) {
    output.writeFieldBegin('dst_vid', Thrift.Type.I64, 4);
    output.writeI64(this.dst_vid);
    output.writeFieldEnd();
  }
  if (this.dst_vpk !== null && this.dst_vpk !== undefined) {
    output.writeFieldBegin('dst_vpk', Thrift.Type.STRING, 5);
    output.writeString(this.dst_vpk);
    output.writeFieldEnd();
  }
  output.writeFieldStop();
  output.writeStructEnd();
  return;
};

var PathCalcJobQuery_Agg_Field_Req = module.exports.PathCalcJobQuery_Agg_Field_Req = function(args) {
  this.type = null;
  this.name = null;
  this.interval_vals = null;
  if (args) {
    if (args.type !== undefined && args.type !== null) {
      this.type = args.type;
    }
    if (args.name !== undefined && args.name !== null) {
      this.name = args.name;
    }
    if (args.interval_vals !== undefined && args.interval_vals !== null) {
      this.interval_vals = Thrift.copyList(args.interval_vals, [ttypes.IntervalPair]);
    }
  }
};
PathCalcJobQuery_Agg_Field_Req.prototype = {};
PathCalcJobQuery_Agg_Field_Req.prototype.read = function(input) {
  input.readStructBegin();
  while (true) {
    var ret = input.readFieldBegin();
    var ftype = ret.ftype;
    var fid = ret.fid;
    if (ftype == Thrift.Type.STOP) {
      break;
    }
    switch (fid) {
      case 1:
      if (ftype == Thrift.Type.I32) {
        this.type = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 2:
      if (ftype == Thrift.Type.STRING) {
        this.name = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      case 3:
      if (ftype == Thrift.Type.LIST) {
        this.interval_vals = [];
        var _rtmp3397 = input.readListBegin();
        var _size396 = _rtmp3397.size || 0;
        for (var _i398 = 0; _i398 < _size396; ++_i398) {
          var elem399 = null;
          elem399 = new ttypes.IntervalPair();
          elem399.read(input);
          this.interval_vals.push(elem399);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      default:
        input.skip(ftype);
    }
    input.readFieldEnd();
  }
  input.readStructEnd();
  return;
};

PathCalcJobQuery_Agg_Field_Req.prototype.write = function(output) {
  output.writeStructBegin('PathCalcJobQuery_Agg_Field_Req');
  if (this.type !== null && this.type !== undefined) {
    output.writeFieldBegin('type', Thrift.Type.I32, 1);
    output.writeI32(this.type);
    output.writeFieldEnd();
  }
  if (this.name !== null && this.name !== undefined) {
    output.writeFieldBegin('name', Thrift.Type.STRING, 2);
    output.writeString(this.name);
    output.writeFieldEnd();
  }
  if (this.interval_vals !== null && this.interval_vals !== undefined) {
    output.writeFieldBegin('interval_vals', Thrift.Type.LIST, 3);
    output.writeListBegin(Thrift.Type.STRUCT, this.interval_vals.length);
    for (var iter400 in this.interval_vals) {
      if (this.interval_vals.hasOwnProperty(iter400)) {
        iter400 = this.interval_vals[iter400];
        iter400.write(output);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  output.writeFieldStop();
  output.writeStructEnd();
  return;
};

var PathCalcJobQuery_Agg_Req = module.exports.PathCalcJobQuery_Agg_Req = function(args) {
  this.type = null;
  this.edgeAttrGroup = null;
  this.vertexAttrGroup = null;
  if (args) {
    if (args.type !== undefined && args.type !== null) {
      this.type = args.type;
    }
    if (args.edgeAttrGroup !== undefined && args.edgeAttrGroup !== null) {
      this.edgeAttrGroup = Thrift.copyMap(args.edgeAttrGroup, [Thrift.copyList, ttypes.PathCalcJobQuery_Agg_Field_Req]);
    }
    if (args.vertexAttrGroup !== undefined && args.vertexAttrGroup !== null) {
      this.vertexAttrGroup = Thrift.copyMap(args.vertexAttrGroup, [Thrift.copyList, ttypes.PathCalcJobQuery_Agg_Field_Req]);
    }
  }
};
PathCalcJobQuery_Agg_Req.prototype = {};
PathCalcJobQuery_Agg_Req.prototype.read = function(input) {
  input.readStructBegin();
  while (true) {
    var ret = input.readFieldBegin();
    var ftype = ret.ftype;
    var fid = ret.fid;
    if (ftype == Thrift.Type.STOP) {
      break;
    }
    switch (fid) {
      case 1:
      if (ftype == Thrift.Type.I32) {
        this.type = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 2:
      if (ftype == Thrift.Type.MAP) {
        this.edgeAttrGroup = {};
        var _rtmp3402 = input.readMapBegin();
        var _size401 = _rtmp3402.size || 0;
        for (var _i403 = 0; _i403 < _size401; ++_i403) {
          var key404 = null;
          var val405 = null;
          key404 = input.readI32();
          val405 = [];
          var _rtmp3407 = input.readListBegin();
          var _size406 = _rtmp3407.size || 0;
          for (var _i408 = 0; _i408 < _size406; ++_i408) {
            var elem409 = null;
            elem409 = new ttypes.PathCalcJobQuery_Agg_Field_Req();
            elem409.read(input);
            val405.push(elem409);
          }
          input.readListEnd();
          this.edgeAttrGroup[key404] = val405;
        }
        input.readMapEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 3:
      if (ftype == Thrift.Type.MAP) {
        this.vertexAttrGroup = {};
        var _rtmp3411 = input.readMapBegin();
        var _size410 = _rtmp3411.size || 0;
        for (var _i412 = 0; _i412 < _size410; ++_i412) {
          var key413 = null;
          var val414 = null;
          key413 = input.readI32();
          val414 = [];
          var _rtmp3416 = input.readListBegin();
          var _size415 = _rtmp3416.size || 0;
          for (var _i417 = 0; _i417 < _size415; ++_i417) {
            var elem418 = null;
            elem418 = new ttypes.PathCalcJobQuery_Agg_Field_Req();
            elem418.read(input);
            val414.push(elem418);
          }
          input.readListEnd();
          this.vertexAttrGroup[key413] = val414;
        }
        input.readMapEnd();
      } else {
        input.skip(ftype);
      }
      break;
      default:
        input.skip(ftype);
    }
    input.readFieldEnd();
  }
  input.readStructEnd();
  return;
};

PathCalcJobQuery_Agg_Req.prototype.write = function(output) {
  output.writeStructBegin('PathCalcJobQuery_Agg_Req');
  if (this.type !== null && this.type !== undefined) {
    output.writeFieldBegin('type', Thrift.Type.I32, 1);
    output.writeI32(this.type);
    output.writeFieldEnd();
  }
  if (this.edgeAttrGroup !== null && this.edgeAttrGroup !== undefined) {
    output.writeFieldBegin('edgeAttrGroup', Thrift.Type.MAP, 2);
    output.writeMapBegin(Thrift.Type.I32, Thrift.Type.LIST, Thrift.objectLength(this.edgeAttrGroup));
    for (var kiter419 in this.edgeAttrGroup) {
      if (this.edgeAttrGroup.hasOwnProperty(kiter419)) {
        var viter420 = this.edgeAttrGroup[kiter419];
        output.writeI32(kiter419);
        output.writeListBegin(Thrift.Type.STRUCT, viter420.length);
        for (var iter421 in viter420) {
          if (viter420.hasOwnProperty(iter421)) {
            iter421 = viter420[iter421];
            iter421.write(output);
          }
        }
        output.writeListEnd();
      }
    }
    output.writeMapEnd();
    output.writeFieldEnd();
  }
  if (this.vertexAttrGroup !== null && this.vertexAttrGroup !== undefined) {
    output.writeFieldBegin('vertexAttrGroup', Thrift.Type.MAP, 3);
    output.writeMapBegin(Thrift.Type.I32, Thrift.Type.LIST, Thrift.objectLength(this.vertexAttrGroup));
    for (var kiter422 in this.vertexAttrGroup) {
      if (this.vertexAttrGroup.hasOwnProperty(kiter422)) {
        var viter423 = this.vertexAttrGroup[kiter422];
        output.writeI32(kiter422);
        output.writeListBegin(Thrift.Type.STRUCT, viter423.length);
        for (var iter424 in viter423) {
          if (viter423.hasOwnProperty(iter424)) {
            iter424 = viter423[iter424];
            iter424.write(output);
          }
        }
        output.writeListEnd();
      }
    }
    output.writeMapEnd();
    output.writeFieldEnd();
  }
  output.writeFieldStop();
  output.writeStructEnd();
  return;
};

var PathCalcJobQuery_Path_Req = module.exports.PathCalcJobQuery_Path_Req = function(args) {
  this.startIds = null;
  this.startVertexFilter = null;
  this.jobid = null;
  this.start_type = null;
  this.throughEdges = null;
  this.filterVector = null;
  this.start_vertex = null;
  this.agginfo = null;
  if (args) {
    if (args.startIds !== undefined && args.startIds !== null) {
      this.startIds = Thrift.copyList(args.startIds, [null]);
    }
    if (args.startVertexFilter !== undefined && args.startVertexFilter !== null) {
      this.startVertexFilter = new ttypes.ReadFilterSet_Req(args.startVertexFilter);
    }
    if (args.jobid !== undefined && args.jobid !== null) {
      this.jobid = args.jobid;
    }
    if (args.start_type !== undefined && args.start_type !== null) {
      this.start_type = args.start_type;
    }
    if (args.throughEdges !== undefined && args.throughEdges !== null) {
      this.throughEdges = Thrift.copyList(args.throughEdges, [null]);
    }
    if (args.filterVector !== undefined && args.filterVector !== null) {
      this.filterVector = Thrift.copyList(args.filterVector, [ttypes.ReadFilterSet_Req]);
    }
    if (args.start_vertex !== undefined && args.start_vertex !== null) {
      this.start_vertex = args.start_vertex;
    }
    if (args.agginfo !== undefined && args.agginfo !== null) {
      this.agginfo = new ttypes.PathCalcJobQuery_Agg_Req(args.agginfo);
    }
  }
};
PathCalcJobQuery_Path_Req.prototype = {};
PathCalcJobQuery_Path_Req.prototype.read = function(input) {
  input.readStructBegin();
  while (true) {
    var ret = input.readFieldBegin();
    var ftype = ret.ftype;
    var fid = ret.fid;
    if (ftype == Thrift.Type.STOP) {
      break;
    }
    switch (fid) {
      case 1:
      if (ftype == Thrift.Type.LIST) {
        this.startIds = [];
        var _rtmp3426 = input.readListBegin();
        var _size425 = _rtmp3426.size || 0;
        for (var _i427 = 0; _i427 < _size425; ++_i427) {
          var elem428 = null;
          elem428 = input.readI64();
          this.startIds.push(elem428);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 2:
      if (ftype == Thrift.Type.STRUCT) {
        this.startVertexFilter = new ttypes.ReadFilterSet_Req();
        this.startVertexFilter.read(input);
      } else {
        input.skip(ftype);
      }
      break;
      case 3:
      if (ftype == Thrift.Type.I64) {
        this.jobid = input.readI64();
      } else {
        input.skip(ftype);
      }
      break;
      case 4:
      if (ftype == Thrift.Type.I32) {
        this.start_type = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 5:
      if (ftype == Thrift.Type.LIST) {
        this.throughEdges = [];
        var _rtmp3430 = input.readListBegin();
        var _size429 = _rtmp3430.size || 0;
        for (var _i431 = 0; _i431 < _size429; ++_i431) {
          var elem432 = null;
          elem432 = input.readString();
          this.throughEdges.push(elem432);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 6:
      if (ftype == Thrift.Type.LIST) {
        this.filterVector = [];
        var _rtmp3434 = input.readListBegin();
        var _size433 = _rtmp3434.size || 0;
        for (var _i435 = 0; _i435 < _size433; ++_i435) {
          var elem436 = null;
          elem436 = new ttypes.ReadFilterSet_Req();
          elem436.read(input);
          this.filterVector.push(elem436);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 7:
      if (ftype == Thrift.Type.STRING) {
        this.start_vertex = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      case 8:
      if (ftype == Thrift.Type.STRUCT) {
        this.agginfo = new ttypes.PathCalcJobQuery_Agg_Req();
        this.agginfo.read(input);
      } else {
        input.skip(ftype);
      }
      break;
      default:
        input.skip(ftype);
    }
    input.readFieldEnd();
  }
  input.readStructEnd();
  return;
};

PathCalcJobQuery_Path_Req.prototype.write = function(output) {
  output.writeStructBegin('PathCalcJobQuery_Path_Req');
  if (this.startIds !== null && this.startIds !== undefined) {
    output.writeFieldBegin('startIds', Thrift.Type.LIST, 1);
    output.writeListBegin(Thrift.Type.I64, this.startIds.length);
    for (var iter437 in this.startIds) {
      if (this.startIds.hasOwnProperty(iter437)) {
        iter437 = this.startIds[iter437];
        output.writeI64(iter437);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.startVertexFilter !== null && this.startVertexFilter !== undefined) {
    output.writeFieldBegin('startVertexFilter', Thrift.Type.STRUCT, 2);
    this.startVertexFilter.write(output);
    output.writeFieldEnd();
  }
  if (this.jobid !== null && this.jobid !== undefined) {
    output.writeFieldBegin('jobid', Thrift.Type.I64, 3);
    output.writeI64(this.jobid);
    output.writeFieldEnd();
  }
  if (this.start_type !== null && this.start_type !== undefined) {
    output.writeFieldBegin('start_type', Thrift.Type.I32, 4);
    output.writeI32(this.start_type);
    output.writeFieldEnd();
  }
  if (this.throughEdges !== null && this.throughEdges !== undefined) {
    output.writeFieldBegin('throughEdges', Thrift.Type.LIST, 5);
    output.writeListBegin(Thrift.Type.STRING, this.throughEdges.length);
    for (var iter438 in this.throughEdges) {
      if (this.throughEdges.hasOwnProperty(iter438)) {
        iter438 = this.throughEdges[iter438];
        output.writeString(iter438);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.filterVector !== null && this.filterVector !== undefined) {
    output.writeFieldBegin('filterVector', Thrift.Type.LIST, 6);
    output.writeListBegin(Thrift.Type.STRUCT, this.filterVector.length);
    for (var iter439 in this.filterVector) {
      if (this.filterVector.hasOwnProperty(iter439)) {
        iter439 = this.filterVector[iter439];
        iter439.write(output);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.start_vertex !== null && this.start_vertex !== undefined) {
    output.writeFieldBegin('start_vertex', Thrift.Type.STRING, 7);
    output.writeString(this.start_vertex);
    output.writeFieldEnd();
  }
  if (this.agginfo !== null && this.agginfo !== undefined) {
    output.writeFieldBegin('agginfo', Thrift.Type.STRUCT, 8);
    this.agginfo.write(output);
    output.writeFieldEnd();
  }
  output.writeFieldStop();
  output.writeStructEnd();
  return;
};

var PathCalcJobQuery_Calc_Req = module.exports.PathCalcJobQuery_Calc_Req = function(args) {
  this.type = null;
  this.type_step = null;
  this.type_edge = null;
  this.type_pathidx = null;
  this.type_attrname = null;
  this.list_type = null;
  this.multi_type = null;
  this.edgeAttrGroup = null;
  this.vertexAttrGroup = null;
  if (args) {
    if (args.type !== undefined && args.type !== null) {
      this.type = args.type;
    }
    if (args.type_step !== undefined && args.type_step !== null) {
      this.type_step = args.type_step;
    }
    if (args.type_edge !== undefined && args.type_edge !== null) {
      this.type_edge = args.type_edge;
    }
    if (args.type_pathidx !== undefined && args.type_pathidx !== null) {
      this.type_pathidx = args.type_pathidx;
    }
    if (args.type_attrname !== undefined && args.type_attrname !== null) {
      this.type_attrname = args.type_attrname;
    }
    if (args.list_type !== undefined && args.list_type !== null) {
      this.list_type = args.list_type;
    }
    if (args.multi_type !== undefined && args.multi_type !== null) {
      this.multi_type = args.multi_type;
    }
    if (args.edgeAttrGroup !== undefined && args.edgeAttrGroup !== null) {
      this.edgeAttrGroup = Thrift.copyMap(args.edgeAttrGroup, [Thrift.copyList, null]);
    }
    if (args.vertexAttrGroup !== undefined && args.vertexAttrGroup !== null) {
      this.vertexAttrGroup = Thrift.copyMap(args.vertexAttrGroup, [Thrift.copyList, null]);
    }
  }
};
PathCalcJobQuery_Calc_Req.prototype = {};
PathCalcJobQuery_Calc_Req.prototype.read = function(input) {
  input.readStructBegin();
  while (true) {
    var ret = input.readFieldBegin();
    var ftype = ret.ftype;
    var fid = ret.fid;
    if (ftype == Thrift.Type.STOP) {
      break;
    }
    switch (fid) {
      case 1:
      if (ftype == Thrift.Type.I32) {
        this.type = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 2:
      if (ftype == Thrift.Type.I32) {
        this.type_step = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 3:
      if (ftype == Thrift.Type.I32) {
        this.type_edge = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 4:
      if (ftype == Thrift.Type.I32) {
        this.type_pathidx = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 5:
      if (ftype == Thrift.Type.STRING) {
        this.type_attrname = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      case 6:
      if (ftype == Thrift.Type.I32) {
        this.list_type = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 7:
      if (ftype == Thrift.Type.I32) {
        this.multi_type = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 8:
      if (ftype == Thrift.Type.MAP) {
        this.edgeAttrGroup = {};
        var _rtmp3441 = input.readMapBegin();
        var _size440 = _rtmp3441.size || 0;
        for (var _i442 = 0; _i442 < _size440; ++_i442) {
          var key443 = null;
          var val444 = null;
          key443 = input.readI32();
          val444 = [];
          var _rtmp3446 = input.readListBegin();
          var _size445 = _rtmp3446.size || 0;
          for (var _i447 = 0; _i447 < _size445; ++_i447) {
            var elem448 = null;
            elem448 = input.readString();
            val444.push(elem448);
          }
          input.readListEnd();
          this.edgeAttrGroup[key443] = val444;
        }
        input.readMapEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 9:
      if (ftype == Thrift.Type.MAP) {
        this.vertexAttrGroup = {};
        var _rtmp3450 = input.readMapBegin();
        var _size449 = _rtmp3450.size || 0;
        for (var _i451 = 0; _i451 < _size449; ++_i451) {
          var key452 = null;
          var val453 = null;
          key452 = input.readI32();
          val453 = [];
          var _rtmp3455 = input.readListBegin();
          var _size454 = _rtmp3455.size || 0;
          for (var _i456 = 0; _i456 < _size454; ++_i456) {
            var elem457 = null;
            elem457 = input.readString();
            val453.push(elem457);
          }
          input.readListEnd();
          this.vertexAttrGroup[key452] = val453;
        }
        input.readMapEnd();
      } else {
        input.skip(ftype);
      }
      break;
      default:
        input.skip(ftype);
    }
    input.readFieldEnd();
  }
  input.readStructEnd();
  return;
};

PathCalcJobQuery_Calc_Req.prototype.write = function(output) {
  output.writeStructBegin('PathCalcJobQuery_Calc_Req');
  if (this.type !== null && this.type !== undefined) {
    output.writeFieldBegin('type', Thrift.Type.I32, 1);
    output.writeI32(this.type);
    output.writeFieldEnd();
  }
  if (this.type_step !== null && this.type_step !== undefined) {
    output.writeFieldBegin('type_step', Thrift.Type.I32, 2);
    output.writeI32(this.type_step);
    output.writeFieldEnd();
  }
  if (this.type_edge !== null && this.type_edge !== undefined) {
    output.writeFieldBegin('type_edge', Thrift.Type.I32, 3);
    output.writeI32(this.type_edge);
    output.writeFieldEnd();
  }
  if (this.type_pathidx !== null && this.type_pathidx !== undefined) {
    output.writeFieldBegin('type_pathidx', Thrift.Type.I32, 4);
    output.writeI32(this.type_pathidx);
    output.writeFieldEnd();
  }
  if (this.type_attrname !== null && this.type_attrname !== undefined) {
    output.writeFieldBegin('type_attrname', Thrift.Type.STRING, 5);
    output.writeString(this.type_attrname);
    output.writeFieldEnd();
  }
  if (this.list_type !== null && this.list_type !== undefined) {
    output.writeFieldBegin('list_type', Thrift.Type.I32, 6);
    output.writeI32(this.list_type);
    output.writeFieldEnd();
  }
  if (this.multi_type !== null && this.multi_type !== undefined) {
    output.writeFieldBegin('multi_type', Thrift.Type.I32, 7);
    output.writeI32(this.multi_type);
    output.writeFieldEnd();
  }
  if (this.edgeAttrGroup !== null && this.edgeAttrGroup !== undefined) {
    output.writeFieldBegin('edgeAttrGroup', Thrift.Type.MAP, 8);
    output.writeMapBegin(Thrift.Type.I32, Thrift.Type.LIST, Thrift.objectLength(this.edgeAttrGroup));
    for (var kiter458 in this.edgeAttrGroup) {
      if (this.edgeAttrGroup.hasOwnProperty(kiter458)) {
        var viter459 = this.edgeAttrGroup[kiter458];
        output.writeI32(kiter458);
        output.writeListBegin(Thrift.Type.STRING, viter459.length);
        for (var iter460 in viter459) {
          if (viter459.hasOwnProperty(iter460)) {
            iter460 = viter459[iter460];
            output.writeString(iter460);
          }
        }
        output.writeListEnd();
      }
    }
    output.writeMapEnd();
    output.writeFieldEnd();
  }
  if (this.vertexAttrGroup !== null && this.vertexAttrGroup !== undefined) {
    output.writeFieldBegin('vertexAttrGroup', Thrift.Type.MAP, 9);
    output.writeMapBegin(Thrift.Type.I32, Thrift.Type.LIST, Thrift.objectLength(this.vertexAttrGroup));
    for (var kiter461 in this.vertexAttrGroup) {
      if (this.vertexAttrGroup.hasOwnProperty(kiter461)) {
        var viter462 = this.vertexAttrGroup[kiter461];
        output.writeI32(kiter461);
        output.writeListBegin(Thrift.Type.STRING, viter462.length);
        for (var iter463 in viter462) {
          if (viter462.hasOwnProperty(iter463)) {
            iter463 = viter462[iter463];
            output.writeString(iter463);
          }
        }
        output.writeListEnd();
      }
    }
    output.writeMapEnd();
    output.writeFieldEnd();
  }
  output.writeFieldStop();
  output.writeStructEnd();
  return;
};

var PathCalcJobQuery_Req = module.exports.PathCalcJobQuery_Req = function(args) {
  this.graph = null;
  this.pathinfos = null;
  this.calcinfo = null;
  this.agginfo = null;
  this.jobid = null;
  this.threadCnt = 1;
  this.start_vertex = null;
  if (args) {
    if (args.graph !== undefined && args.graph !== null) {
      this.graph = args.graph;
    }
    if (args.pathinfos !== undefined && args.pathinfos !== null) {
      this.pathinfos = Thrift.copyList(args.pathinfos, [ttypes.PathCalcJobQuery_Path_Req]);
    }
    if (args.calcinfo !== undefined && args.calcinfo !== null) {
      this.calcinfo = new ttypes.PathCalcJobQuery_Calc_Req(args.calcinfo);
    }
    if (args.agginfo !== undefined && args.agginfo !== null) {
      this.agginfo = new ttypes.PathCalcJobQuery_Agg_Req(args.agginfo);
    }
    if (args.jobid !== undefined && args.jobid !== null) {
      this.jobid = args.jobid;
    }
    if (args.threadCnt !== undefined && args.threadCnt !== null) {
      this.threadCnt = args.threadCnt;
    }
    if (args.start_vertex !== undefined && args.start_vertex !== null) {
      this.start_vertex = args.start_vertex;
    }
  }
};
PathCalcJobQuery_Req.prototype = {};
PathCalcJobQuery_Req.prototype.read = function(input) {
  input.readStructBegin();
  while (true) {
    var ret = input.readFieldBegin();
    var ftype = ret.ftype;
    var fid = ret.fid;
    if (ftype == Thrift.Type.STOP) {
      break;
    }
    switch (fid) {
      case 1:
      if (ftype == Thrift.Type.STRING) {
        this.graph = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      case 2:
      if (ftype == Thrift.Type.LIST) {
        this.pathinfos = [];
        var _rtmp3465 = input.readListBegin();
        var _size464 = _rtmp3465.size || 0;
        for (var _i466 = 0; _i466 < _size464; ++_i466) {
          var elem467 = null;
          elem467 = new ttypes.PathCalcJobQuery_Path_Req();
          elem467.read(input);
          this.pathinfos.push(elem467);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 3:
      if (ftype == Thrift.Type.STRUCT) {
        this.calcinfo = new ttypes.PathCalcJobQuery_Calc_Req();
        this.calcinfo.read(input);
      } else {
        input.skip(ftype);
      }
      break;
      case 4:
      if (ftype == Thrift.Type.STRUCT) {
        this.agginfo = new ttypes.PathCalcJobQuery_Agg_Req();
        this.agginfo.read(input);
      } else {
        input.skip(ftype);
      }
      break;
      case 5:
      if (ftype == Thrift.Type.I64) {
        this.jobid = input.readI64();
      } else {
        input.skip(ftype);
      }
      break;
      case 6:
      if (ftype == Thrift.Type.I32) {
        this.threadCnt = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 7:
      if (ftype == Thrift.Type.STRING) {
        this.start_vertex = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      default:
        input.skip(ftype);
    }
    input.readFieldEnd();
  }
  input.readStructEnd();
  return;
};

PathCalcJobQuery_Req.prototype.write = function(output) {
  output.writeStructBegin('PathCalcJobQuery_Req');
  if (this.graph !== null && this.graph !== undefined) {
    output.writeFieldBegin('graph', Thrift.Type.STRING, 1);
    output.writeString(this.graph);
    output.writeFieldEnd();
  }
  if (this.pathinfos !== null && this.pathinfos !== undefined) {
    output.writeFieldBegin('pathinfos', Thrift.Type.LIST, 2);
    output.writeListBegin(Thrift.Type.STRUCT, this.pathinfos.length);
    for (var iter468 in this.pathinfos) {
      if (this.pathinfos.hasOwnProperty(iter468)) {
        iter468 = this.pathinfos[iter468];
        iter468.write(output);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.calcinfo !== null && this.calcinfo !== undefined) {
    output.writeFieldBegin('calcinfo', Thrift.Type.STRUCT, 3);
    this.calcinfo.write(output);
    output.writeFieldEnd();
  }
  if (this.agginfo !== null && this.agginfo !== undefined) {
    output.writeFieldBegin('agginfo', Thrift.Type.STRUCT, 4);
    this.agginfo.write(output);
    output.writeFieldEnd();
  }
  if (this.jobid !== null && this.jobid !== undefined) {
    output.writeFieldBegin('jobid', Thrift.Type.I64, 5);
    output.writeI64(this.jobid);
    output.writeFieldEnd();
  }
  if (this.threadCnt !== null && this.threadCnt !== undefined) {
    output.writeFieldBegin('threadCnt', Thrift.Type.I32, 6);
    output.writeI32(this.threadCnt);
    output.writeFieldEnd();
  }
  if (this.start_vertex !== null && this.start_vertex !== undefined) {
    output.writeFieldBegin('start_vertex', Thrift.Type.STRING, 7);
    output.writeString(this.start_vertex);
    output.writeFieldEnd();
  }
  output.writeFieldStop();
  output.writeStructEnd();
  return;
};

var MixDataStruct_Res = module.exports.MixDataStruct_Res = function(args) {
  this.dataType = null;
  this.str = null;
  this.num = null;
  this.dbl = null;
  if (args) {
    if (args.dataType !== undefined && args.dataType !== null) {
      this.dataType = args.dataType;
    }
    if (args.str !== undefined && args.str !== null) {
      this.str = args.str;
    }
    if (args.num !== undefined && args.num !== null) {
      this.num = args.num;
    }
    if (args.dbl !== undefined && args.dbl !== null) {
      this.dbl = args.dbl;
    }
  }
};
MixDataStruct_Res.prototype = {};
MixDataStruct_Res.prototype.read = function(input) {
  input.readStructBegin();
  while (true) {
    var ret = input.readFieldBegin();
    var ftype = ret.ftype;
    var fid = ret.fid;
    if (ftype == Thrift.Type.STOP) {
      break;
    }
    switch (fid) {
      case 1:
      if (ftype == Thrift.Type.I32) {
        this.dataType = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 2:
      if (ftype == Thrift.Type.STRING) {
        this.str = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      case 3:
      if (ftype == Thrift.Type.I64) {
        this.num = input.readI64();
      } else {
        input.skip(ftype);
      }
      break;
      case 4:
      if (ftype == Thrift.Type.DOUBLE) {
        this.dbl = input.readDouble();
      } else {
        input.skip(ftype);
      }
      break;
      default:
        input.skip(ftype);
    }
    input.readFieldEnd();
  }
  input.readStructEnd();
  return;
};

MixDataStruct_Res.prototype.write = function(output) {
  output.writeStructBegin('MixDataStruct_Res');
  if (this.dataType !== null && this.dataType !== undefined) {
    output.writeFieldBegin('dataType', Thrift.Type.I32, 1);
    output.writeI32(this.dataType);
    output.writeFieldEnd();
  }
  if (this.str !== null && this.str !== undefined) {
    output.writeFieldBegin('str', Thrift.Type.STRING, 2);
    output.writeString(this.str);
    output.writeFieldEnd();
  }
  if (this.num !== null && this.num !== undefined) {
    output.writeFieldBegin('num', Thrift.Type.I64, 3);
    output.writeI64(this.num);
    output.writeFieldEnd();
  }
  if (this.dbl !== null && this.dbl !== undefined) {
    output.writeFieldBegin('dbl', Thrift.Type.DOUBLE, 4);
    output.writeDouble(this.dbl);
    output.writeFieldEnd();
  }
  output.writeFieldStop();
  output.writeStructEnd();
  return;
};

var StepGroupObject_Res = module.exports.StepGroupObject_Res = function(args) {
  this.attrs = null;
  this.val = null;
  if (args) {
    if (args.attrs !== undefined && args.attrs !== null) {
      this.attrs = Thrift.copyList(args.attrs, [ttypes.MixDataStruct_Res]);
    }
    if (args.val !== undefined && args.val !== null) {
      this.val = args.val;
    }
  }
};
StepGroupObject_Res.prototype = {};
StepGroupObject_Res.prototype.read = function(input) {
  input.readStructBegin();
  while (true) {
    var ret = input.readFieldBegin();
    var ftype = ret.ftype;
    var fid = ret.fid;
    if (ftype == Thrift.Type.STOP) {
      break;
    }
    switch (fid) {
      case 1:
      if (ftype == Thrift.Type.LIST) {
        this.attrs = [];
        var _rtmp3470 = input.readListBegin();
        var _size469 = _rtmp3470.size || 0;
        for (var _i471 = 0; _i471 < _size469; ++_i471) {
          var elem472 = null;
          elem472 = new ttypes.MixDataStruct_Res();
          elem472.read(input);
          this.attrs.push(elem472);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 2:
      if (ftype == Thrift.Type.I64) {
        this.val = input.readI64();
      } else {
        input.skip(ftype);
      }
      break;
      default:
        input.skip(ftype);
    }
    input.readFieldEnd();
  }
  input.readStructEnd();
  return;
};

StepGroupObject_Res.prototype.write = function(output) {
  output.writeStructBegin('StepGroupObject_Res');
  if (this.attrs !== null && this.attrs !== undefined) {
    output.writeFieldBegin('attrs', Thrift.Type.LIST, 1);
    output.writeListBegin(Thrift.Type.STRUCT, this.attrs.length);
    for (var iter473 in this.attrs) {
      if (this.attrs.hasOwnProperty(iter473)) {
        iter473 = this.attrs[iter473];
        iter473.write(output);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.val !== null && this.val !== undefined) {
    output.writeFieldBegin('val', Thrift.Type.I64, 2);
    output.writeI64(this.val);
    output.writeFieldEnd();
  }
  output.writeFieldStop();
  output.writeStructEnd();
  return;
};

var KeyValuePair_Res = module.exports.KeyValuePair_Res = function(args) {
  this.kid = null;
  this.val = null;
  if (args) {
    if (args.kid !== undefined && args.kid !== null) {
      this.kid = args.kid;
    }
    if (args.val !== undefined && args.val !== null) {
      this.val = args.val;
    }
  }
};
KeyValuePair_Res.prototype = {};
KeyValuePair_Res.prototype.read = function(input) {
  input.readStructBegin();
  while (true) {
    var ret = input.readFieldBegin();
    var ftype = ret.ftype;
    var fid = ret.fid;
    if (ftype == Thrift.Type.STOP) {
      break;
    }
    switch (fid) {
      case 1:
      if (ftype == Thrift.Type.I64) {
        this.kid = input.readI64();
      } else {
        input.skip(ftype);
      }
      break;
      case 2:
      if (ftype == Thrift.Type.DOUBLE) {
        this.val = input.readDouble();
      } else {
        input.skip(ftype);
      }
      break;
      default:
        input.skip(ftype);
    }
    input.readFieldEnd();
  }
  input.readStructEnd();
  return;
};

KeyValuePair_Res.prototype.write = function(output) {
  output.writeStructBegin('KeyValuePair_Res');
  if (this.kid !== null && this.kid !== undefined) {
    output.writeFieldBegin('kid', Thrift.Type.I64, 1);
    output.writeI64(this.kid);
    output.writeFieldEnd();
  }
  if (this.val !== null && this.val !== undefined) {
    output.writeFieldBegin('val', Thrift.Type.DOUBLE, 2);
    output.writeDouble(this.val);
    output.writeFieldEnd();
  }
  output.writeFieldStop();
  output.writeStructEnd();
  return;
};

var PageList_Res = module.exports.PageList_Res = function(args) {
  this.ids = null;
  this.scrollId = null;
  if (args) {
    if (args.ids !== undefined && args.ids !== null) {
      this.ids = Thrift.copyList(args.ids, [null]);
    }
    if (args.scrollId !== undefined && args.scrollId !== null) {
      this.scrollId = args.scrollId;
    }
  }
};
PageList_Res.prototype = {};
PageList_Res.prototype.read = function(input) {
  input.readStructBegin();
  while (true) {
    var ret = input.readFieldBegin();
    var ftype = ret.ftype;
    var fid = ret.fid;
    if (ftype == Thrift.Type.STOP) {
      break;
    }
    switch (fid) {
      case 1:
      if (ftype == Thrift.Type.LIST) {
        this.ids = [];
        var _rtmp3475 = input.readListBegin();
        var _size474 = _rtmp3475.size || 0;
        for (var _i476 = 0; _i476 < _size474; ++_i476) {
          var elem477 = null;
          elem477 = input.readI64();
          this.ids.push(elem477);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 2:
      if (ftype == Thrift.Type.I64) {
        this.scrollId = input.readI64();
      } else {
        input.skip(ftype);
      }
      break;
      default:
        input.skip(ftype);
    }
    input.readFieldEnd();
  }
  input.readStructEnd();
  return;
};

PageList_Res.prototype.write = function(output) {
  output.writeStructBegin('PageList_Res');
  if (this.ids !== null && this.ids !== undefined) {
    output.writeFieldBegin('ids', Thrift.Type.LIST, 1);
    output.writeListBegin(Thrift.Type.I64, this.ids.length);
    for (var iter478 in this.ids) {
      if (this.ids.hasOwnProperty(iter478)) {
        iter478 = this.ids[iter478];
        output.writeI64(iter478);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.scrollId !== null && this.scrollId !== undefined) {
    output.writeFieldBegin('scrollId', Thrift.Type.I64, 2);
    output.writeI64(this.scrollId);
    output.writeFieldEnd();
  }
  output.writeFieldStop();
  output.writeStructEnd();
  return;
};

var TagJobSample_Res = module.exports.TagJobSample_Res = function(args) {
  this.total = null;
  this.sample = null;
  this.result = null;
  this.tagMap = null;
  this.state = null;
  this.brand_total_map = null;
  this.brand_result_map = null;
  this.brand_tag_map = null;
  if (args) {
    if (args.total !== undefined && args.total !== null) {
      this.total = args.total;
    }
    if (args.sample !== undefined && args.sample !== null) {
      this.sample = args.sample;
    }
    if (args.result !== undefined && args.result !== null) {
      this.result = args.result;
    }
    if (args.tagMap !== undefined && args.tagMap !== null) {
      this.tagMap = Thrift.copyMap(args.tagMap, [null]);
    }
    if (args.state !== undefined && args.state !== null) {
      this.state = args.state;
    }
    if (args.brand_total_map !== undefined && args.brand_total_map !== null) {
      this.brand_total_map = Thrift.copyMap(args.brand_total_map, [null]);
    }
    if (args.brand_result_map !== undefined && args.brand_result_map !== null) {
      this.brand_result_map = Thrift.copyMap(args.brand_result_map, [null]);
    }
    if (args.brand_tag_map !== undefined && args.brand_tag_map !== null) {
      this.brand_tag_map = Thrift.copyMap(args.brand_tag_map, [Thrift.copyMap, null]);
    }
  }
};
TagJobSample_Res.prototype = {};
TagJobSample_Res.prototype.read = function(input) {
  input.readStructBegin();
  while (true) {
    var ret = input.readFieldBegin();
    var ftype = ret.ftype;
    var fid = ret.fid;
    if (ftype == Thrift.Type.STOP) {
      break;
    }
    switch (fid) {
      case 1:
      if (ftype == Thrift.Type.I64) {
        this.total = input.readI64();
      } else {
        input.skip(ftype);
      }
      break;
      case 2:
      if (ftype == Thrift.Type.I64) {
        this.sample = input.readI64();
      } else {
        input.skip(ftype);
      }
      break;
      case 3:
      if (ftype == Thrift.Type.I64) {
        this.result = input.readI64();
      } else {
        input.skip(ftype);
      }
      break;
      case 4:
      if (ftype == Thrift.Type.MAP) {
        this.tagMap = {};
        var _rtmp3480 = input.readMapBegin();
        var _size479 = _rtmp3480.size || 0;
        for (var _i481 = 0; _i481 < _size479; ++_i481) {
          var key482 = null;
          var val483 = null;
          key482 = input.readI64();
          val483 = input.readI32();
          this.tagMap[key482] = val483;
        }
        input.readMapEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 5:
      if (ftype == Thrift.Type.I32) {
        this.state = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 6:
      if (ftype == Thrift.Type.MAP) {
        this.brand_total_map = {};
        var _rtmp3485 = input.readMapBegin();
        var _size484 = _rtmp3485.size || 0;
        for (var _i486 = 0; _i486 < _size484; ++_i486) {
          var key487 = null;
          var val488 = null;
          key487 = input.readI32();
          val488 = input.readI32();
          this.brand_total_map[key487] = val488;
        }
        input.readMapEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 7:
      if (ftype == Thrift.Type.MAP) {
        this.brand_result_map = {};
        var _rtmp3490 = input.readMapBegin();
        var _size489 = _rtmp3490.size || 0;
        for (var _i491 = 0; _i491 < _size489; ++_i491) {
          var key492 = null;
          var val493 = null;
          key492 = input.readI32();
          val493 = input.readI32();
          this.brand_result_map[key492] = val493;
        }
        input.readMapEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 8:
      if (ftype == Thrift.Type.MAP) {
        this.brand_tag_map = {};
        var _rtmp3495 = input.readMapBegin();
        var _size494 = _rtmp3495.size || 0;
        for (var _i496 = 0; _i496 < _size494; ++_i496) {
          var key497 = null;
          var val498 = null;
          key497 = input.readI32();
          val498 = {};
          var _rtmp3500 = input.readMapBegin();
          var _size499 = _rtmp3500.size || 0;
          for (var _i501 = 0; _i501 < _size499; ++_i501) {
            var key502 = null;
            var val503 = null;
            key502 = input.readI64();
            val503 = input.readI32();
            val498[key502] = val503;
          }
          input.readMapEnd();
          this.brand_tag_map[key497] = val498;
        }
        input.readMapEnd();
      } else {
        input.skip(ftype);
      }
      break;
      default:
        input.skip(ftype);
    }
    input.readFieldEnd();
  }
  input.readStructEnd();
  return;
};

TagJobSample_Res.prototype.write = function(output) {
  output.writeStructBegin('TagJobSample_Res');
  if (this.total !== null && this.total !== undefined) {
    output.writeFieldBegin('total', Thrift.Type.I64, 1);
    output.writeI64(this.total);
    output.writeFieldEnd();
  }
  if (this.sample !== null && this.sample !== undefined) {
    output.writeFieldBegin('sample', Thrift.Type.I64, 2);
    output.writeI64(this.sample);
    output.writeFieldEnd();
  }
  if (this.result !== null && this.result !== undefined) {
    output.writeFieldBegin('result', Thrift.Type.I64, 3);
    output.writeI64(this.result);
    output.writeFieldEnd();
  }
  if (this.tagMap !== null && this.tagMap !== undefined) {
    output.writeFieldBegin('tagMap', Thrift.Type.MAP, 4);
    output.writeMapBegin(Thrift.Type.I64, Thrift.Type.I32, Thrift.objectLength(this.tagMap));
    for (var kiter504 in this.tagMap) {
      if (this.tagMap.hasOwnProperty(kiter504)) {
        var viter505 = this.tagMap[kiter504];
        output.writeI64(kiter504);
        output.writeI32(viter505);
      }
    }
    output.writeMapEnd();
    output.writeFieldEnd();
  }
  if (this.state !== null && this.state !== undefined) {
    output.writeFieldBegin('state', Thrift.Type.I32, 5);
    output.writeI32(this.state);
    output.writeFieldEnd();
  }
  if (this.brand_total_map !== null && this.brand_total_map !== undefined) {
    output.writeFieldBegin('brand_total_map', Thrift.Type.MAP, 6);
    output.writeMapBegin(Thrift.Type.I32, Thrift.Type.I32, Thrift.objectLength(this.brand_total_map));
    for (var kiter506 in this.brand_total_map) {
      if (this.brand_total_map.hasOwnProperty(kiter506)) {
        var viter507 = this.brand_total_map[kiter506];
        output.writeI32(kiter506);
        output.writeI32(viter507);
      }
    }
    output.writeMapEnd();
    output.writeFieldEnd();
  }
  if (this.brand_result_map !== null && this.brand_result_map !== undefined) {
    output.writeFieldBegin('brand_result_map', Thrift.Type.MAP, 7);
    output.writeMapBegin(Thrift.Type.I32, Thrift.Type.I32, Thrift.objectLength(this.brand_result_map));
    for (var kiter508 in this.brand_result_map) {
      if (this.brand_result_map.hasOwnProperty(kiter508)) {
        var viter509 = this.brand_result_map[kiter508];
        output.writeI32(kiter508);
        output.writeI32(viter509);
      }
    }
    output.writeMapEnd();
    output.writeFieldEnd();
  }
  if (this.brand_tag_map !== null && this.brand_tag_map !== undefined) {
    output.writeFieldBegin('brand_tag_map', Thrift.Type.MAP, 8);
    output.writeMapBegin(Thrift.Type.I32, Thrift.Type.MAP, Thrift.objectLength(this.brand_tag_map));
    for (var kiter510 in this.brand_tag_map) {
      if (this.brand_tag_map.hasOwnProperty(kiter510)) {
        var viter511 = this.brand_tag_map[kiter510];
        output.writeI32(kiter510);
        output.writeMapBegin(Thrift.Type.I64, Thrift.Type.I32, Thrift.objectLength(viter511));
        for (var kiter512 in viter511) {
          if (viter511.hasOwnProperty(kiter512)) {
            var viter513 = viter511[kiter512];
            output.writeI64(kiter512);
            output.writeI32(viter513);
          }
        }
        output.writeMapEnd();
      }
    }
    output.writeMapEnd();
    output.writeFieldEnd();
  }
  output.writeFieldStop();
  output.writeStructEnd();
  return;
};

var PathCalcJob_Num_Res = module.exports.PathCalcJob_Num_Res = function(args) {
  this.max = null;
  this.min = null;
  this.sum = null;
  this.max_d = null;
  this.min_d = null;
  this.sum_d = null;
  this.count = null;
  this.distinct_count = null;
  if (args) {
    if (args.max !== undefined && args.max !== null) {
      this.max = args.max;
    }
    if (args.min !== undefined && args.min !== null) {
      this.min = args.min;
    }
    if (args.sum !== undefined && args.sum !== null) {
      this.sum = args.sum;
    }
    if (args.max_d !== undefined && args.max_d !== null) {
      this.max_d = args.max_d;
    }
    if (args.min_d !== undefined && args.min_d !== null) {
      this.min_d = args.min_d;
    }
    if (args.sum_d !== undefined && args.sum_d !== null) {
      this.sum_d = args.sum_d;
    }
    if (args.count !== undefined && args.count !== null) {
      this.count = args.count;
    }
    if (args.distinct_count !== undefined && args.distinct_count !== null) {
      this.distinct_count = args.distinct_count;
    }
  }
};
PathCalcJob_Num_Res.prototype = {};
PathCalcJob_Num_Res.prototype.read = function(input) {
  input.readStructBegin();
  while (true) {
    var ret = input.readFieldBegin();
    var ftype = ret.ftype;
    var fid = ret.fid;
    if (ftype == Thrift.Type.STOP) {
      break;
    }
    switch (fid) {
      case 1:
      if (ftype == Thrift.Type.I64) {
        this.max = input.readI64();
      } else {
        input.skip(ftype);
      }
      break;
      case 2:
      if (ftype == Thrift.Type.I64) {
        this.min = input.readI64();
      } else {
        input.skip(ftype);
      }
      break;
      case 3:
      if (ftype == Thrift.Type.I64) {
        this.sum = input.readI64();
      } else {
        input.skip(ftype);
      }
      break;
      case 4:
      if (ftype == Thrift.Type.DOUBLE) {
        this.max_d = input.readDouble();
      } else {
        input.skip(ftype);
      }
      break;
      case 5:
      if (ftype == Thrift.Type.DOUBLE) {
        this.min_d = input.readDouble();
      } else {
        input.skip(ftype);
      }
      break;
      case 6:
      if (ftype == Thrift.Type.DOUBLE) {
        this.sum_d = input.readDouble();
      } else {
        input.skip(ftype);
      }
      break;
      case 7:
      if (ftype == Thrift.Type.I64) {
        this.count = input.readI64();
      } else {
        input.skip(ftype);
      }
      break;
      case 8:
      if (ftype == Thrift.Type.I64) {
        this.distinct_count = input.readI64();
      } else {
        input.skip(ftype);
      }
      break;
      default:
        input.skip(ftype);
    }
    input.readFieldEnd();
  }
  input.readStructEnd();
  return;
};

PathCalcJob_Num_Res.prototype.write = function(output) {
  output.writeStructBegin('PathCalcJob_Num_Res');
  if (this.max !== null && this.max !== undefined) {
    output.writeFieldBegin('max', Thrift.Type.I64, 1);
    output.writeI64(this.max);
    output.writeFieldEnd();
  }
  if (this.min !== null && this.min !== undefined) {
    output.writeFieldBegin('min', Thrift.Type.I64, 2);
    output.writeI64(this.min);
    output.writeFieldEnd();
  }
  if (this.sum !== null && this.sum !== undefined) {
    output.writeFieldBegin('sum', Thrift.Type.I64, 3);
    output.writeI64(this.sum);
    output.writeFieldEnd();
  }
  if (this.max_d !== null && this.max_d !== undefined) {
    output.writeFieldBegin('max_d', Thrift.Type.DOUBLE, 4);
    output.writeDouble(this.max_d);
    output.writeFieldEnd();
  }
  if (this.min_d !== null && this.min_d !== undefined) {
    output.writeFieldBegin('min_d', Thrift.Type.DOUBLE, 5);
    output.writeDouble(this.min_d);
    output.writeFieldEnd();
  }
  if (this.sum_d !== null && this.sum_d !== undefined) {
    output.writeFieldBegin('sum_d', Thrift.Type.DOUBLE, 6);
    output.writeDouble(this.sum_d);
    output.writeFieldEnd();
  }
  if (this.count !== null && this.count !== undefined) {
    output.writeFieldBegin('count', Thrift.Type.I64, 7);
    output.writeI64(this.count);
    output.writeFieldEnd();
  }
  if (this.distinct_count !== null && this.distinct_count !== undefined) {
    output.writeFieldBegin('distinct_count', Thrift.Type.I64, 8);
    output.writeI64(this.distinct_count);
    output.writeFieldEnd();
  }
  output.writeFieldStop();
  output.writeStructEnd();
  return;
};

var PathCalcJob_Agg_Res = module.exports.PathCalcJob_Agg_Res = function(args) {
  this.groups = null;
  this.func_val = null;
  if (args) {
    if (args.groups !== undefined && args.groups !== null) {
      this.groups = Thrift.copyList(args.groups, [null]);
    }
    if (args.func_val !== undefined && args.func_val !== null) {
      this.func_val = new ttypes.PathCalcJob_Num_Res(args.func_val);
    }
  }
};
PathCalcJob_Agg_Res.prototype = {};
PathCalcJob_Agg_Res.prototype.read = function(input) {
  input.readStructBegin();
  while (true) {
    var ret = input.readFieldBegin();
    var ftype = ret.ftype;
    var fid = ret.fid;
    if (ftype == Thrift.Type.STOP) {
      break;
    }
    switch (fid) {
      case 1:
      if (ftype == Thrift.Type.LIST) {
        this.groups = [];
        var _rtmp3515 = input.readListBegin();
        var _size514 = _rtmp3515.size || 0;
        for (var _i516 = 0; _i516 < _size514; ++_i516) {
          var elem517 = null;
          elem517 = input.readString();
          this.groups.push(elem517);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 2:
      if (ftype == Thrift.Type.STRUCT) {
        this.func_val = new ttypes.PathCalcJob_Num_Res();
        this.func_val.read(input);
      } else {
        input.skip(ftype);
      }
      break;
      default:
        input.skip(ftype);
    }
    input.readFieldEnd();
  }
  input.readStructEnd();
  return;
};

PathCalcJob_Agg_Res.prototype.write = function(output) {
  output.writeStructBegin('PathCalcJob_Agg_Res');
  if (this.groups !== null && this.groups !== undefined) {
    output.writeFieldBegin('groups', Thrift.Type.LIST, 1);
    output.writeListBegin(Thrift.Type.STRING, this.groups.length);
    for (var iter518 in this.groups) {
      if (this.groups.hasOwnProperty(iter518)) {
        iter518 = this.groups[iter518];
        output.writeString(iter518);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.func_val !== null && this.func_val !== undefined) {
    output.writeFieldBegin('func_val', Thrift.Type.STRUCT, 2);
    this.func_val.write(output);
    output.writeFieldEnd();
  }
  output.writeFieldStop();
  output.writeStructEnd();
  return;
};

var PathCalcJob_Res = module.exports.PathCalcJob_Res = function(args) {
  this.calc_type = null;
  this.num_type = null;
  this.func_val = null;
  this.agg_val = null;
  this.offset = null;
  if (args) {
    if (args.calc_type !== undefined && args.calc_type !== null) {
      this.calc_type = args.calc_type;
    }
    if (args.num_type !== undefined && args.num_type !== null) {
      this.num_type = args.num_type;
    }
    if (args.func_val !== undefined && args.func_val !== null) {
      this.func_val = new ttypes.PathCalcJob_Num_Res(args.func_val);
    }
    if (args.agg_val !== undefined && args.agg_val !== null) {
      this.agg_val = Thrift.copyList(args.agg_val, [ttypes.PathCalcJob_Agg_Res]);
    }
    if (args.offset !== undefined && args.offset !== null) {
      this.offset = args.offset;
    }
  }
};
PathCalcJob_Res.prototype = {};
PathCalcJob_Res.prototype.read = function(input) {
  input.readStructBegin();
  while (true) {
    var ret = input.readFieldBegin();
    var ftype = ret.ftype;
    var fid = ret.fid;
    if (ftype == Thrift.Type.STOP) {
      break;
    }
    switch (fid) {
      case 1:
      if (ftype == Thrift.Type.I32) {
        this.calc_type = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 2:
      if (ftype == Thrift.Type.I32) {
        this.num_type = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 3:
      if (ftype == Thrift.Type.STRUCT) {
        this.func_val = new ttypes.PathCalcJob_Num_Res();
        this.func_val.read(input);
      } else {
        input.skip(ftype);
      }
      break;
      case 4:
      if (ftype == Thrift.Type.LIST) {
        this.agg_val = [];
        var _rtmp3520 = input.readListBegin();
        var _size519 = _rtmp3520.size || 0;
        for (var _i521 = 0; _i521 < _size519; ++_i521) {
          var elem522 = null;
          elem522 = new ttypes.PathCalcJob_Agg_Res();
          elem522.read(input);
          this.agg_val.push(elem522);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 5:
      if (ftype == Thrift.Type.I64) {
        this.offset = input.readI64();
      } else {
        input.skip(ftype);
      }
      break;
      default:
        input.skip(ftype);
    }
    input.readFieldEnd();
  }
  input.readStructEnd();
  return;
};

PathCalcJob_Res.prototype.write = function(output) {
  output.writeStructBegin('PathCalcJob_Res');
  if (this.calc_type !== null && this.calc_type !== undefined) {
    output.writeFieldBegin('calc_type', Thrift.Type.I32, 1);
    output.writeI32(this.calc_type);
    output.writeFieldEnd();
  }
  if (this.num_type !== null && this.num_type !== undefined) {
    output.writeFieldBegin('num_type', Thrift.Type.I32, 2);
    output.writeI32(this.num_type);
    output.writeFieldEnd();
  }
  if (this.func_val !== null && this.func_val !== undefined) {
    output.writeFieldBegin('func_val', Thrift.Type.STRUCT, 3);
    this.func_val.write(output);
    output.writeFieldEnd();
  }
  if (this.agg_val !== null && this.agg_val !== undefined) {
    output.writeFieldBegin('agg_val', Thrift.Type.LIST, 4);
    output.writeListBegin(Thrift.Type.STRUCT, this.agg_val.length);
    for (var iter523 in this.agg_val) {
      if (this.agg_val.hasOwnProperty(iter523)) {
        iter523 = this.agg_val[iter523];
        iter523.write(output);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.offset !== null && this.offset !== undefined) {
    output.writeFieldBegin('offset', Thrift.Type.I64, 5);
    output.writeI64(this.offset);
    output.writeFieldEnd();
  }
  output.writeFieldStop();
  output.writeStructEnd();
  return;
};

var SameTargetQuery_Req = module.exports.SameTargetQuery_Req = function(args) {
  this.graph = null;
  this.throughEdges = null;
  this.vids = null;
  this.filterVector = null;
  this.resSort = 0;
  this.threadCnt = 1;
  this.resLimit = 100;
  if (args) {
    if (args.graph !== undefined && args.graph !== null) {
      this.graph = args.graph;
    }
    if (args.throughEdges !== undefined && args.throughEdges !== null) {
      this.throughEdges = Thrift.copyList(args.throughEdges, [null]);
    }
    if (args.vids !== undefined && args.vids !== null) {
      this.vids = Thrift.copyList(args.vids, [null]);
    }
    if (args.filterVector !== undefined && args.filterVector !== null) {
      this.filterVector = Thrift.copyList(args.filterVector, [ttypes.ReadFilterSet_Req]);
    }
    if (args.resSort !== undefined && args.resSort !== null) {
      this.resSort = args.resSort;
    }
    if (args.threadCnt !== undefined && args.threadCnt !== null) {
      this.threadCnt = args.threadCnt;
    }
    if (args.resLimit !== undefined && args.resLimit !== null) {
      this.resLimit = args.resLimit;
    }
  }
};
SameTargetQuery_Req.prototype = {};
SameTargetQuery_Req.prototype.read = function(input) {
  input.readStructBegin();
  while (true) {
    var ret = input.readFieldBegin();
    var ftype = ret.ftype;
    var fid = ret.fid;
    if (ftype == Thrift.Type.STOP) {
      break;
    }
    switch (fid) {
      case 1:
      if (ftype == Thrift.Type.STRING) {
        this.graph = input.readString();
      } else {
        input.skip(ftype);
      }
      break;
      case 2:
      if (ftype == Thrift.Type.LIST) {
        this.throughEdges = [];
        var _rtmp3525 = input.readListBegin();
        var _size524 = _rtmp3525.size || 0;
        for (var _i526 = 0; _i526 < _size524; ++_i526) {
          var elem527 = null;
          elem527 = input.readString();
          this.throughEdges.push(elem527);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 3:
      if (ftype == Thrift.Type.LIST) {
        this.vids = [];
        var _rtmp3529 = input.readListBegin();
        var _size528 = _rtmp3529.size || 0;
        for (var _i530 = 0; _i530 < _size528; ++_i530) {
          var elem531 = null;
          elem531 = input.readI64();
          this.vids.push(elem531);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 4:
      if (ftype == Thrift.Type.LIST) {
        this.filterVector = [];
        var _rtmp3533 = input.readListBegin();
        var _size532 = _rtmp3533.size || 0;
        for (var _i534 = 0; _i534 < _size532; ++_i534) {
          var elem535 = null;
          elem535 = new ttypes.ReadFilterSet_Req();
          elem535.read(input);
          this.filterVector.push(elem535);
        }
        input.readListEnd();
      } else {
        input.skip(ftype);
      }
      break;
      case 5:
      if (ftype == Thrift.Type.I32) {
        this.resSort = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 6:
      if (ftype == Thrift.Type.I32) {
        this.threadCnt = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      case 7:
      if (ftype == Thrift.Type.I32) {
        this.resLimit = input.readI32();
      } else {
        input.skip(ftype);
      }
      break;
      default:
        input.skip(ftype);
    }
    input.readFieldEnd();
  }
  input.readStructEnd();
  return;
};

SameTargetQuery_Req.prototype.write = function(output) {
  output.writeStructBegin('SameTargetQuery_Req');
  if (this.graph !== null && this.graph !== undefined) {
    output.writeFieldBegin('graph', Thrift.Type.STRING, 1);
    output.writeString(this.graph);
    output.writeFieldEnd();
  }
  if (this.throughEdges !== null && this.throughEdges !== undefined) {
    output.writeFieldBegin('throughEdges', Thrift.Type.LIST, 2);
    output.writeListBegin(Thrift.Type.STRING, this.throughEdges.length);
    for (var iter536 in this.throughEdges) {
      if (this.throughEdges.hasOwnProperty(iter536)) {
        iter536 = this.throughEdges[iter536];
        output.writeString(iter536);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.vids !== null && this.vids !== undefined) {
    output.writeFieldBegin('vids', Thrift.Type.LIST, 3);
    output.writeListBegin(Thrift.Type.I64, this.vids.length);
    for (var iter537 in this.vids) {
      if (this.vids.hasOwnProperty(iter537)) {
        iter537 = this.vids[iter537];
        output.writeI64(iter537);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.filterVector !== null && this.filterVector !== undefined) {
    output.writeFieldBegin('filterVector', Thrift.Type.LIST, 4);
    output.writeListBegin(Thrift.Type.STRUCT, this.filterVector.length);
    for (var iter538 in this.filterVector) {
      if (this.filterVector.hasOwnProperty(iter538)) {
        iter538 = this.filterVector[iter538];
        iter538.write(output);
      }
    }
    output.writeListEnd();
    output.writeFieldEnd();
  }
  if (this.resSort !== null && this.resSort !== undefined) {
    output.writeFieldBegin('resSort', Thrift.Type.I32, 5);
    output.writeI32(this.resSort);
    output.writeFieldEnd();
  }
  if (this.threadCnt !== null && this.threadCnt !== undefined) {
    output.writeFieldBegin('threadCnt', Thrift.Type.I32, 6);
    output.writeI32(this.threadCnt);
    output.writeFieldEnd();
  }
  if (this.resLimit !== null && this.resLimit !== undefined) {
    output.writeFieldBegin('resLimit', Thrift.Type.I32, 7);
    output.writeI32(this.resLimit);
    output.writeFieldEnd();
  }
  output.writeFieldStop();
  output.writeStructEnd();
  return;
};

ttypes.INT32CONSTANT = 9853;
ttypes.MAPCONSTANT = {
  'goodnight' : 'moon',
  'hello' : 'world'
};
