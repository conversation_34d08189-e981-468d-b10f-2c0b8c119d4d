import React from 'react';
import { useToast } from '../contexts/ToastContext';

const ToastDemo: React.FC = () => {
  const { showSuccess, showError, showWarning, showInfo, showToast, clearAllToasts } = useToast();

  const handleShowSuccess = () => {
    showSuccess('操作成功', '数据已成功保存到数据库');
  };

  const handleShowError = () => {
    showError('操作失败', '网络连接超时，请稍后重试');
  };

  const handleShowWarning = () => {
    showWarning('注意', '您的会话即将过期，请及时保存数据');
  };

  const handleShowInfo = () => {
    showInfo('提示', '新版本已发布，建议您更新到最新版本');
  };

  const handleShowCustom = () => {
    showToast({
      type: 'success',
      title: '自定义 Toast',
      message: '这是一个带有操作按钮的 Toast',
      duration: 0, // 不自动消失
      action: {
        label: '查看详情',
        onClick: () => {
          alert('查看详情被点击');
        }
      }
    });
  };

  const handleShowMultiple = () => {
    showSuccess('第一个消息');
    setTimeout(() => showError('第二个消息'), 500);
    setTimeout(() => showWarning('第三个消息'), 1000);
    setTimeout(() => showInfo('第四个消息'), 1500);
  };

  return (
    <div className="p-8 space-y-4">
      <h1 className="text-2xl font-bold text-white mb-6" style={{fontFamily: 'PingFang SC'}}>
        Toast 系统演示
      </h1>
      
      <div className="grid grid-cols-2 gap-4 max-w-2xl">
        <button
          onClick={handleShowSuccess}
          className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
          style={{fontFamily: 'PingFang SC'}}
        >
          显示成功消息
        </button>

        <button
          onClick={handleShowError}
          className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
          style={{fontFamily: 'PingFang SC'}}
        >
          显示错误消息
        </button>

        <button
          onClick={handleShowWarning}
          className="px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors"
          style={{fontFamily: 'PingFang SC'}}
        >
          显示警告消息
        </button>

        <button
          onClick={handleShowInfo}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          style={{fontFamily: 'PingFang SC'}}
        >
          显示信息消息
        </button>

        <button
          onClick={handleShowCustom}
          className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
          style={{fontFamily: 'PingFang SC'}}
        >
          显示自定义 Toast
        </button>

        <button
          onClick={handleShowMultiple}
          className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
          style={{fontFamily: 'PingFang SC'}}
        >
          显示多个消息
        </button>

        <button
          onClick={clearAllToasts}
          className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors col-span-2"
          style={{fontFamily: 'PingFang SC'}}
        >
          清除所有消息
        </button>
      </div>

      <div className="mt-8 p-4 bg-gray-800 rounded-lg">
        <h2 className="text-lg font-semibold text-white mb-2" style={{fontFamily: 'PingFang SC'}}>
          使用方法
        </h2>
        <pre className="text-sm text-gray-300 overflow-x-auto">
{`// 在任何组件中使用
import { useToast } from '../contexts/ToastContext';

const MyComponent = () => {
  const { showSuccess, showError, showWarning, showInfo } = useToast();
  
  const handleClick = () => {
    showSuccess('操作成功', '可选的详细信息');
  };
  
  return <button onClick={handleClick}>点击我</button>;
};`}
        </pre>
      </div>
    </div>
  );
};

export default ToastDemo;
