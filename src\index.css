@import 'tailwindcss';

@theme {
  /* 根据 Figma 设计规范配置颜色主题 */
  --color-primary: #000000;        /* 主背景 - 黑色 */
  --color-card-bg: rgba(60, 60, 60, 0.8); /* 卡片背景 - 半透明灰色 */
  --color-nav-bg: #3c3c3c;         /* 导航栏背景 */
  --color-highlight: #679cff;      /* 高亮色 - 蓝色 */
  --color-accent: #FB956B;         /* 强调色 - 红色 */
  --color-secondary: #F2BC29;      /* 次要强调色 - 黄色 */
  --color-text-primary: #ffffff;   /* 主要文本 - 白色 */
  --color-text-secondary: #d9d9d9; /* 次要文本 - 浅灰 */
  --color-border-color: #9d9d9d;   /* 边框颜色 */

  /* 字体配置 */
  --font-family-sans: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;

  /* 字体大小 */
  --font-size-base: 16px;          /* 常规文本 */
  --font-size-lg: 18px;            /* 正文文本 */
  --font-size-xl: 22px;            /* 标题 */
  --font-size-3xl: 36px;           /* 大标题 */

  /* 图标尺寸 */
  --size-icon: 24px;               /* 图标尺寸 24px */

  /* 间距 */
  --spacing-min: 10px;             /* 最小间距 */

  /* 行高 */
  --line-height-relaxed: 1.5;      /* 1.5倍行高 */
}

:root {
  font-family: var(--font-family-sans);
  line-height: var(--line-height-relaxed);
  font-weight: 400;

  color-scheme: dark;
  color: var(--color-text-primary);
  background-color: var(--color-primary);

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
}

/* 自定义样式类 */
.icon-size {
  width: var(--size-icon);
  height: var(--size-icon);
}

.text-primary {
  color: var(--color-text-primary);
}

.text-secondary {
  color: var(--color-text-secondary);
}

.bg-primary {
  background-color: var(--color-primary);
}

.bg-card-bg {
  background-color: var(--color-card-bg);
}

.bg-nav-bg {
  background-color: var(--color-nav-bg);
}

.bg-highlight {
  background-color: var(--color-highlight);
}

.bg-accent {
  background-color: var(--color-accent);
}

.bg-secondary {
  background-color: var(--color-secondary);
}

.text-text-primary {
  color: var(--color-text-primary);
}

.text-text-secondary {
  color: var(--color-text-secondary);
}

.border-border-color {
  border-color: var(--color-border-color);
}
