//
// Autogenerated by Thrift Compiler (0.22.0)
//
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
//
import thrift = require('thrift');
import Thrift = thrift.Thrift;
import Q = thrift.Q;
import Int64 = require('node-int64');


/**
 * You can define enums, which are just 32 bit integers. Values are optional
 * and start at 1 if not supplied, C style again.
 */
declare enum Operation {
  ADD = 1,
  SUBTRACT = 2,
  MULTIPLY = 3,
  DIVIDE = 4,
}

declare enum DataTypeEnum {
  INT_TYPE = 1,
  DOUBLE_TYPE = 2,
  STRING_Type = 3,
}

/**
 * Structs are the basic complex data structures. They are comprised of fields
 * which each have an integer identifier, a type, a symbolic name, and an
 * optional default value.
 * 
 * Fields can be declared "optional", which ensures they will not be included
 * in the serialized output if they aren't set.  Note that this requires some
 * manual management in some languages.
 */
declare class Work {
    public num1?: number;
    public num2: number;
    public op: Operation;
    public comment?: string;

      constructor(args?: { num1?: number; num2: number; op: Operation; comment?: string; });
  }

declare class EPair {
    public eid: Int64;
    public vid: Int64;

      constructor(args?: { eid: Int64; vid: Int64; });
  }

declare class VertexPk {
    public id: Int64;
    public id_l: Int64;
    public id_s: string;
    public vertexName: string;

      constructor(args?: { id: Int64; id_l: Int64; id_s: string; vertexName: string; });
  }

declare class EdgePk {
    public id: Int64;
    public edgeName: string;
    public srcid: Int64;
    public dstid: Int64;

      constructor(args?: { id: Int64; edgeName: string; srcid: Int64; dstid: Int64; });
  }

declare class VertexID {
    public id: Int64;
    public name: string;

      constructor(args?: { id: Int64; name: string; });
  }

declare class IntervalPair {
    public left: Int64;
    public right: Int64;
    public name: string;

      constructor(args?: { left: Int64; right: Int64; name: string; });
  }

/**
 * Structs can also be exceptions, if they are nasty.
 */
declare class InvalidOperation extends Thrift.TException {
    public whatOp: number;
    public why: string;

      constructor(args?: { whatOp: number; why: string; });
  }

declare class ReadNodeFilter_Req {
    public filterType?: number;
    public nodeName: string;
    public attrName: string;
    public upper: string;
    public lower: string;

      constructor(args?: { filterType?: number; nodeName: string; attrName: string; upper: string; lower: string; });
  }

declare class ReadFilter_Req {
    public filterType?: number;
    public nodeName: string;
    public attrName: string;
    public upper: string;
    public lower: string;
    public targetFilter: ReadNodeFilter_Req;

      constructor(args?: { filterType?: number; nodeName: string; attrName: string; upper: string; lower: string; targetFilter: ReadNodeFilter_Req; });
  }

declare class ReadFilterSet_Req {
    public filters: ReadFilter_Req[];
    public relationType: number;

      constructor(args?: { filters: ReadFilter_Req[]; relationType: number; });
  }

declare class ReadFilterQuery_Req {
    public graph: string;
    public startVertex: string;
    public startIds: Int64[];
    public throughEdges: string[];
    public targetEdgeNodeName: string;
    public isReturnEdge?: number;
    public count?: number;
    public stepLayerLimit?: number;
    public filterMap: { [k: string]: ReadFilterSet_Req; };
    public targetFilter: ReadFilterSet_Req;

      constructor(args?: { graph: string; startVertex: string; startIds: Int64[]; throughEdges: string[]; targetEdgeNodeName: string; isReturnEdge?: number; count?: number; stepLayerLimit?: number; filterMap: { [k: string]: ReadFilterSet_Req; }; targetFilter: ReadFilterSet_Req; });
  }

declare class ReadRegulerQuery_Req {
    public graph: string;
    public startVertex: string;
    public startIds: Int64[];
    public throughEdges: string[];
    public targetEdgeNodeName: string;
    public isReturnEdge?: number;
    public count?: number;
    public filterVector: ReadFilterSet_Req[];

      constructor(args?: { graph: string; startVertex: string; startIds: Int64[]; throughEdges: string[]; targetEdgeNodeName: string; isReturnEdge?: number; count?: number; filterVector: ReadFilterSet_Req[]; });
  }

declare class ReadRegulerQuerySort_Req {
    public graph: string;
    public startVertex: string;
    public startIds: Int64[];
    public throughEdges: string[];
    public targetEdgeNodeName: string;
    public isReturnEdge?: number;
    public count?: number;
    public filterVector: ReadFilterSet_Req[];
    public sortAttrs: string[];

      constructor(args?: { graph: string; startVertex: string; startIds: Int64[]; throughEdges: string[]; targetEdgeNodeName: string; isReturnEdge?: number; count?: number; filterVector: ReadFilterSet_Req[]; sortAttrs: string[]; });
  }

declare class StepGroupQuery_Req {
    public graph: string;
    public startVertex: string;
    public startIds: Int64[];
    public throughEdges: string[];
    public filterVector: ReadFilterSet_Req[];
    public edgeAttrGroup: { [k: number]: string[]; };
    public vertexAttrGroup: { [k: number]: string[]; };
    public sortFlag?: number;
    public sortTopN?: number;

      constructor(args?: { graph: string; startVertex: string; startIds: Int64[]; throughEdges: string[]; filterVector: ReadFilterSet_Req[]; edgeAttrGroup: { [k: number]: string[]; }; vertexAttrGroup: { [k: number]: string[]; }; sortFlag?: number; sortTopN?: number; });
  }

declare class SimilarFREdgeQuery_Req {
    public graph: string;
    public edge: string;
    public startId: Int64;
    public startDirect: number;
    public scoreAttrName: string;
    public topn: number;
    public filterStep1: ReadFilterSet_Req;
    public filterStep2: ReadFilterSet_Req;

      constructor(args?: { graph: string; edge: string; startId: Int64; startDirect: number; scoreAttrName: string; topn: number; filterStep1: ReadFilterSet_Req; filterStep2: ReadFilterSet_Req; });
  }

declare class SimilarMutualQuery_Req {
    public graph: string;
    public edge1: string;
    public edge2: string;
    public startId: Int64;
    public topn: number;
    public multiFlag: number;
    public filterStep1: ReadFilterSet_Req;
    public filterStep2: ReadFilterSet_Req;

      constructor(args?: { graph: string; edge1: string; edge2: string; startId: Int64; topn: number; multiFlag: number; filterStep1: ReadFilterSet_Req; filterStep2: ReadFilterSet_Req; });
  }

declare class RecommendQuery_Req {
    public graph: string;
    public startId: Int64;
    public throughEdges: string[];
    public filterVector: ReadFilterSet_Req[];
    public corelationFlag: number;
    public multiFlag: number;
    public minCount: number;
    public minInDegree: number;
    public topn: number;

      constructor(args?: { graph: string; startId: Int64; throughEdges: string[]; filterVector: ReadFilterSet_Req[]; corelationFlag: number; multiFlag: number; minCount: number; minInDegree: number; topn: number; });
  }

declare class PathQuery_Req {
    public graph: string;
    public startId: Int64;
    public destId: Int64;
    public throughEdges: string[];
    public filterVector: ReadFilterSet_Req[];
    public topn: number;

      constructor(args?: { graph: string; startId: Int64; destId: Int64; throughEdges: string[]; filterVector: ReadFilterSet_Req[]; topn: number; });
  }

declare class SimplePageQuery_Req {
    public graph: string;
    public veName: string;
    public vertexFlag: boolean;
    public filter: ReadFilterSet_Req;
    public pageSize: number;
    public scrollId?: Int64;

      constructor(args?: { graph: string; veName: string; vertexFlag: boolean; filter: ReadFilterSet_Req; pageSize: number; scrollId?: Int64; });
  }

declare class SimplePageQuerySort_Req {
    public graph: string;
    public veName: string;
    public vertexFlag: boolean;
    public filter: ReadFilterSet_Req;
    public pageSize: number;
    public scrollId?: Int64;
    public sortAttrs: string[];

      constructor(args?: { graph: string; veName: string; vertexFlag: boolean; filter: ReadFilterSet_Req; pageSize: number; scrollId?: Int64; sortAttrs: string[]; });
  }

declare class SimpleGroupQuery_Req {
    public graph: string;
    public veName: string;
    public vertexFlag: boolean;
    public filter: ReadFilterSet_Req;
    public attrGroup: string[];
    public sortFlag?: number;
    public sortTopN?: number;

      constructor(args?: { graph: string; veName: string; vertexFlag: boolean; filter: ReadFilterSet_Req; attrGroup: string[]; sortFlag?: number; sortTopN?: number; });
  }

declare class CompondStepQuery_Req {
    public startQuery: SimplePageQuery_Req;
    public stepQuery: ReadRegulerQuery_Req;

      constructor(args?: { startQuery: SimplePageQuery_Req; stepQuery: ReadRegulerQuery_Req; });
  }

declare class CompondGroupQuery_Req {
    public startQuery: SimpleGroupQuery_Req;
    public stepQuery: StepGroupQuery_Req;

      constructor(args?: { startQuery: SimpleGroupQuery_Req; stepQuery: StepGroupQuery_Req; });
  }

declare class TagJob4WanderWorkRestrain {
    public restrains: ReadRegulerQuery_Req[];
    public restrainLogic: number;

      constructor(args?: { restrains: ReadRegulerQuery_Req[]; restrainLogic: number; });
  }

declare class TagJob4WanderWork_Req {
    public paths: RecommendQuery_Req[];
    public jobid: Int64;
    public resultEdge: string;
    public logic: number;
    public factEdge: string;
    public restrainMap: any;
    public ratio: number;
    public single: number;
    public threshold: number;
    public brandId: number;
    public userBrandEdgeName: string;

      constructor(args?: { paths: RecommendQuery_Req[]; jobid: Int64; resultEdge: string; logic: number; factEdge: string; restrainMap: any; ratio: number; single: number; threshold: number; brandId: number; userBrandEdgeName: string; });
  }

declare class TagJob4Custom1_Req {
    public paths: RecommendQuery_Req[];
    public jobid: Int64;
    public resultEdge: string;
    public brandMap: any;
    public brandAttrName: string;
    public ratio: number;
    public single: number;
    public brandId: number;
    public userBrandEdgeName: string;
    public brandEdgeFlag?: number;
    public brandStep?: number;

      constructor(args?: { paths: RecommendQuery_Req[]; jobid: Int64; resultEdge: string; brandMap: any; brandAttrName: string; ratio: number; single: number; brandId: number; userBrandEdgeName: string; brandEdgeFlag?: number; brandStep?: number; });
  }

declare class ReadRegulerJobQuery_Req {
    public graph: string;
    public startIds: Int64[];
    public throughEdges: string[];
    public edgeAttrGroup: { [k: number]: string[]; };
    public vertexAttrGroup: { [k: number]: string[]; };
    public filterVector: ReadFilterSet_Req[];
    public expVertexAttrGroup: { [k: string]: string[]; };
    public jobid: Int64;

      constructor(args?: { graph: string; startIds: Int64[]; throughEdges: string[]; edgeAttrGroup: { [k: number]: string[]; }; vertexAttrGroup: { [k: number]: string[]; }; filterVector: ReadFilterSet_Req[]; expVertexAttrGroup: { [k: string]: string[]; }; jobid: Int64; });
  }

declare class ReadRegulerJob_BranchEdge_Req {
    public edgename: string;
    public filter: ReadFilterSet_Req;
    public edgeAttrs: string[];
    public vertexAttrs: string[];

      constructor(args?: { edgename: string; filter: ReadFilterSet_Req; edgeAttrs: string[]; vertexAttrs: string[]; });
  }

declare class ReadRegulerJob_BranchPath_Req {
    public path: ReadRegulerJob_BranchEdge_Req[];
    public type: number;

      constructor(args?: { path: ReadRegulerJob_BranchEdge_Req[]; type: number; });
  }

declare class ReadRegulerJob_MainStep_Req {
    public mainStep: ReadRegulerJob_BranchEdge_Req;
    public branchPaths: ReadRegulerJob_BranchPath_Req[];

      constructor(args?: { mainStep: ReadRegulerJob_BranchEdge_Req; branchPaths: ReadRegulerJob_BranchPath_Req[]; });
  }

declare class ReadRegulerJobQueryV2_Req {
    public graph: string;
    public startIds: Int64[];
    public jobid: Int64;
    public paths: ReadRegulerJob_MainStep_Req[];
    public vertexAttrs: string[];

      constructor(args?: { graph: string; startIds: Int64[]; jobid: Int64; paths: ReadRegulerJob_MainStep_Req[]; vertexAttrs: string[]; });
  }

declare class TagJob4Custom2PathThreshold_Req {
    public filterType?: number;
    public upper: number;
    public lower: number;

      constructor(args?: { filterType?: number; upper: number; lower: number; });
  }

declare class TagJob4Custom2PathThresholdSet_Req {
    public thresholds: TagJob4Custom2PathThreshold_Req[];
    public relationType: number;

      constructor(args?: { thresholds: TagJob4Custom2PathThreshold_Req[]; relationType: number; });
  }

declare class TagJob4Custom2PathFilter_Req {
    public filterType?: number;
    public fillType?: number;
    public attrName: string;
    public upper: string;
    public lower: string;

      constructor(args?: { filterType?: number; fillType?: number; attrName: string; upper: string; lower: string; });
  }

declare class TagJob4Custom2PathFilterSet_Req {
    public filters: TagJob4Custom2PathFilter_Req[];
    public relationType: number;

      constructor(args?: { filters: TagJob4Custom2PathFilter_Req[]; relationType: number; });
  }

declare class TagJob4Custom2Path_Req {
    public throughEdges: string[];
    public filters_up: ReadFilterSet_Req[];
    public filters_down: ReadFilterSet_Req[];
    public filterLogic_up: number;
    public filterLogic_down: number;
    public calcType: number;
    public statisType_up: number;
    public statisType_down: number;
    public threshold: TagJob4Custom2PathThreshold_Req;
    public filters_spec_edge_up: TagJob4Custom2PathFilterSet_Req[];
    public filters_spec_edge_down: TagJob4Custom2PathFilterSet_Req[];
    public filters_spec_node_up: TagJob4Custom2PathFilterSet_Req[];
    public filters_spec_node_down: TagJob4Custom2PathFilterSet_Req[];

      constructor(args?: { throughEdges: string[]; filters_up: ReadFilterSet_Req[]; filters_down: ReadFilterSet_Req[]; filterLogic_up: number; filterLogic_down: number; calcType: number; statisType_up: number; statisType_down: number; threshold: TagJob4Custom2PathThreshold_Req; filters_spec_edge_up: TagJob4Custom2PathFilterSet_Req[]; filters_spec_edge_down: TagJob4Custom2PathFilterSet_Req[]; filters_spec_node_up: TagJob4Custom2PathFilterSet_Req[]; filters_spec_node_down: TagJob4Custom2PathFilterSet_Req[]; });
  }

declare class TagJob4Custom2_Req {
    public graph: string;
    public ratio: number;
    public single: number;
    public paths: TagJob4Custom2Path_Req[];
    public thresholdLogic: number;
    public thresholdSets: TagJob4Custom2PathThresholdSet_Req[];
    public jobid: Int64;
    public threadCnt: number;
    public singleTagIdList: Int64[];
    public resultEdge: string;
    public brandId: number;
    public userBrandEdgeName: string;

      constructor(args?: { graph: string; ratio: number; single: number; paths: TagJob4Custom2Path_Req[]; thresholdLogic: number; thresholdSets: TagJob4Custom2PathThresholdSet_Req[]; jobid: Int64; threadCnt: number; singleTagIdList: Int64[]; resultEdge: string; brandId: number; userBrandEdgeName: string; });
  }

declare class TagJob4Custom4Path_Req {
    public throughEdges: string[];
    public filters: ReadFilterSet_Req[];
    public up_step: number;
    public up_isEdge: number;
    public up_attr: string;
    public down_step: number;
    public down_isEdge: number;
    public down_attr: string;

      constructor(args?: { throughEdges: string[]; filters: ReadFilterSet_Req[]; up_step: number; up_isEdge: number; up_attr: string; down_step: number; down_isEdge: number; down_attr: string; });
  }

declare class TagJob4Custom4_Req {
    public graph: string;
    public ratio: number;
    public paths: TagJob4Custom4Path_Req[];
    public jobid: Int64;

      constructor(args?: { graph: string; ratio: number; paths: TagJob4Custom4Path_Req[]; jobid: Int64; });
  }

declare class TagJob4Custom3_Res {
    public pathidx: number;
    public uid: Int64;
    public tagid: Int64;
    public up: Int64;
    public down: Int64;
    public score: number;

      constructor(args?: { pathidx: number; uid: Int64; tagid: Int64; up: Int64; down: Int64; score: number; });
  }

declare class FreeBroadPath_Req {
    public throughEdges: string[];
    public edgefilters: ReadFilterSet_Req[];
    public throughVertexes: string[];
    public vertexfilters: ReadFilterSet_Req[];
    public startVertex: string;
    public startIds: Int64[];
    public steplimit?: number;
    public resultlimit?: number;
    public graph: string;

      constructor(args?: { throughEdges: string[]; edgefilters: ReadFilterSet_Req[]; throughVertexes: string[]; vertexfilters: ReadFilterSet_Req[]; startVertex: string; startIds: Int64[]; steplimit?: number; resultlimit?: number; graph: string; });
  }

declare class VertexCommunicatePath_Req {
    public throughEdges: string[];
    public edgefilters: ReadFilterSet_Req[];
    public throughVertexes: string[];
    public vertexfilters: ReadFilterSet_Req[];
    public srcid: Int64;
    public dstid: Int64;
    public graph: string;
    public resultlimit?: number;
    public calclimit?: number;
    public pathlimit?: number;
    public stepDown?: number;
    public stepUp?: number;
    public shortFlag?: number;
    public srcVertex: string;
    public dstVertex: string;

      constructor(args?: { throughEdges: string[]; edgefilters: ReadFilterSet_Req[]; throughVertexes: string[]; vertexfilters: ReadFilterSet_Req[]; srcid: Int64; dstid: Int64; graph: string; resultlimit?: number; calclimit?: number; pathlimit?: number; stepDown?: number; stepUp?: number; shortFlag?: number; srcVertex: string; dstVertex: string; });
  }

declare class VertexConnectionPath_Req {
    public throughEdges: string[];
    public edgefilters: ReadFilterSet_Req[];
    public throughVertexes: string[];
    public vertexfilters: ReadFilterSet_Req[];
    public vids: VertexID[];
    public graph: string;
    public resultlimit?: number;
    public calclimit?: number;
    public pathlimit?: number;

      constructor(args?: { throughEdges: string[]; edgefilters: ReadFilterSet_Req[]; throughVertexes: string[]; vertexfilters: ReadFilterSet_Req[]; vids: VertexID[]; graph: string; resultlimit?: number; calclimit?: number; pathlimit?: number; });
  }

declare class GraphEntityResult_Res {
    public vids: VertexPk[];
    public eids: EdgePk[];

      constructor(args?: { vids: VertexPk[]; eids: EdgePk[]; });
  }

declare class PathResultBean_Res {
    public edge_name: string;
    public dst_vname: string;
    public eid: Int64;
    public dst_vid: Int64;
    public dst_vpk: string;

      constructor(args?: { edge_name: string; dst_vname: string; eid: Int64; dst_vid: Int64; dst_vpk: string; });
  }

/**
 * type : 0-属性值等值聚合 1-属性值分段聚合(左闭右开)
 */
declare class PathCalcJobQuery_Agg_Field_Req {
    public type: number;
    public name: string;
    public interval_vals: IntervalPair[];

      constructor(args?: { type: number; name: string; interval_vals: IntervalPair[]; });
  }

/**
 * type : 0-不聚合 1-属性聚合 2-终点聚合
 */
declare class PathCalcJobQuery_Agg_Req {
    public type: number;
    public edgeAttrGroup: { [k: number]: PathCalcJobQuery_Agg_Field_Req[]; };
    public vertexAttrGroup: { [k: number]: PathCalcJobQuery_Agg_Field_Req[]; };

      constructor(args?: { type: number; edgeAttrGroup: { [k: number]: PathCalcJobQuery_Agg_Field_Req[]; }; vertexAttrGroup: { [k: number]: PathCalcJobQuery_Agg_Field_Req[]; }; });
  }

/**
 * start_type 1-根据startIds指定点起始 2-根据startVertexFilter的过滤条件从概念出发 3-通过jobid的计算结果出发
 */
declare class PathCalcJobQuery_Path_Req {
    public startIds: Int64[];
    public startVertexFilter: ReadFilterSet_Req;
    public jobid: Int64;
    public start_type: number;
    public throughEdges: string[];
    public filterVector: ReadFilterSet_Req[];
    public start_vertex: string;
    public agginfo: PathCalcJobQuery_Agg_Req;

      constructor(args?: { startIds: Int64[]; startVertexFilter: ReadFilterSet_Req; jobid: Int64; start_type: number; throughEdges: string[]; filterVector: ReadFilterSet_Req[]; start_vertex: string; agginfo: PathCalcJobQuery_Agg_Req; });
  }

/**
 * type : 1-count 2-distinct 3-numberic(sum max min) 4-Intersection 5-Union 6-Complement
 * list_type : 0-不保存清单 1-保存终点 2-保存终边 3-保存指定属性值
 * 当type=3的时候，需设置type_step:要计算的属性在第几步、type_edge:要计算的属性是否是边、type_attrname:要计算的属性的名、type_pathidx:指标属性在第几条路径称
 * multi_type, 1代表多路径串行计算：点集，2代表多路径串行计算：边集
 */
declare class PathCalcJobQuery_Calc_Req {
    public type: number;
    public type_step: number;
    public type_edge: number;
    public type_pathidx: number;
    public type_attrname: string;
    public list_type: number;
    public multi_type: number;
    public edgeAttrGroup: { [k: number]: string[]; };
    public vertexAttrGroup: { [k: number]: string[]; };

      constructor(args?: { type: number; type_step: number; type_edge: number; type_pathidx: number; type_attrname: string; list_type: number; multi_type: number; edgeAttrGroup: { [k: number]: string[]; }; vertexAttrGroup: { [k: number]: string[]; }; });
  }

/**
 * jobid : 0表示同步
 */
declare class PathCalcJobQuery_Req {
    public graph: string;
    public pathinfos: PathCalcJobQuery_Path_Req[];
    public calcinfo: PathCalcJobQuery_Calc_Req;
    public agginfo: PathCalcJobQuery_Agg_Req;
    public jobid: Int64;
    public threadCnt?: number;
    public start_vertex: string;

      constructor(args?: { graph: string; pathinfos: PathCalcJobQuery_Path_Req[]; calcinfo: PathCalcJobQuery_Calc_Req; agginfo: PathCalcJobQuery_Agg_Req; jobid: Int64; threadCnt?: number; start_vertex: string; });
  }

declare class MixDataStruct_Res {
    public dataType: DataTypeEnum;
    public str: string;
    public num: Int64;
    public dbl: number;

      constructor(args?: { dataType: DataTypeEnum; str: string; num: Int64; dbl: number; });
  }

declare class StepGroupObject_Res {
    public attrs: MixDataStruct_Res[];
    public val: Int64;

      constructor(args?: { attrs: MixDataStruct_Res[]; val: Int64; });
  }

declare class KeyValuePair_Res {
    public kid: Int64;
    public val: number;

      constructor(args?: { kid: Int64; val: number; });
  }

declare class PageList_Res {
    public ids: Int64[];
    public scrollId: Int64;

      constructor(args?: { ids: Int64[]; scrollId: Int64; });
  }

declare class TagJobSample_Res {
    public total: Int64;
    public sample: Int64;
    public result: Int64;
    public tagMap: any;
    public state: number;
    public brand_total_map: { [k: number]: number; };
    public brand_result_map: { [k: number]: number; };
    public brand_tag_map: { [k: number]: any; };

      constructor(args?: { total: Int64; sample: Int64; result: Int64; tagMap: any; state: number; brand_total_map: { [k: number]: number; }; brand_result_map: { [k: number]: number; }; brand_tag_map: { [k: number]: any; }; });
  }

declare class PathCalcJob_Num_Res {
    public max: Int64;
    public min: Int64;
    public sum: Int64;
    public max_d: number;
    public min_d: number;
    public sum_d: number;
    public count: Int64;
    public distinct_count: Int64;

      constructor(args?: { max: Int64; min: Int64; sum: Int64; max_d: number; min_d: number; sum_d: number; count: Int64; distinct_count: Int64; });
  }

declare class PathCalcJob_Agg_Res {
    public groups: string[];
    public func_val: PathCalcJob_Num_Res;

      constructor(args?: { groups: string[]; func_val: PathCalcJob_Num_Res; });
  }

/**
 * num_type : 1-整数 2-浮点数
 */
declare class PathCalcJob_Res {
    public calc_type: number;
    public num_type: number;
    public func_val: PathCalcJob_Num_Res;
    public agg_val: PathCalcJob_Agg_Res[];
    public offset: Int64;

      constructor(args?: { calc_type: number; num_type: number; func_val: PathCalcJob_Num_Res; agg_val: PathCalcJob_Agg_Res[]; offset: Int64; });
  }

declare class SameTargetQuery_Req {
    public graph: string;
    public throughEdges: string[];
    public vids: Int64[];
    public filterVector: ReadFilterSet_Req[];
    public resSort?: number;
    public threadCnt?: number;
    public resLimit?: number;

      constructor(args?: { graph: string; throughEdges: string[]; vids: Int64[]; filterVector: ReadFilterSet_Req[]; resSort?: number; threadCnt?: number; resLimit?: number; });
  }

/**
 * Thrift also lets you define constants for use across languages. Complex
 * types and structs are specified using JSON notation.
 */
declare const INT32CONSTANT: number;

declare const MAPCONSTANT: { [k: string]: string; };
