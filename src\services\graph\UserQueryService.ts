// 用户查询服务 - 实现用户名查询、最大深度查询和相似用户推荐

import graphService from './index';

// 查询结果类型定义
export interface UserInfo {
  id: number;
  name: string;
  properties: Record<string, any>;
}

export interface SimilarUser {
  id: number;
  score: number;
  properties?: Record<string, any>;
}

export interface PathResult {
  path: number[];
  depth: number;
  edges: number[];
}

export interface QueryFilter {
  field: string;
  operator: 'eq' | 'ne' | 'gt' | 'gte' | 'lt' | 'lte' | 'contains';
  value: any;
}

export class UserQueryService {
  private graphName: string;
  private userVertexType: string;
  private relationEdgeType: string;

  constructor(graphName: string, userVertexType: string = 'User', relationEdgeType: string = 'Relation') {
    this.graphName = graphName;
    this.userVertexType = userVertexType;
    this.relationEdgeType = relationEdgeType;
  }

  /**
   * 根据用户名查询用户信息
   * @param username 用户名
   * @returns 用户信息列表
   */
  async queryUserByName(username: string): Promise<UserInfo[]> {
    try {
      // 构建过滤条件查询用户
      const filterQuery = {
        graph: this.graphName,
        startVertex: this.userVertexType,
        startIds: [],
        throughEdges: [],
        targetEdgeNodeName: '',
        isReturnEdge: 0,
        count: 100,
        stepLayerLimit: 0,
        filterMap: {
          [this.userVertexType]: {
            filters: [{
              filterType: 0, // 等值过滤
              nodeName: this.userVertexType,
              attrName: 'name',
              upper: username,
              lower: username,
              targetFilter: null
            }],
            relationType: 0 // AND关系
          }
        },
        targetFilter: null
      };

      const userIds = await graphService.filterQuery(filterQuery, 0);

      if (!userIds || userIds.length === 0) {
        return [];
      }

      // 获取用户详细信息
      const userInfos: UserInfo[] = [];
      for (const userId of userIds) {
        const userInfo = await graphService.getVertexInfoById(
          this.graphName,
          this.userVertexType,
          userId
        );
        
        if (userInfo) {
          const userData = JSON.parse(userInfo);
          userInfos.push({
            id: userId,
            name: userData.name || username,
            properties: userData
          });
        }
      }

      return userInfos;
    } catch (error) {
      console.error('查询用户失败:', error);
      throw new Error(`查询用户失败: ${error}`);
    }
  }

  /**
   * 查询指定用户的最大深度路径
   * @param startUserId 起始用户ID
   * @param targetUserId 目标用户ID（可选，如果不指定则查询从起始用户出发的最大深度）
   * @param maxDepth 最大深度限制
   * @returns 路径结果
   */
  async queryMaxDepthPath(
    startUserId: number, 
    targetUserId?: number, 
    maxDepth: number = 6
  ): Promise<PathResult[]> {
    try {
      if (targetUserId) {
        // 查询两个用户之间的路径
        const pathQuery = {
          graph: this.graphName,
          startId: startUserId,
          destId: targetUserId,
          throughEdges: [this.relationEdgeType],
          filterVector: [],
          topn: 10
        };

        const paths = await graphService.pathFindCalc(pathQuery);

        return paths.map((path: number[], index: number) => ({
          path: path,
          depth: path.length - 1,
          edges: [] // 边信息需要额外查询
        }));
      } else {
        // 查询从起始用户出发的广度优先遍历
        const broadQuery = {
          graph: this.graphName,
          startVertex: this.userVertexType,
          startIds: [startUserId],
          throughEdges: [this.relationEdgeType],
          targetEdgeNodeName: this.userVertexType,
          isReturnEdge: 1,
          count: 1000,
          stepLayerLimit: maxDepth,
          filterMap: {},
          targetFilter: null
        };

        const result = await graphService.filterQuery(broadQuery, 0);
        
        // 转换结果格式
        const paths: PathResult[] = [];
        if (result && result.length > 0) {
          for (const userId of result) {
            paths.push({
              path: [startUserId, userId],
              depth: 1, // 简化处理，实际深度需要通过路径计算
              edges: []
            });
          }
        }

        return paths;
      }
    } catch (error) {
      console.error('查询最大深度路径失败:', error);
      throw new Error(`查询最大深度路径失败: ${error}`);
    }
  }

  /**
   * 相似用户推荐
   * @param userId 用户ID
   * @param topN 返回前N个相似用户
   * @param minCommonConnections 最少共同连接数
   * @returns 相似用户列表
   */
  async recommendSimilarUsers(
    userId: number, 
    topN: number = 10, 
    minCommonConnections: number = 2
  ): Promise<SimilarUser[]> {
    try {
      // 使用推荐算法查询相似用户
      const recommendQuery = {
        graph: this.graphName,
        startId: userId,
        throughEdges: [this.relationEdgeType],
        filterVector: [],
        corelationFlag: 1, // 启用相关性计算
        multiFlag: 1,      // 多路径计算
        minCount: minCommonConnections,
        minInDegree: 1,
        topn: topN
      };

      const recommendations = await graphService.recommendCalc(recommendQuery);

      const similarUsers: SimilarUser[] = [];

      for (const rec of recommendations) {
        // rec 应该是 KeyValuePair_Res 类型，包含 kid (用户ID) 和 val (相似度分数)
        const userInfo = await graphService.getVertexInfoById(
          this.graphName,
          this.userVertexType,
          rec.kid
        );
        
        let properties = {};
        if (userInfo) {
          try {
            properties = JSON.parse(userInfo);
          } catch (e) {
            console.warn('解析用户信息失败:', e);
          }
        }

        similarUsers.push({
          id: rec.kid,
          score: rec.val,
          properties: properties
        });
      }

      return similarUsers.sort((a, b) => b.score - a.score);
    } catch (error) {
      console.error('推荐相似用户失败:', error);
      throw new Error(`推荐相似用户失败: ${error}`);
    }
  }

  /**
   * 基于共同好友的相似用户推荐
   * @param userId 用户ID
   * @param topN 返回前N个相似用户
   * @returns 相似用户列表
   */
  async recommendByMutualFriends(userId: number, topN: number = 10): Promise<SimilarUser[]> {
    try {
      // 使用互相关联查询
      const mutualQuery = {
        graph: this.graphName,
        edge1: this.relationEdgeType,
        edge2: this.relationEdgeType,
        startId: userId,
        topn: topN,
        multiFlag: 1,
        filterStep1: {
          filters: [],
          relationType: 0
        },
        filterStep2: {
          filters: [],
          relationType: 0
        }
      };

      const mutualResults = await graphService.similarMutualCalc(mutualQuery);

      const similarUsers: SimilarUser[] = [];

      for (const result of mutualResults) {
        const userInfo = await graphService.getVertexInfoById(
          this.graphName,
          this.userVertexType,
          result.kid
        );
        
        let properties = {};
        if (userInfo) {
          try {
            properties = JSON.parse(userInfo);
          } catch (e) {
            console.warn('解析用户信息失败:', e);
          }
        }

        similarUsers.push({
          id: result.kid,
          score: result.val,
          properties: properties
        });
      }

      return similarUsers.sort((a, b) => b.score - a.score);
    } catch (error) {
      console.error('基于共同好友推荐失败:', error);
      throw new Error(`基于共同好友推荐失败: ${error}`);
    }
  }

  /**
   * 复合查询：根据用户名查找用户并推荐相似用户
   * @param username 用户名
   * @param topN 推荐数量
   * @returns 用户信息和推荐列表
   */
  async queryUserAndRecommend(username: string, topN: number = 10) {
    try {
      // 先查询用户
      const users = await this.queryUserByName(username);
      
      if (users.length === 0) {
        throw new Error(`未找到用户: ${username}`);
      }

      const user = users[0]; // 取第一个匹配的用户
      
      // 推荐相似用户
      const recommendations = await this.recommendSimilarUsers(user.id, topN);
      
      return {
        user: user,
        recommendations: recommendations,
        totalRecommendations: recommendations.length
      };
    } catch (error) {
      console.error('复合查询失败:', error);
      throw new Error(`复合查询失败: ${error}`);
    }
  }
}

// 创建默认实例
export const userQueryService = new UserQueryService('default');

// 工厂函数
export const createUserQueryService = (
  graphName: string, 
  userVertexType?: string, 
  relationEdgeType?: string
) => {
  return new UserQueryService(graphName, userVertexType, relationEdgeType);
};
